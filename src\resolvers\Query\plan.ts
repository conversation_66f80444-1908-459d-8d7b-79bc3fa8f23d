import { PrismaClient } from "@prisma/client";
import { Context } from "../../context";
import { protect } from "../../middleware/auth";
import { CreditHistory, CreditHistoryPayload, CreditRequestsPayload, plan, PlansPayload } from "../../types/plan.type";
import { decode } from "../../utils/crypto";
import { CreditApprovalStatus } from "../../utils/constants";

export const planQuery = {
  plans: async (
		parent: unknown,
		args: unknown,
		{ prisma, req }: Context,
	): Promise<PlansPayload> => {
		await protect(req);
    try {
      const getAllPlans = await prisma.plan.findMany();
      // getAllPlans.map((plan) => plan.slapPrice = (+plan.slapPrice + ((+plan.slapPrice * +plan.gst)/100)))
      console.log("===getAllPlans==", getAllPlans);
      return {
        plansErrors: [],
        message: "Successfully getting all plans",
        status: 200,
        plans: getAllPlans
      }
    } catch (error) {
      console.log("===Error get all plans", error);
      return {
        plansErrors: [
          {
            message: 'Unknown error getting plans',
          },
        ],
        message: null,
        status: 500,
        plans: []
      }
    }
	},

  getCreditRequests: async (
		parent: unknown,
		args: unknown,
		{ prisma, req }: Context,
	) => {
		await protect(req);
    try {
      const getCreditRequestList = await prisma.recruitercreditrequest.findMany({
        include: {
          recruiter: true,
          crmUser: true,
          creditRequestApproveStatus: true,
          creditRequestType: true,
          currentPlanTransaction: true
        }
      });
      console.log("===getCreditRequestList==", getCreditRequestList);
      if(getCreditRequestList && getCreditRequestList.length > 0) {
        for(const i of getCreditRequestList){
          try {
            if (i.recruiter && i.recruiter.mobileNumber) {
              i.recruiter.mobileNumber = await decode(i.recruiter.mobileNumber);
            }
          } catch (error) {
            console.log("Failed to decode mobileNumber:", error);
          }
          try {
            if (i.recruiter && i.recruiter.email) {
              i.recruiter.email = await decode(i.recruiter.email);
            }
          } catch (error) {
            console.log("Failed to decode email:", error);
          }
        }
        return {
          getCreditRequestsErrors: [],
          message: "Successfully getting all plans",
          status: 200,
          creditRequests: getCreditRequestList
        }
      } else {
        return {
          getCreditRequestsErrors: [],
          message: "No request is currently available",
          status: 404,
          creditRequests: []
        }
      }

    } catch (error) {
      console.log("===Error getCreditRequestList", error);
      return {
        getCreditRequestsErrors: [],
        message: "Unknown error getting credit request",
        status: 500,
        creditRequests: []
      }
    }
	},

  recruiterCreditHistory: async (
    parent: unknown,
    args: any,
    { prisma, req }: Context,
  ): Promise<CreditHistoryPayload> => {
    await protect(req);
    try {
      const recruiterHistory = await prisma.recruitercreditrequest.findMany({
        where: {
          recruiterId: +args.recruiterId,
          creditRequestApproveStatusId: {
            in: [CreditApprovalStatus.ACCEPTED, CreditApprovalStatus.REJECTED]
          }
        },
        include: {
          creditRequestApproveStatus: true       
        },
      });
      if(recruiterHistory&&recruiterHistory[0].recruiterId){
        const recruiter = await prisma.recruiter.findUnique({
          where: {
            id: +recruiterHistory[0].recruiterId,
          },
          select: {
            id:true,
            name: true,
            mobileNumber: true,
            email: true,
            company: true,
            recruiterlatestplaninfo:{
              select: {
                currentJobCredits: true,
              },
            },
          },
        });
      if (recruiter) {
        try {
          if (recruiter && recruiter.mobileNumber) {
           recruiter.mobileNumber = await decode(recruiter.mobileNumber);
          }
        } catch (error) {
          console.log("Failed to decode mobileNumber:", error);
        }
        try {
          if (recruiter && recruiter.email) {
            recruiter.email = await decode(recruiter.email);
          }
        } catch (error) {
          console.log("Failed to decode email:", error);
        }
        const recruiterCreditHistory={
          ...recruiter,
          creditHistory:recruiterHistory
         }
        return {
          creditHistoryErrors: [],
          message: "Recruiter's credit history retrieved successfully",
          status: 200,
          recruiterCreditHistory: recruiterCreditHistory
        };
      } else {
        return {
          creditHistoryErrors: [],
          message: "No credit history found",
          status: 404,
          recruiterCreditHistory: null,
        };
      }
    } else{
      return {
        creditHistoryErrors: [],
        message: "No recruiter found",
        status: 404,
        recruiterCreditHistory: null,
      };
    }
    } catch (error) {
      console.log("===Error recruiter's credit history", error);
      return {
        creditHistoryErrors: [{ message: "Unknown error getting in credit history" }],
        message: "Unknown error getting in credit history",
        status: 500,
        recruiterCreditHistory:null,
      };
    }
  }
}