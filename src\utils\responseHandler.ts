import { ResponsePayload } from "../types/responseHandler";

export const responseHandler = <ErrorName extends string, AdditionalData = Record<string, any>>(
    errorName: ErrorName,
    message: string,
    status: number,
    additionalData?: AdditionalData

  ): ResponsePayload<ErrorName , AdditionalData> => {
    const isSuccess = status === 200 || status === 201 || message === 'No User Found';
    const errorObject = { message };
    return {
      [errorName]: isSuccess ? [] : [errorObject],
      message: isSuccess ? message : null,
      status,
      ...additionalData,
    } as ResponsePayload<ErrorName , AdditionalData>; // Use type assertion here
  };
