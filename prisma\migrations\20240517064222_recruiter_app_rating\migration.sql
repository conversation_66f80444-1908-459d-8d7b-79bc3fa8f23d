-- AlterTable
ALTER TABLE `recruiter` ADD COLUMN `appRating` INTEGER NULL DEFAULT 0;

-- CreateTable
CREATE TABLE `recruiterappfeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VA<PERSON><PERSON><PERSON>(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterappfeedbackmapping` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `recruiterappFeedbackId` INTEGER NOT NULL,
    `recruiterappFeedbackOtherReason` VARCHAR(255) NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `recruiterappfeedbackmapping_recruiterId_key`(`recruiterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiterappfeedbackmapping` ADD CONSTRAINT `recruiterappfeedbackmapping_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterappfeedbackmapping` ADD CONSTRAINT `recruiterappfeedbackmapping_recruiterappFeedbackId_fkey` FOREIGN KEY (`recruiterappFeedbackId`) REFERENCES `recruiterappfeedback`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
