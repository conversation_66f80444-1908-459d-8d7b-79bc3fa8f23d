import { createClient } from "redis";

class RedisServer {
  async redisConnect() {
    const redisClient = await createClient();
    redisClient.connect();
    redisClient.on("error", (err) => {
      console.log("Error " + err);
    });

    return redisClient;
  }

  async setData(data: string) {
    const redisClient = this.redisConnect();
    (await redisClient).set("qualifications", data);
    
  }

  async getData(
    redisCall: (error: Error | null, result: string | null) => void
  ) {
    const redisClient = this.redisConnect();
    const response = (await redisClient).get("qualifications");

    response.then(function (result: string | null) {
      redisCall(null, result);
    });
    console.log("------ about to quite-----");
    (await redisClient).quit();
  }
}

export default RedisServer;
