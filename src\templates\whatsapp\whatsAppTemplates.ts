import { RecDetails } from "../../types/commonInterFun.type";

type TemplateParams = {
  name?: string;
  languageCode?: string;
  text?: string;
};


export const getTemplateDetails = function (recDetails: RecDetails, templateName: string, params: TemplateParams = {}) {
  let template: any;
  // Select the template based on templateName
  if (recDetails) {
    switch (templateName) {
      case "registrations_incomplete":  //OTP verified but registration not done
        template = {
          name: "registrations_incomplete",
          language: {
            policy: "deterministic",
            code: "en"
          },
          components: [
            {
              type: "body",
              parameters: []
            }
          ]
        };
        break;
      case "verification_pending": //recruiter onboarding completion 
        template = {
          name: "verification_pending",
          language: {
            policy: "deterministic",
            code: "en"
          },
          components: [
            {
              type: "body",
              parameters: [
                {
                  type: "text",
                  text: `${recDetails.name}`
                }
              ]
            }
          ]
        };
        break;
      case "profile_verified":  //crm verification completion 
        template = {
          name: "profile_verified",
          language: {
            policy: "deterministic",
            code: "en"
          },
          components: [
            {
              type: "body",
              parameters: [
                {
                  type: "text",
                  text: `${recDetails.name}`
                }
              ]
            }
          ]
        };
        break;
      case "job_posting_incomplete":   //start posting jobs(after crm verification)
        template = {
          name: "job_posting_incomplete",
          language: {
            policy: "deterministic",
            code: "en"
          },
          components: [
            {
              type: "body",
              parameters: [
                {
                  type: "text",
                  text: `${recDetails.name}`
                }
              ]
            }
          ]
        };
        break;
        case "jobpost_successful":  // after job posting verified in CRM
        template = {
          name: "jobpost_successful",
          language: {
            policy: "deterministic",
            code: "en"
          },
          components: [
            {
              type: "body",
              parameters: [
                {
                  type: "text",
                  text: `${recDetails.name}`  
                },
                {
                  type: "text",
                  text: `${recDetails.jobTitle}`  
                },
                {
                  type: "text",
                  text: `${recDetails.companyName}`  
                }
              ]
            }
          ]
        };
        break;
      //  case "recruiter_inactivity":
      //   template = {
      //     name: "recruiter_inactivity",
      //     language: {
      //       policy: "deterministic",
      //       code: "en"
      //     },
      //     components: [
      //       {
      //         type: "body",
      //         parameters: [
      //           {
      //             type: "text",
      //             text: "Hi"
      //           }
      //         ]
      //       }
      //     ]
      //   };
      //   break;
      default:
        throw new Error(`Template ${templateName} not found`);
    }
  }

  return template;
};
