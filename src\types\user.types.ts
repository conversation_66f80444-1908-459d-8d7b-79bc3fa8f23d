// Create User
export interface CreateUserInput {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  roleId: number;
}

export interface CreateUserArgs<T> {
  input: T;
}

export interface CreateUserPayload {
  CreateUserErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}

// login User
export interface LoginUserInput {
  email: string;
  password: string;
}

export interface LoginUserArgs<T> {
  input: T;
}

export interface LoginUserPayload {
  LoginUserErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
  token: string;
}


//update password
export interface UpdatePasswordInput {
  oldPassword: string;
  newPassword: string;
}

export interface UpdatePasswordArgs<T> {
  input: T;
}

export interface UpdatePasswordPayload {
  updatePasswordErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}


// S3 Generate Signed URL
export interface uploadResumeArgs<T> {
  input: T;
}

export interface uploadResumeInput {
  path: string;
  file_format: string;
  userId: number;
}

export interface uploadResumePayload {
  uploadResumeError: {
    message: string;
  }[]
  message: string | null;
  status: number;
}



export interface UpdateRecruiterStatusArgs<T> {
  input: T;
}


export interface UpdateRecruiterStatusInput {
  recruiterId: string; 
  statusId: string;
}

interface UpdateRecruiterStatusError {
  message: string;
}

export interface UpdateRecruiterStatusPayload {
  UpdateRecruiterStatusErrors: UpdateRecruiterStatusError[];
  message?: string | null; 
  status?: number | null; 
}