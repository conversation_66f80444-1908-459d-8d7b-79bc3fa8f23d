import { PrismaClient } from "@prisma/client";
import { getRoles, getRoleByName, hasRoleName } from "../middleware/auth";

const prisma = new PrismaClient();

async function testDynamicRoles() {
  try {
    console.log("🧪 Testing Dynamic Role System");
    console.log("================================");

    // Test 1: Get all roles from database
    console.log("\n1. Getting roles from database:");
    const roles = await getRoles();
    console.log("Roles:", roles);

    // Test 2: Get role by name
    console.log("\n2. Getting role by name:");
    const adminRoleId = await getRoleByName("Admin");
    const userRoleId = await getRoleByName("User");
    console.log("Admin role ID:", adminRoleId);
    console.log("User role ID:", userRoleId);

    // Test 3: Check if roles exist in database
    console.log("\n3. Checking roles in database:");
    const allRoles = await prisma.crmuserrole.findMany({
      where: { isActive: true },
      select: { id: true, name: true, description: true }
    });
    console.log("Database roles:", allRoles);

    // Test 4: Mock user role checking
    console.log("\n4. Testing role checking with mock users:");
    const mockAdminUser = { id: 1, roleId: adminRoleId };
    const mockRegularUser = { id: 2, roleId: userRoleId };

    if (adminRoleId && userRoleId) {
      const isAdminCheck = await hasRoleName(mockAdminUser, "Admin");
      const isUserCheck = await hasRoleName(mockRegularUser, "User");
      
      console.log("Mock admin user has Admin role:", isAdminCheck);
      console.log("Mock regular user has User role:", isUserCheck);
    }

    console.log("\n✅ Dynamic role system is working correctly!");
    console.log("📝 Roles are now loaded from database instead of hardcoded values.");

  } catch (error) {
    console.error("❌ Error testing dynamic roles:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  testDynamicRoles();
}

export { testDynamicRoles };
