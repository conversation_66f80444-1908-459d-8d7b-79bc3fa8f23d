// import { Worker } from "bullmq";
// import { handleEmailNotification, handleSolrJob, handleWhatsappNotification } from "../utils/bullmq-supportFn";

// let worker: Worker;

// const setUpWorker = (): void => {
//   worker = new Worker("JOBS", async (job:any) => {
//     try {
//         switch (job.name) {
//           case 'solrJob':
//             await handleSolrJob(job.data);
//             break;
//           case 'sendWhatsappNotification':
//             await handleWhatsappNotification(job.data);
//             break;
//           case 'sendEmailNotification': 
//             await handleEmailNotification(job.data);
//           break;
//           default:
//             console.log("Unrecognized job name:", job.name);
//         }
//       } catch (error) {
//         console.error("Job processing error:", error);
//       }
//   });

//   worker.on("active", (job: any) => {
//     console.debug(`Processing job with id ${job.id}`);
//   });

//   worker.on("completed", (job: any, returnvalue: any) => {
//     console.debug(`Completed job with id ${job.id}`);
//   });

//   worker.on("error", (failedReason: any) => {
//     console.error(`Job encountered an error`, failedReason);
//   });
// };

// export default setUpWorker;
