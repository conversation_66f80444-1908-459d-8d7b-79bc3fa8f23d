/*
  Warnings:

  - You are about to drop the column `location` on the `ffinterview` table. All the data in the column will be lost.
  - You are about to drop the column `allowCandidateCall` on the `interview` table. All the data in the column will be lost.
  - You are about to drop the column `jobDetailId` on the `interview` table. All the data in the column will be lost.
  - You are about to drop the column `whatsAppUpdate` on the `interview` table. All the data in the column will be lost.
  - You are about to drop the column `jobDetailId` on the `recruitercandidatepreference` table. All the data in the column will be lost.
  - You are about to drop the column `fullAddress` on the `recruiterjobpostdetail` table. All the data in the column will be lost.
  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.
  - You are about to drop the column `fullAddress` on the `walkininterview` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[jobId]` on the table `interview` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[jobId]` on the table `recruitercandidatepreference` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[jobId]` on the table `recruiterjobpostdetail` will be added. If there are existing duplicate values, this will fail.
  - Made the column `interviewId` on table `ffinterview` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `jobId` to the `interview` table without a default value. This is not possible if the table is not empty.
  - Added the required column `jobId` to the `recruitercandidatepreference` table without a default value. This is not possible if the table is not empty.
  - Made the column `genderId` on table `recruitercandidatepreference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `educationId` on table `recruitercandidatepreference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `workExperienceId` on table `recruitercandidatepreference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `minExperience` on table `recruitercandidatepreference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `applicationRadiusId` on table `recruitercandidatepreference` required. This step will fail if there are existing NULL values in that column.
  - Made the column `englishKnowledgeId` on table `recruitercandidatepreference` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `jobId` to the `recruiterjobpostdetail` table without a default value. This is not possible if the table is not empty.
  - Made the column `jobCategoryId` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `latitude` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `longitude` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `workTypeId` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `jobUrgencyId` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `workingDaysId` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `shiftTypeId` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `employmentTypeId` on table `recruiterjobpostdetail` required. This step will fail if there are existing NULL values in that column.
  - Made the column `interviewId` on table `walkininterview` required. This step will fail if there are existing NULL values in that column.

*/

-- Alter Table
ALTER TABLE `FFInterview` RENAME TO `ffinterview`;
ALTER TABLE `Interview` RENAME TO `interview`;
ALTER TABLE `InterviewType` RENAME TO `interviewtype`;
ALTER TABLE `WalkinInterview` RENAME TO `walkininterview`;
ALTER TABLE `UserJobMapping` RENAME TO `userjobmapping`;
ALTER TABLE `UserJobViewMapping` RENAME TO `userjobviewmapping`;
ALTER TABLE `RecruiterJobPostDetail` RENAME TO `recruiterjobpostdetail`;
ALTER TABLE `RecruiterCandidatePreference` RENAME TO `recruitercandidatepreference`;

-- DropForeignKey
ALTER TABLE `ffinterview` DROP FOREIGN KEY `FFInterview_interviewId_fkey`;

-- DropForeignKey
ALTER TABLE `interview` DROP FOREIGN KEY `Interview_interviewTypeId_fkey`;

-- DropForeignKey
ALTER TABLE `interview` DROP FOREIGN KEY `Interview_jobDetailId_fkey`;

-- DropForeignKey
ALTER TABLE `recruitercandidatepreference` DROP FOREIGN KEY `RecruiterCandidatePreference_applicationRadiusId_fkey`;

-- DropForeignKey
ALTER TABLE `recruitercandidatepreference` DROP FOREIGN KEY `RecruiterCandidatePreference_educationId_fkey`;

-- DropForeignKey
ALTER TABLE `recruitercandidatepreference` DROP FOREIGN KEY `RecruiterCandidatePreference_englishKnowledgeId_fkey`;

-- DropForeignKey
ALTER TABLE `recruitercandidatepreference` DROP FOREIGN KEY `RecruiterCandidatePreference_genderId_fkey`;

-- DropForeignKey
ALTER TABLE `recruitercandidatepreference` DROP FOREIGN KEY `RecruiterCandidatePreference_jobDetailId_fkey`;

-- DropForeignKey
ALTER TABLE `recruitercandidatepreference` DROP FOREIGN KEY `RecruiterCandidatePreference_workExperienceId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_companyId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_depositReasonId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_employmentTypeId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_jobCategoryId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_jobUrgencyId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_shiftTypeId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_workTypeId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiterjobpostdetail` DROP FOREIGN KEY `RecruiterJobPostDetail_workingDaysId_fkey`;

-- DropForeignKey
ALTER TABLE `userjobmapping` DROP FOREIGN KEY `UserJobMapping_jobId_fkey`;

-- DropForeignKey
ALTER TABLE `userjobmapping` DROP FOREIGN KEY `UserJobMapping_userId_fkey`;

-- DropForeignKey
ALTER TABLE `userjobviewmapping` DROP FOREIGN KEY `UserJobViewMapping_jobId_fkey`;

-- DropForeignKey
ALTER TABLE `userjobviewmapping` DROP FOREIGN KEY `UserJobViewMapping_userId_fkey`;

-- DropForeignKey
ALTER TABLE `walkininterview` DROP FOREIGN KEY `WalkinInterview_interviewId_fkey`;

-- DropIndex
DROP INDEX `RecruiterJobPostDetail_jobTitle_companyName_jobDescription_j_idx` ON `recruiterjobpostdetail`;

-- AlterTable
ALTER TABLE `clientcompany` ADD COLUMN `companyListId` INTEGER NULL;

-- AlterTable
ALTER TABLE `ffinterview` DROP COLUMN `location`,
    ADD COLUMN `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    ADD COLUMN `latitude` VARCHAR(20) NOT NULL DEFAULT '',
    ADD COLUMN `longitude` VARCHAR(20) NOT NULL DEFAULT '',
    MODIFY `interviewId` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `interview` DROP COLUMN `allowCandidateCall`,
    DROP COLUMN `jobDetailId`,
    DROP COLUMN `whatsAppUpdate`,
    ADD COLUMN `jobId` INTEGER NOT NULL,
    MODIFY `hrContactNumber` VARCHAR(255) NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE `recruitercandidatepreference` DROP COLUMN `jobDetailId`,
    ADD COLUMN `jobId` INTEGER NOT NULL,
    MODIFY `genderId` INTEGER NOT NULL,
    MODIFY `educationId` INTEGER NOT NULL,
    MODIFY `workExperienceId` INTEGER NOT NULL,
    MODIFY `minExperience` INTEGER NOT NULL DEFAULT 0,
    MODIFY `applicationRadiusId` INTEGER NOT NULL,
    MODIFY `englishKnowledgeId` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `recruiterdocument` ADD COLUMN `companyDocGivenId` INTEGER NULL,
    ADD COLUMN `personalDocGivenId` INTEGER NULL;

-- AlterTable
ALTER TABLE `recruiterjobpostdetail` DROP COLUMN `fullAddress`,
    ADD COLUMN `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    ADD COLUMN `jobId` INTEGER NOT NULL,
    MODIFY `companyName` VARCHAR(255) NOT NULL,
    MODIFY `jobCategoryId` INTEGER NOT NULL,
    MODIFY `latitude` VARCHAR(191) NOT NULL DEFAULT '',
    MODIFY `longitude` VARCHAR(191) NOT NULL DEFAULT '',
    MODIFY `workTypeId` INTEGER NOT NULL,
    MODIFY `jobUrgencyId` INTEGER NOT NULL,
    MODIFY `workingDaysId` INTEGER NOT NULL,
    MODIFY `shiftTypeId` INTEGER NOT NULL,
    MODIFY `employmentTypeId` INTEGER NOT NULL;

-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- AlterTable
ALTER TABLE `walkininterview` DROP COLUMN `fullAddress`,
    ADD COLUMN `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    ADD COLUMN `latitude` VARCHAR(20) NOT NULL DEFAULT '',
    ADD COLUMN `longitude` VARCHAR(20) NOT NULL DEFAULT '',
    MODIFY `interviewId` INTEGER NOT NULL;

-- CreateTable
CREATE TABLE `verificationdocs` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(35) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `job` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `companyName` VARCHAR(255) NOT NULL,
    `recruiterId` INTEGER NOT NULL,
    `companyId` INTEGER NOT NULL,
    `companyTypeId` INTEGER NULL,
    `companyListId` INTEGER NOT NULL,
    `consultantId` INTEGER NULL,
    `jobStatusId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `interview_jobId_key` ON `interview`(`jobId`);

-- CreateIndex
CREATE UNIQUE INDEX `recruitercandidatepreference_jobId_key` ON `recruitercandidatepreference`(`jobId`);

-- CreateIndex
CREATE UNIQUE INDEX `recruiterjobpostdetail_jobId_key` ON `recruiterjobpostdetail`(`jobId`);

-- CreateIndex
CREATE FULLTEXT INDEX `recruiterjobpostdetail_jobTitle_companyName_jobDescription_j_idx` ON `recruiterjobpostdetail`(`jobTitle`, `companyName`, `jobDescription`, `jobLocation`, `buildingName`);

-- AddForeignKey
ALTER TABLE `userjobmapping` ADD CONSTRAINT `userjobmapping_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobmapping` ADD CONSTRAINT `userjobmapping_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobviewmapping` ADD CONSTRAINT `userjobviewmapping_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobviewmapping` ADD CONSTRAINT `userjobviewmapping_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_personalDocGivenId_fkey` FOREIGN KEY (`personalDocGivenId`) REFERENCES `verificationdocs`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_companyDocGivenId_fkey` FOREIGN KEY (`companyDocGivenId`) REFERENCES `verificationdocs`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_companyListId_fkey` FOREIGN KEY (`companyListId`) REFERENCES `CompanyList`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_companyId_fkey` FOREIGN KEY (`companyId`) REFERENCES `company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_companyTypeId_fkey` FOREIGN KEY (`companyTypeId`) REFERENCES `HiringFor`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_companyListId_fkey` FOREIGN KEY (`companyListId`) REFERENCES `CompanyList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_consultantId_fkey` FOREIGN KEY (`consultantId`) REFERENCES `clientcompany`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_jobStatusId_fkey` FOREIGN KEY (`jobStatusId`) REFERENCES `JobStatus`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobCategoryId_fkey` FOREIGN KEY (`jobCategoryId`) REFERENCES `JobCategory`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_workTypeId_fkey` FOREIGN KEY (`workTypeId`) REFERENCES `WorkType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobUrgencyId_fkey` FOREIGN KEY (`jobUrgencyId`) REFERENCES `JoinPreference`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_workingDaysId_fkey` FOREIGN KEY (`workingDaysId`) REFERENCES `WorkingDays`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_depositReasonId_fkey` FOREIGN KEY (`depositReasonId`) REFERENCES `DepositReason`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_companyId_fkey` FOREIGN KEY (`companyId`) REFERENCES `company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_shiftTypeId_fkey` FOREIGN KEY (`shiftTypeId`) REFERENCES `ShiftType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_employmentTypeId_fkey` FOREIGN KEY (`employmentTypeId`) REFERENCES `EmploymentType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_genderId_fkey` FOREIGN KEY (`genderId`) REFERENCES `Gender`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_educationId_fkey` FOREIGN KEY (`educationId`) REFERENCES `QualificationType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_workExperienceId_fkey` FOREIGN KEY (`workExperienceId`) REFERENCES `WorkExperience`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_applicationRadiusId_fkey` FOREIGN KEY (`applicationRadiusId`) REFERENCES `Distance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_englishKnowledgeId_fkey` FOREIGN KEY (`englishKnowledgeId`) REFERENCES `EnglishKnowledgeLevel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `interview` ADD CONSTRAINT `interview_interviewTypeId_fkey` FOREIGN KEY (`interviewTypeId`) REFERENCES `interviewtype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `interview` ADD CONSTRAINT `interview_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ffinterview` ADD CONSTRAINT `ffinterview_interviewId_fkey` FOREIGN KEY (`interviewId`) REFERENCES `interview`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `walkininterview` ADD CONSTRAINT `walkininterview_interviewId_fkey` FOREIGN KEY (`interviewId`) REFERENCES `interview`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- RenameIndex
ALTER TABLE `ffinterview` RENAME INDEX `FFInterview_interviewId_key` TO `ffinterview_interviewId_key`;

-- RenameIndex
ALTER TABLE `walkininterview` RENAME INDEX `WalkinInterview_interviewId_key` TO `walkininterview_interviewId_key`;
