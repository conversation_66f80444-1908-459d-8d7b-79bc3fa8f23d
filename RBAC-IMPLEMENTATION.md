# Role-Based Authentication Implementation

## Overview

This document describes the role-based authentication (RBAC) system implemented in the JJ CRM backend following your existing codebase patterns and standards.

## Features Implemented

### 1. **Two-Role System Only** (as requested)
- **Admin Role (ID: 1)**: Full system access including user management
- **User Role (ID: 2)**: Limited access to basic CRM operations
- **No other roles** - System explicitly rejects any other role IDs

### 2. **Database Schema Enhancements** (following your patterns)
- Enhanced `Crmuserrole` table with description field
- New `CrmPermission` table for granular permissions (28 permissions)
- New `CrmRolePermission` table for role-permission mapping
- Proper foreign key relationships with cascade delete

### 3. **Dynamic Authentication Middleware** (following your patterns)
- Enhanced `protect()` function with role information
- `requireAdmin()`, `requireRole()` functions that get roles from database
- Helper functions: `isAdmin()`, `hasRole()`, `hasPermission()`, `getRoles()`
- **No hardcoded role IDs** - all roles are fetched from seeded database

### 4. **JWT Token Enhancement** (following your extension pattern)
- Tokens include role information (roleId, roleName, email)
- Enhanced `getSignedJwtToken` extension

### 5. **GraphQL Resolvers** (following your patterns)
- Enhanced user creation with role assignment (Admin only)
- Login returns user role information
- Role assignment functionality (Admin only)
- Current user information retrieval
- Admin-only queries for user/role management

### 6. **Data Seeding** (following your `addJobToQueue` pattern)
- Permissions seeded via `crmPermissions` data
- Role-permissions seeded via `crmRolePermissions` data
- Integrated into your existing seed structure

## Dynamic Role System

### Key Features
- **No Hardcoded Role IDs**: All role checking is done dynamically from the database
- **Database-Driven**: Roles are fetched from the seeded `crmuserrole` table
- **Flexible**: Easy to add new roles by updating seed data
- **Consistent**: All middleware functions use the same dynamic role lookup

### How It Works
```typescript
// Get roles dynamically from database
const roles = await getRoles(); // Returns { ADMIN: 1, USER: 2 }

// Check if user is admin
const isUserAdmin = await isAdmin(user);

// Get role by name
const adminRoleId = await getRoleByName("Admin");

// Check role by name
const hasAdminRole = await hasRoleName(user, "Admin");
```

### Testing Dynamic Roles
```bash
npx ts-node src/scripts/test-dynamic-roles.ts
```

## Setup Instructions

### 1. Generate Prisma Client
```bash
npx prisma generate
```

### 2. Run Database Migration
```bash
npx prisma migrate dev --name add_rbac_permissions
```

### 3. Seed Database with Roles and Permissions
```bash
npx prisma db seed -- --environment=allSeed
```

### 4. Create Initial Admin User
```bash
npx ts-node src/scripts/create-admin.ts
```

## Default Admin Credentials

After running the admin creation script:
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Admin (ID: 1)

⚠️ **Important**: Change the default password after first login!

## API Usage Examples

### 1. Login (Returns role information)
```graphql
mutation {
  loginUser(input: {
    email: "<EMAIL>"
    password: "admin123"
  }) {
    message
    status
    token
    user {
      id
      name
      email
      roleId
      roleName
    }
  }
}
```

### 2. Create User (Admin Only) - Only Admin (1) or User (2)
```graphql
mutation {
  createUser(input: {
    name: "John Doe"
    email: "<EMAIL>"
    password: "password123"
    confirmPassword: "password123"
    roleId: 2  # Only 1 (Admin) or 2 (User) allowed, defaults to 2 (User)
  }) {
    message
    status
  }
}
```

### 3. Get Current User
```graphql
query {
  getCurrentUser {
    message
    status
    user {
      id
      name
      email
      roleId
      roleName
    }
  }
}
```

### 4. Assign Role (Admin Only) - Only Admin (1) or User (2)
```graphql
mutation {
  assignRole(input: {
    userId: 2
    roleId: 1  # Only 1 (Admin) or 2 (User) allowed
  }) {
    message
    status
  }
}
```

### 5. Get All Users (Admin Only)
```graphql
query {
  getAllCRMUsers {
    id
    name
    email
    roleId
    roleName
  }
}
```

## File Structure

```
src/
├── middleware/
│   └── auth.ts                 # Enhanced auth middleware with RBAC
├── extensions/
│   └── jwtTokenExtension.ts    # JWT with role information
├── resolvers/
│   ├── Mutation/
│   │   └── user.ts            # Enhanced user resolvers with RBAC
│   └── Query/
│       └── Query.ts           # Role-protected queries
├── types/
│   └── user.types.ts          # Enhanced type definitions
├── data/
│   ├── crm-permissions.ts     # Permission definitions
│   ├── crm-role-permissions.ts # Role-permission mappings
│   └── index.ts               # Updated exports
├── scripts/
│   └── create-admin.ts        # Admin user creation
└── seed.ts                    # Enhanced seeding with permissions
```

## Security Features

### 1. **Role-Based Access Control**
- Admin-only operations properly protected
- User role validation on all protected routes
- Consistent error handling (401/403)

### 2. **JWT Security**
- Enhanced tokens with role information
- Secure cookie configuration
- Proper token validation

### 3. **Database Security**
- Foreign key constraints with cascade delete
- Unique constraints on role-permission mappings
- Active/inactive status for permissions

## Permission System

The system includes 28 predefined permissions across different resources:
- User Management (5 permissions)
- Recruiter Management (5 permissions)
- Company Management (4 permissions)
- Job Management (5 permissions)
- Role Management (4 permissions)
- System Management (3 permissions)
- Callback Management (2 permissions)

## Important: Two-Role System Only

**This system is specifically designed for only TWO roles:**
- **Admin (roleId: 1)** - Full system access
- **User (roleId: 2)** - Limited access

**No additional roles are supported.** The permission system is in place for future expansion but currently only these two roles are implemented and allowed.

## Error Handling

- **401 Unauthorized**: User not authenticated
- **403 Forbidden**: User lacks required role
- **400 Bad Request**: Invalid input data
- **404 Not Found**: Resource not found

## Testing

Use GraphQL Playground or any GraphQL client to test the endpoints. Ensure you include the JWT token in either:
- Cookie: `jjcrm_token=<JWT_TOKEN>`
- Header: `Authorization: Bearer <JWT_TOKEN>`

This implementation follows your existing codebase patterns and provides a solid foundation for role-based access control in your CRM system.
