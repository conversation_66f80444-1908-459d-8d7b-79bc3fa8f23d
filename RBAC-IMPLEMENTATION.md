# Role-Based Authentication Implementation

## Overview

This document describes the role-based authentication (RBAC) system implemented in the JJ CRM backend following your existing codebase patterns and standards.

## Features Implemented

### 1. **Two-Role System** (as requested)
- **Admin Role (ID: 1)**: Full system access including user management
- **User Role (ID: 2)**: Limited access to basic CRM operations

### 2. **Database Schema Enhancements**
- Enhanced `Crmuserrole` table with description field
- New `CrmPermission` table for granular permissions
- New `CrmRolePermission` table for role-permission mapping
- Proper foreign key relationships with cascade delete

### 3. **Authentication Middleware** (following your patterns)
- `protect()`: Basic authentication check with role information
- `requireAdmin()`: Admin-only access control
- `requireRole()`: Flexible role-based access control
- Helper functions: `isAdmin()`, `hasRole()`, `hasPermission()`

### 4. **JWT Token Enhancement**
- Tokens now include role information (roleId, roleName, email)
- Secure cookie and Bearer token support
- Following your existing JWT extension pattern

### 5. **Graph<PERSON> Resolvers** (following your patterns)
- Enhanced user creation with role assignment (Admin only)
- Login with role information in response
- Role assignment functionality (Admin only)
- Current user information retrieval
- Admin-only queries for user and role management

## Setup Instructions

### 1. Generate Prisma Client
```bash
npx prisma generate
```

### 2. Run Database Migration
```bash
npx prisma migrate dev --name add_rbac_permissions
```

### 3. Seed Database with Roles and Permissions
```bash
npx prisma db seed -- --environment=allSeed
```

### 4. Create Initial Admin User
```bash
npx ts-node src/scripts/create-admin.ts
```

## Default Admin Credentials

After running the admin creation script:
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Admin

⚠️ **Important**: Change the default password after first login!

## API Usage Examples

### 1. Login (Returns role information)
```graphql
mutation {
  loginUser(input: {
    email: "<EMAIL>"
    password: "admin123"
  }) {
    message
    status
    token
    user {
      id
      name
      email
      roleId
      roleName
    }
  }
}
```

### 2. Create User (Admin Only)
```graphql
mutation {
  createUser(input: {
    name: "John Doe"
    email: "<EMAIL>"
    password: "password123"
    confirmPassword: "password123"
    roleId: 2
  }) {
    message
    status
  }
}
```

### 3. Get Current User
```graphql
query {
  getCurrentUser {
    message
    status
    user {
      id
      name
      email
      roleId
      roleName
    }
  }
}
```

### 4. Assign Role (Admin Only)
```graphql
mutation {
  assignRole(input: {
    userId: 2
    roleId: 1
  }) {
    message
    status
  }
}
```

### 5. Get All Users (Admin Only)
```graphql
query {
  getAllCRMUsers {
    id
    name
    email
    roleId
    roleName
  }
}
```

## File Structure

```
src/
├── middleware/
│   └── auth.ts                 # Enhanced auth middleware with RBAC
├── extensions/
│   └── jwtTokenExtension.ts    # JWT with role information
├── resolvers/
│   ├── Mutation/
│   │   └── user.ts            # Enhanced user resolvers with RBAC
│   └── Query/
│       └── Query.ts           # Role-protected queries
├── types/
│   └── user.types.ts          # Enhanced type definitions
├── data/
│   ├── crm-permissions.ts     # Permission definitions
│   ├── crm-role-permissions.ts # Role-permission mappings
│   └── index.ts               # Updated exports
├── scripts/
│   └── create-admin.ts        # Admin user creation
└── seed.ts                    # Enhanced seeding with permissions
```

## Security Features

### 1. **Role-Based Access Control**
- Admin-only operations properly protected
- User role validation on all protected routes
- Consistent error handling (401/403)

### 2. **JWT Security**
- Enhanced tokens with role information
- Secure cookie configuration
- Proper token validation

### 3. **Database Security**
- Foreign key constraints with cascade delete
- Unique constraints on role-permission mappings
- Active/inactive status for permissions

## Permission System

The system includes 28 predefined permissions across different resources:
- User Management (5 permissions)
- Recruiter Management (5 permissions)
- Company Management (4 permissions)
- Job Management (5 permissions)
- Role Management (4 permissions)
- System Management (3 permissions)
- Callback Management (2 permissions)

## Error Handling

- **401 Unauthorized**: User not authenticated
- **403 Forbidden**: User lacks required role
- **400 Bad Request**: Invalid input data
- **404 Not Found**: Resource not found

## Testing

Use GraphQL Playground or any GraphQL client to test the endpoints. Ensure you include the JWT token in either:
- Cookie: `jjcrm_token=<JWT_TOKEN>`
- Header: `Authorization: Bearer <JWT_TOKEN>`

## Future Enhancements

The system is designed to support:
- Additional custom roles
- Granular permission-based access control
- Resource-specific permissions
- Audit logging
- Session management

## Troubleshooting

### Common Issues

1. **"User is not authenticated"**
   - Verify JWT token is valid and not expired
   - Check token is sent in correct format

2. **"Access denied"**
   - User lacks required role (Admin vs User)
   - Check user's roleId in database

3. **Database errors**
   - Ensure migrations are run
   - Verify seeding completed successfully

This implementation follows your existing codebase patterns and provides a solid foundation for role-based access control in your CRM system.
