/*
  Warnings:

  - You are about to drop the column `isJobApplied` on the `recruiterjobpostdetail` table. All the data in the column will be lost.
  - You are about to drop the column `isJobViewed` on the `recruiterjobpostdetail` table. All the data in the column will be lost.
  - You are about to drop the column `jobAppliedDate` on the `recruiterjobpostdetail` table. All the data in the column will be lost.
  - You are about to drop the column `jobViewedDate` on the `recruiterjobpostdetail` table. All the data in the column will be lost.
  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.
  - You are about to drop the column `profileViewed` on the `userjobmapping` table. All the data in the column will be lost.
  - You are about to drop the column `shortlisted` on the `userjobviewmapping` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `recruiterjobpostdetail` DROP COLUMN `isJobApplied`,
    DROP COLUMN `isJobViewed`,
    DROP COLUMN `jobAppliedDate`,
    DROP COLUMN `jobViewedDate`;

-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- AlterTable
ALTER TABLE `userjobmapping` DROP COLUMN `profileViewed`,
    ADD COLUMN `candidateApplied` BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE `userjobviewmapping` DROP COLUMN `shortlisted`,
    ADD COLUMN `candidateViewed` BOOLEAN NOT NULL DEFAULT false;
