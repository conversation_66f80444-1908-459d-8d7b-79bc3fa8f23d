import { ApolloServer } from "@apollo/server";
import { expressMiddleware } from "@apollo/server/express4";
import { ApolloServerPluginDrainHttpServer } from "@apollo/server/plugin/drainHttpServer";
import { ApolloServerPluginLandingPageLocalDefault, ApolloServerPluginLandingPageProductionDefault } from '@apollo/server/plugin/landingPage/default';
import express from "express";
import http from "http";
import { json } from "body-parser";
import cors from "cors";
import Keyv from "keyv";
import { KeyvAdapter } from "@apollo/utils.keyvadapter";
import responseCachePlugin from '@apollo/server-plugin-response-cache';

import { typeDefs } from "./schema";
import { Query, Mutation } from "./resolvers";
import { Context, createContext } from "./context";
import * as dotenv from "dotenv";
import cookieParser from "cookie-parser";
import MailService from "./services/mailservice";
import { planQuery } from "./resolvers/Query/plan";
dotenv.config({ path: __dirname + "/.env" });

const app = express();
const httpServer = http.createServer(app);

// Email service initiate
const mailService = MailService.getInstance();

(async () => {
  const server = new ApolloServer<Context>({
    typeDefs,
    resolvers: {
      Query: {
        ...Query,
        ...planQuery
      },
      Mutation,
    },
    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }), 
      responseCachePlugin(),
      // ApolloServerPluginLandingPageProductionDefault({
      //   footer: false,
      // })
    ],
    // cache: new KeyvAdapter(new Keyv(`redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`)),
  });

  await server.start();

  const corsOptions = {
    origin: process.env.CORS_DOMAIN?.split(","),
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    credentials: true,
  };

  app.use(
    "/",
    cors<cors.CorsRequest>(corsOptions),
    json(),
    expressMiddleware(server, {
      context: createContext,
    })
  );

  app.use(cookieParser());
  await mailService.createConneection();
  await mailService.verifyConnection();

  await new Promise<void>((resolve) =>
    httpServer.listen({ port: process.env.PORT }, resolve)
  );
  console.log(`🚀 Server ready at http://localhost:${process.env.PORT}`);
})();
