export const workingDomain = [
  {
    id: 1, name: "Accounting/Bookkeeping", name_en: "Accounting/Bookkeeping", name_ta: "", isActive: true
  },
  {
    id: 2, name: "Admin & Office (Front & Back)", name_en: "Admin & Office (Front & Back)", name_ta: "", isActive: true
  },
  {
    id: 3, name: "Aerospace Engineering", name_en: "Aerospace Engineering", name_ta: "", isActive: true
  },
  {
    id: 4, name: "Aircraft Maintenance", name_en: "Aircraft Maintenance", name_ta: "", isActive: true
  },
  {
    id: 5, name: "Animation and Graphics", name_en: "Animation and Graphics", name_ta: "", isActive: true
  },
  {
    id: 6, name: "Automobile Engineering", name_en: "Automobile Engineering", name_ta: "", isActive: true
  },
  {
    id: 7, name: "Automobile Mechanic", name_en: "Automobile Mechanic", name_ta: "", isActive: true
  },
  {
    id: 8, name: "Beauty & Personal Care", name_en: "Beauty & Personal Care", name_ta: "", isActive: true
  },
  {
    id: 9, name: "Business Development", name_en: "Business Development", name_ta: "", isActive: true
  },
  {
    id: 10, name: "Carpentry", name_en: "Carpentry", name_ta: "", isActive: true
  },
  {
    id: 11, name: "Chemical Engineering", name_en: "Chemical Engineering", name_ta: "", isActive: true
  },
  {
    id: 12, name: "Civil Engineering", name_en: "Civil Engineering", name_ta: "", isActive: true
  },
  {
    id: 13, name: "Computer Science Engineering", name_en: "Computer Science Engineering", name_ta: "", isActive: true
  },
  {
    id: 14, name: "Content Writing/Copywriting", name_en: "Content Writing/Copywriting", name_ta: "", isActive: true
  },
  {
    id: 15, name: "Customer Service", name_en: "Customer Service", name_ta: "", isActive: true
  },
  {
    id: 16, name: "Customer Service Operations", name_en: "Customer Service Operations", name_ta: "", isActive: true
  },
  {
    id: 17, name: "Data Entry & MIS", name_en: "Data Entry & MIS", name_ta: "", isActive: true
  },
  {
    id: 18, name: "Driver / Ecommerce Delivery", name_en: "Driver / Ecommerce Delivery", name_ta: "", isActive: true
  },
  {
    id: 19, name: "Editing/Writing/Journalism", name_en: "Editing/Writing/Journalism", name_ta: "", isActive: true
  },
  {
    id: 20, name: "Electrical Engineering", name_en: "Electrical Engineering", name_ta: "", isActive: true
  },
  {
    id: 21, name: "Electrician", name_en: "Electrician", name_ta: "", isActive: true
  },
  {
    id: 22, name: "Electronics Engineering", name_en: "Electronics Engineering", name_ta: "", isActive: true
  },
  {
    id: 23, name: "Engineering (Others)", name_en: "Engineering (Others)", name_ta: "", isActive: true
  },
  {
    id: 24, name: "Facility Management", name_en: "Facility Management", name_ta: "", isActive: true
  },
  {
    id: 25, name: "Factory Worker", name_en: "Factory Worker", name_ta: "", isActive: true
  },
  {
    id: 26, name: "Field Sales", name_en: "Field Sales", name_ta: "", isActive: true
  },
  {
    id: 27, name: "Food Delivery", name_en: "Food Delivery", name_ta: "", isActive: true
  },
  {
    id: 28, name: "Gym and Fitness", name_en: "Gym and Fitness", name_ta: "", isActive: true
  },
  {
    id: 29, name: "Hospital/Medical staff", name_en: "Hospital/Medical staff", name_ta: "", isActive: true
  },
  {
    id: 30, name: "Hospitality Staff ( Non-Kitchen)", name_en: "Hospitality Staff ( Non-Kitchen)", name_ta: "", isActive: true
  },
  {
    id: 31, name: "Housekeeping", name_en: "Housekeeping", name_ta: "", isActive: true
  },
  {
    id: 32, name: "Human Resources (HR)", name_en: "Human Resources (HR)", name_ta: "", isActive: true
  },
  {
    id: 33, name: "Logistics & Warehouse", name_en: "Logistics & Warehouse", name_ta: "", isActive: true
  },
  {
    id: 34, name: "Machine Operator", name_en: "Machine Operator", name_ta: "", isActive: true
  },
  {
    id: 35, name: "Manufacturing", name_en: "Manufacturing", name_ta: "", isActive: true
  },
  {
    id: 36, name: "Marine/Ocean Engineering", name_en: "Marine/Ocean Engineering", name_ta: "", isActive: true
  },
  {
    id: 37, name: "Marketing", name_en: "Marketing", name_ta: "", isActive: true
  },
  {
    id: 38, name: "Mechanical Engineering", name_en: "Mechanical Engineering", name_ta: "", isActive: true
  },
  {
    id: 39, name: "Medical Lab & Diagnostics", name_en: "Medical Lab & Diagnostics", name_ta: "", isActive: true
  },
  {
    id: 40, name: "Painting", name_en: "Painting", name_ta: "", isActive: true
  },
  {
    id: 41, name: "Pharmacist/Medical Retail", name_en: "Pharmacist/Medical Retail", name_ta: "", isActive: true
  },
  {
    id: 42, name: "Photo & Video", name_en: "Photo & Video", name_ta: "", isActive: true
  },
  {
    id: 43, name: "Plumbing", name_en: "Plumbing", name_ta: "", isActive: true
  },
  {
    id: 44, name: "Production/Industrial Engineering", name_en: "Production/Industrial Engineering", name_ta: "", isActive: true
  },
  {
    id: 45, name: "Purchase & Supply Chain", name_en: "Purchase & Supply Chain", name_ta: "", isActive: true
  },
  {
    id: 46, name: "Refrigerator/AC (HVAC) Repair", name_en: "Refrigerator/AC (HVAC) Repair", name_ta: "", isActive: true
  },
  {
    id: 47, name: "Repair/Technician", name_en: "Repair/Technician", name_ta: "", isActive: true
  },
  {
    id: 48, name: "Restaurant Food Preperation", name_en: "Restaurant Food Preperation", name_ta: "", isActive: true
  },
  {
    id: 49, name: "Retail/Counter Sales", name_en: "Retail/Counter Sales", name_ta: "", isActive: true
  },
  {
    id: 50, name: "Sales Engineer ( Non-IT)", name_en: "Sales Engineer ( Non-IT)", name_ta: "", isActive: true
  },
  {
    id: 51, name: "Security", name_en: "Security", name_ta: "", isActive: true
  },
  {
    id: 52, name: "Tailoring", name_en: "Tailoring", name_ta: "", isActive: true
  },
  {
    id: 53, name: "Teacher/Tutor", name_en: "Teacher/Tutor", name_ta: "", isActive: true
  },
  {
    id: 54, name: "Technical Support", name_en: "Technical Support", name_ta: "", isActive: true
  },
  {
    id: 55, name: "Telesales", name_en: "Telesales", name_ta: "", isActive: true
  }
];
