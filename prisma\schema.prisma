// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "fullTextIndex"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User Personal Information Schema Start
model User {
  id                        Int                             @id @default(autoincrement())
  mobileNumber              String                          @unique @db.VarChar(70)
  profilePicture            String?                         @default("") @db.VarChar(150)
  name                      String?                         @default("") @db.VarChar(256)
  dateOfBirth               DateTime?                       @db.Date()
  whatsAppUpdate            Boolean                         @default(false)
  gender                    String?                         @default("") @db.Char(10)
  email                     String?                         @unique @db.VarChar(255)
  twoWheelerLicense         Boolean                         @default(false)
  coachMarkStatus           Boolean                         @default(false)
  latitude                  String?                         @default("")
  longitude                 String?                         @default("")
  location                  Unsupported("POINT SRID 4326")?
  state                     state?                          @relation(fields: [stateId], references: [id])
  stateId                   Int?
  city                      city?                           @relation(fields: [cityId], references: [id])
  cityId                    Int?
  createdAt                 DateTime                        @default(now())
  updatedAt                 DateTime                        @updatedAt
  language                  AppLanguage?                    @relation(fields: [languageId], references: [id])
  languageId                Int?
  userStatus                UserStatus?                     @relation(fields: [userStatusId], references: [id])
  userStatusId              Int?
  jobzAppStatus             JobzAppStatus?                  @relation(fields: [jobzAppStatusId], references: [id])
  jobzAppStatusId           Int?
  DeleteFeedback            DeleteFeedback?                 @relation(fields: [deleteFeedbackId], references: [id])
  deleteFeedbackId          Int?
  deleteFeedbackOtherReason String?                         @default("") @db.VarChar(255)
  userEducation             UserEducation?
  userProfessional          UserProfessional?
  userJobPreferences        UserJobPreferences?
  userFeedback              UserFeedback?
  userReportJob             UserReportJob[]
  UserRating                Int?                            @default(0)
  userJobMapping            UserJobMapping[]
  userJobViewMapping        UserJobViewMapping[]
  userNotification          UserNotification[]
  fcmTokens                 fcmdevicetoken[]
  recruiterUserDetails      recruiteruserdetails[]
  mobileNumberHash          String                          @default("") @db.VarChar(65)
  userLoginInfo             userlogininfo[]

  @@map("user")
}

model logintype {
  id                Int                 @id @default(autoincrement())
  name              String              @default("") @db.VarChar(50)
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  userlogininfo     userlogininfo[]
  recruiterlogininfo  recruiterlogininfo[]
}

model userlogininfo {
  id               Int       @id @default(autoincrement())
  user             User      @relation(fields: [userId], references: [id])
  userId           Int
  loginDevice      String?   @default("") @db.VarChar(100)
  devicePlatform   String?   @default("") @db.VarChar(100)
  deviceId         String?   @default("") @db.VarChar(100)
  loginDate        DateTime  @default(now())
  loginType        logintype @relation(fields: [logintypeId], references: [id])
  logintypeId      Int
  logoutDate       DateTime?    
  logoutType       logouttype?  @relation(fields: [logouttypeId], references: [id])
  logouttypeId     Int?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model UserStatus {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(10)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
}

model JobzAppStatus {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(10)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
}

model UserNotification {
  id                     Int                   @id @default(autoincrement())
  title                  String                @default("") @db.VarChar(30)
  description            String                @default("") @db.VarChar(150)
  isRead                 Boolean               @default(false)
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @updatedAt
  user                   User                  @relation(fields: [userId], references: [id])
  userId                 Int
  usernotificationtype   usernotificationtype? @relation(fields: [usernotificationtypeId], references: [id])
  usernotificationtypeId Int?

  @@map("usernotification")
}

model fcmdevicetoken {
  id        Int      @id @default(autoincrement())
  device    String   @default("") @db.VarChar(100)
  token     String   @default("") @db.VarChar(255)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])
  userId    Int

  @@unique([device, token, userId])
}

model usernotificationtype {
  id               Int                @id @default(autoincrement())
  name             String             @default("") @db.VarChar(30)
  isActive         Boolean            @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  userNotification UserNotification[]
}

model DeleteFeedback {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(50)
  name_en   String   @default("") @db.VarChar(50)
  name_ta   String   @default("") @db.VarChar(50)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
}

model AppLanguage {
  id        Int      @id @default(autoincrement())
  name      String?  @default("") @db.VarChar(20)
  name_en   String   @default("") @db.VarChar(50)
  name_ta   String   @default("") @db.VarChar(50)
  code      String?  @default("") @db.Char(6)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
}

model OTP {
  id             Int      @id @default(autoincrement())
  otp            String   @default("") @db.VarChar(6)
  expirationTime DateTime
  verified       Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model JobzOTP {
  id             Int      @id @default(autoincrement())
  otp            String   @default("") @db.VarChar(6)
  expirationTime DateTime
  verified       Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model state {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(250)
  name_en   String   @default("") @db.VarChar(250)
  name_ta   String   @default("") @db.VarChar(250)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
  city      city[]
}

model city {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(250)
  name_en   String   @default("") @db.VarChar(250)
  name_ta   String   @default("") @db.VarChar(250)
  state     state    @relation(fields: [stateId], references: [id])
  stateId   Int
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
}

// User Personal Information Schema End

// User Education Details Schema Start

model UserEducation {
  id                        Int               @id @default(autoincrement())
  user                      User              @relation(fields: [userId], references: [id])
  userId                    Int               @unique
  qualification             QualificationType @relation(fields: [qualificationId], references: [id])
  qualificationId           Int
  course                    Course            @relation(fields: [courseId], references: [id])
  courseId                  Int
  specialization            Specialization    @relation(fields: [specializationId], references: [id])
  specializationId          Int
  higherSecondaryPercentage Decimal?          @default(0) @db.Decimal(5, 2)
  graduatePercentage        Decimal?          @default(0) @db.Decimal(5, 2)
  collegeName               String?           @default("") @db.VarChar(255)
  graduationYear            Int?              @default(0)
  createdAt                 DateTime          @default(now())
  updatedAt                 DateTime          @updatedAt
  degreeMode                DegreeMode?       @relation(fields: [degreeModeId], references: [id])
  degreeModeId              Int?
  colleges                  Colleges?         @relation(fields: [collegesId], references: [id])
  collegesId                Int?
}

model QualificationType {
  id                           Int                            @id @default(autoincrement())
  name                         String                         @default("") @db.VarChar(50)
  name_en                      String                         @default("") @db.VarChar(50)
  name_ta                      String                         @default("") @db.VarChar(50)
  isActive                     Boolean                        @default(true)
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
  Course                       Course[]
  UserEducation                UserEducation[]
  RecruiterCandidatePreference RecruiterCandidatePreference[]
}

model Course {
  id              Int               @id @default(autoincrement())
  name            String            @default("") @db.VarChar(100)
  name_en         String            @default("") @db.VarChar(100)
  name_ta         String            @default("") @db.VarChar(100)
  Qualification   QualificationType @relation(fields: [qualificationId], references: [id])
  qualificationId Int
  isActive        Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  Specialization  Specialization[]
  UserEducation   UserEducation[]
}

model Specialization {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(150)
  name_en       String          @default("") @db.VarChar(150)
  name_ta       String          @default("") @db.VarChar(150)
  course        Course          @relation(fields: [courseId], references: [id])
  courseId      Int
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  UserEducation UserEducation[]
}

model DegreeMode {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(50)
  name_en       String          @default("") @db.VarChar(50)
  name_ta       String          @default("") @db.VarChar(50)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  UserEducation UserEducation[]
}

model Colleges {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(255)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  UserEducation UserEducation[]
}

// User Education Details Schema End

// User Professional Information Schema
model UserProfessional {
  id                    Int                    @id @default(autoincrement())
  user                  User                   @relation(fields: [userId], references: [id])
  userId                Int                    @unique
  workExperience        String?
  experienceYear        Int?                   @default(0)
  experienceMonth       Int?                   @default(0)
  monthlySalary         Int?                   @default(0)
  joinPreference        JoinPreference?        @relation(fields: [joinPreferenceId], references: [id])
  joinPreferenceId      Int?
  currentRoleCategory   JobRoleCategory?       @relation(fields: [currentRoleCategoryId], references: [id])
  currentRoleCategoryId Int?
  currentJobRole        JobRole?               @relation(fields: [currentJobRoleId], references: [id])
  currentJobRoleId      Int?
  previousJobTitle      String?                @default("") @db.VarChar(50)
  companyName           String?                @default("") @db.VarChar(50)
  languagesKnown        LanguageMapping[]
  englishKnowledge      EnglishKnowledgeLevel? @relation(fields: [englishKnowledgeId], references: [id])
  englishKnowledgeId    Int?
  skills                SkillsMapping[]
  workingDomain         WorkingDomainMapping[]
  resume                String?                @default("") @db.VarChar(255)
  professionalSummary   String?                @default("") @db.VarChar(800)
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
}

model EnglishKnowledgeLevel {
  id                           Int                            @id @default(autoincrement())
  name                         String                         @default("") @db.VarChar(50)
  name_en                      String                         @default("") @db.VarChar(50)
  name_ta                      String                         @default("") @db.VarChar(50)
  isActive                     Boolean                        @default(true)
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
  UserProfessional             UserProfessional[]
  RecruiterCandidatePreference RecruiterCandidatePreference[]
}

model MonthlySalaryRange {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(50)
  name_en   String   @default("") @db.VarChar(50)
  name_ta   String   @default("") @db.VarChar(50)
  minSalary Int      @default(0) @db.Int
  maxSalary Int      @default(0) @db.Int
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  UserJobPreferences UserJobPreferences[]

  @@map("monthlysalaryrange")
}

model jobtitle {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(255)
  name_en                String                   @default("") @db.VarChar(255)
  name_ta                String                   @default("") @db.VarChar(255)
  jobRole                JobRole?                 @relation(fields: [jobRoleId], references: [id])
  jobRoleId              Int?
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model LanguageMapping {
  userLanguage      UserProfessional @relation(fields: [userLanguageId], references: [id])
  userLanguageId    Int
  profileLanguage   ProfileLanguages @relation(fields: [profileLanguageId], references: [id])
  profileLanguageId Int

  @@id([userLanguageId, profileLanguageId])
}

model ProfileLanguages {
  id               Int               @id @default(autoincrement())
  name             String            @default("") @db.VarChar(20)
  name_en          String            @default("") @db.VarChar(20)
  name_ta          String            @default("") @db.VarChar(20)
  isActive         Boolean           @default(true)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  profileLanguages LanguageMapping[]
}

model SkillsMapping {
  userSkill          UserProfessional @relation(fields: [userProfessionalId], references: [id])
  userProfessionalId Int
  skill              SkillList        @relation(fields: [skillId], references: [id])
  skillId            Int

  @@id([userProfessionalId, skillId])
}

model SkillList {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(100)
  name_en                String                   @default("") @db.VarChar(100)
  name_ta                String                   @default("") @db.VarChar(100)
  jobRole                JobRole?                 @relation(fields: [jobRoleId], references: [id])
  jobRoleId              Int?
  isActive               Boolean                  @default(true)
  skillList              SkillsMapping[]
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  RecruiterSkillsMapping RecruiterSkillsMapping[]

  @@map("skilllist")
}

model WorkingDomainList {
  id                Int                    @id @default(autoincrement())
  name              String                 @default("") @db.VarChar(255)
  name_en           String                 @default("") @db.VarChar(255)
  name_ta           String                 @default("") @db.VarChar(255)
  isActive          Boolean                @default(true)
  workingDomainList WorkingDomainMapping[]
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
}

model WorkingDomainMapping {
  userWorkingDomain  UserProfessional  @relation(fields: [userProfessionalId], references: [id])
  userProfessionalId Int
  WorkingDomain      WorkingDomainList @relation(fields: [WorkingDomainId], references: [id])
  WorkingDomainId    Int

  @@id([userProfessionalId, WorkingDomainId])
}

// // User Professional Information Schema End

// // User Job Preferences Schema Start
model UserJobPreferences {
  id                 Int                     @id @default(autoincrement())
  user               User                    @relation(fields: [userId], references: [id])
  userId             Int                     @unique
  prefRoleCategory   JobRoleCategory?        @relation(fields: [prefRoleCategoryId], references: [id])
  prefRoleCategoryId Int?
  prefJobRole        JobRole?                @relation(fields: [prefJobRoleId], references: [id])
  prefJobRoleId      Int?
  city               String?                 @default("") @db.VarChar(70)
  area               String?                 @default("") @db.VarChar(70)
  distance           Distance?               @relation(fields: [distanceId], references: [id])
  distanceId         Int?
  salaryRange        MonthlySalaryRange?     @relation(fields: [salaryRangeId], references: [id])
  salaryRangeId      Int?
  userCommuteMode    CommuteModeMapping[]
  userEmploymentType EmploymentTypeMapping[]
  userWorkType       WorkTypeMapping[]
  userShiftType      ShiftTypeMapping[]
  userWorkingDays    WorkingDaysMapping[]
  createdAt          DateTime                @default(now())
  updatedAt          DateTime                @updatedAt
}

model Distance {
  id                           Int                            @id @default(autoincrement())
  name                         String                         @default("") @db.VarChar(50)
  name_en                      String                         @default("") @db.VarChar(50)
  name_ta                      String                         @default("") @db.VarChar(50)
  isActive                     Boolean                        @default(true)
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
  UserJobPreferences           UserJobPreferences[]
  RecruiterCandidatePreference RecruiterCandidatePreference[]
}

model JobRoleCategory {
  id                 Int                  @id @default(autoincrement())
  name               String               @default("") @db.VarChar(255)
  name_en            String               @default("") @db.VarChar(255)
  name_ta            String               @default("") @db.VarChar(255)
  isActive           Boolean              @default(true)
  UserJobPreferences UserJobPreferences[]
  UserProfessional   UserProfessional[]
  jobRole            JobRole[]
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
}

model JobRole {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(255)
  name_en                String                   @default("") @db.VarChar(255)
  name_ta                String                   @default("") @db.VarChar(255)
  jobRoleCategory        JobRoleCategory          @relation(fields: [jobRoleCategoryId], references: [id])
  jobRoleCategoryId      Int
  isActive               Boolean                  @default(true)
  UserJobPreferences     UserJobPreferences[]
  UserProfessional       UserProfessional[]
  RecruiterJobPostDetail RecruiterJobPostDetail[]
  skills                 SkillList[]
  jobTitles              jobtitle[]
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
}

model CommuteModeMapping {
  userJobPreference   UserJobPreferences @relation(fields: [userJobPreferenceId], references: [id])
  userJobPreferenceId Int
  commuteMode         CommuteMode        @relation(fields: [commuteModeId], references: [id])
  commuteModeId       Int

  @@id([userJobPreferenceId, commuteModeId])
}

model CommuteMode {
  id           Int                  @id @default(autoincrement())
  name         String               @default("") @db.VarChar(40)
  name_en      String               @default("") @db.VarChar(40)
  name_ta      String               @default("") @db.VarChar(40)
  isActive     Boolean              @default(true)
  commuteModes CommuteModeMapping[]
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt
}

model EmploymentTypeMapping {
  userJobPreference   UserJobPreferences @relation(fields: [userJobPreferenceId], references: [id])
  userJobPreferenceId Int
  employmentType      EmploymentType     @relation(fields: [employmentTypeId], references: [id])
  employmentTypeId    Int

  @@id([userJobPreferenceId, employmentTypeId])
}

model EmploymentType {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(40)
  name_en                String                   @default("") @db.VarChar(40)
  name_ta                String                   @default("") @db.VarChar(40)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  employmentTypes        EmploymentTypeMapping[]
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model WorkTypeMapping {
  userJobPreference   UserJobPreferences @relation(fields: [userJobPreferenceId], references: [id])
  userJobPreferenceId Int
  workType            WorkType           @relation(fields: [workTypeId], references: [id])
  workTypeId          Int

  @@id([userJobPreferenceId, workTypeId])
}

model WorkType {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(40)
  name_en                String                   @default("") @db.VarChar(40)
  name_ta                String                   @default("") @db.VarChar(40)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  workTypes              WorkTypeMapping[]
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model ShiftTypeMapping {
  userJobPreference   UserJobPreferences @relation(fields: [userJobPreferenceId], references: [id])
  userJobPreferenceId Int
  shiftType           ShiftType          @relation(fields: [shiftTypeId], references: [id])
  shiftTypeId         Int

  @@id([userJobPreferenceId, shiftTypeId])
}

model ShiftType {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(40)
  name_en                String                   @default("") @db.VarChar(40)
  name_ta                String                   @default("") @db.VarChar(40)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  shiftTypes             ShiftTypeMapping[]
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model WorkingDaysMapping {
  userJobPreference   UserJobPreferences @relation(fields: [userJobPreferenceId], references: [id])
  userJobPreferenceId Int
  workingDays         WorkingDays        @relation(fields: [workingDaysId], references: [id])
  workingDaysId       Int

  @@id([userJobPreferenceId, workingDaysId])
}

model WorkingDays {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(50)
  name_en                String                   @default("") @db.VarChar(50)
  name_ta                String                   @default("") @db.VarChar(50)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  workingDays            WorkingDaysMapping[]
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model JoinPreference {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(30)
  name_en                String                   @default("") @db.VarChar(30)
  name_ta                String                   @default("") @db.VarChar(30)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  UserProfessional       UserProfessional[]
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model SuggestionFeedback {
  id           Int            @id @default(autoincrement())
  name         String         @default("") @db.VarChar(50)
  name_en      String         @default("") @db.VarChar(50)
  name_ta      String         @default("") @db.VarChar(50)
  isActive     Boolean        @default(true)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  userFeedback UserFeedback[]
}

model UserReviewFeedback {
  id           Int            @id @default(autoincrement())
  name         String         @default("") @db.VarChar(50)
  name_en      String         @default("") @db.VarChar(50)
  name_ta      String         @default("") @db.VarChar(50)
  isActive     Boolean        @default(true)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  userFeedback UserFeedback[]
}

model UserFeedback {
  id                 Int                 @id @default(autoincrement())
  user               User                @relation(fields: [userId], references: [id])
  userId             Int                 @unique
  reviewFeedback     UserReviewFeedback? @relation(fields: [reviewId], references: [id])
  reviewId           Int?
  suggestionFeedback SuggestionFeedback? @relation(fields: [suggestionId], references: [id])
  suggestionId       Int?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
}

model ReportJob {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(70)
  name_en       String          @default("") @db.VarChar(70)
  name_ta       String          @default("") @db.VarChar(70)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  userReportJob UserReportJob[]
}

model UserReportJob {
  id                Int        @default(autoincrement())
  user              User       @relation(fields: [userId], references: [id])
  userId            Int
  job               Job        @relation(fields: [jobId], references: [id])
  jobId             Int
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  reportJob         ReportJob? @relation(fields: [reportJobId], references: [id])
  reportJobId       Int?
  otherReportReason String?    @db.VarChar(255)
  isJobReported     Boolean    @default(false)

  @@id([id, jobId])
  @@unique([userId, jobId])
  @@unique([userId, jobId, reportJobId])
  @@map("userreportjob")
}

// User and Job Mapping
model UserJobMapping {
  id                  Int          @default(autoincrement())
  user                User         @relation(fields: [userId], references: [id])
  userId              Int
  job                 Job          @relation(fields: [jobId], references: [id])
  jobId               Int
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt
  candidateApplied    Boolean      @default(false)
  jobAppliedDate      DateTime?    @default(now())
  jobFeedback         jobfeedback? @relation(fields: [jobFeedbackId], references: [id])
  jobFeedbackId       Int?
  isJobFeedBack       Boolean?     @default(false)
  jobFeedbackDate     DateTime?
  otherFeedbackReason String?      @db.VarChar(255)

  @@id([id, userId, jobId])
  @@unique([userId, jobId])
  @@unique([userId, jobId, jobFeedbackId]) // Unique constraint for (userId, jobId, jobFeedbackId)
  @@map("userjobmapping")
}

model jobfeedback {
  id             Int              @id @default(autoincrement())
  name           String           @default("") @db.VarChar(70)
  name_en        String           @default("") @db.VarChar(70)
  name_ta        String           @default("") @db.VarChar(70)
  isActive       Boolean          @default(true)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  userJobMapping UserJobMapping[]
}

model recruiteruserdetails {
  id                         Int                       @default(autoincrement())
  Recruiter                  Recruiter                 @relation(fields: [recruiterId], references: [id])
  recruiterId                Int
  user                       User                      @relation(fields: [userId], references: [id])
  userId                     Int
  job                        Job                       @relation(fields: [jobId], references: [id])
  jobId                      Int
  shortlisted                Boolean                   @default(false)
  shortlistedDate            DateTime?
  profileViewed              Boolean                   @default(false)
  profileViewedDate          DateTime?
  candidateprofilestatus     candidateprofilestatus?   @relation(fields: [candidateProfileStatusId], references: [id])
  candidateProfileStatusId   Int?
  recruiterReportCandidate   recruiterreportcandidate? @relation(fields: [recruiterReportCandidateId], references: [id])
  recruiterReportCandidateId Int?
  otherReportReason          String?                   @db.VarChar(255)

  @@id([id, userId, jobId, recruiterId])
  @@unique([userId, recruiterId, jobId])
}

model recruiterreportcandidate {
  id                   Int                    @id @default(autoincrement())
  name                 String                 @default("") @db.VarChar(70)
  name_en              String                 @default("") @db.VarChar(70)
  name_ta              String                 @default("") @db.VarChar(70)
  isActive             Boolean                @default(true)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  recruiterUserDetails recruiteruserdetails[]
}

model candidateprofilestatus {
  id                   Int                    @id @default(autoincrement())
  name                 String                 @default("") @db.VarChar(20)
  isActive             Boolean                @default(true)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  recruiteruserdetails recruiteruserdetails[]
}

model UserJobViewMapping {
  id              Int      @default(autoincrement())
  user            User     @relation(fields: [userId], references: [id])
  userId          Int
  job             Job      @relation(fields: [jobId], references: [id])
  jobId           Int
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  candidateViewed Boolean  @default(false)
  jobViewedDate   DateTime @default(now())

  @@id([id, userId, jobId])
  @@unique([userId, jobId])
  @@map("userjobviewmapping")
}

// // User Job Preferences Schema End

// Recruiter

model Recruiter {
  id                               Int                                @id @default(autoincrement())
  mobileNumber                     String                             @unique
  name                             String                             @default("") @db.VarChar(255)
  email                            String?                            @unique @db.VarChar(255)
  isEmailVerified                  Boolean                            @default(false)
  isPersonalVerified               Boolean                            @default(false)
  isCompanyVerified                Boolean                            @default(false)
  whatsAppUpdate                   Boolean                            @default(false)
  isRecruiterHoldEmail             Boolean                            @default(false)
  isRecruiterSubmitEmail           Boolean                            @default(false)
  isAppRatingSubmit                Boolean                            @default(false)
  isAppFeedbackSubmit              Boolean                            @default(false)
  mobileNumberHash                 String                             @default("") @db.VarChar(65)
  emailHash                        String                             @default("") @db.VarChar(65)
  appRating                        Int?                               @default(0)
  createdAt                        DateTime                           @default(now())
  updatedAt                        DateTime                           @updatedAt
  RecruiterRole                    RecruiterRole?                     @relation(fields: [recruiterRoleId], references: [id])
  recruiterRoleId                  Int?                               @default(1)
  RecruiterStatus                  RecruiterStatus?                   @relation(fields: [recruiterStatusId], references: [id])
  recruiterStatusId                Int?
  HiringFor                        HiringFor?                         @relation(fields: [hiringForId], references: [id])
  hiringForId                      Int?                               @default(1)
  Designation                      Designation?                       @relation(fields: [designationId], references: [id])
  designationId                    Int?                               @default(1)
  company                          Company?
  ClientCompany                    ClientCompany[]
  Recruiterdocument                Recruiterdocument?
  Job                              Job[]
  recruiterUserDetails             recruiteruserdetails[]
  recruiterAppFeedbackMapping      recruiterappfeedbackmapping?
  recruiterrequest                 recruiterrequest[]
  recruiterlogininfo               recruiterlogininfo[]
  recruiterplanpurchasetransaction recruiterplanpurchasetransaction[]
  recruiterlatestplaninfo          recruiterlatestplaninfo?
  recruitercredittransaction       recruitercredittransaction[]
  recruitercreditrequest           recruitercreditrequest[]

  @@map("recruiter")
}

model recruiterlogininfo {
  id               Int       @id @default(autoincrement())
  recruiter        Recruiter      @relation(fields: [recruiterId], references: [id])
  recruiterId      Int
  loginDevice      String?   @default("") @db.VarChar(100)
  devicePlatform   String?   @default("") @db.VarChar(100)
  deviceId         String?   @default("") @db.VarChar(100)
  loginDate        DateTime  @default(now())
  loginType        logintype @relation(fields: [logintypeId], references: [id])
  logintypeId      Int
  logoutDate       DateTime?    
  logoutType       logouttype?  @relation(fields: [logouttypeId], references: [id])
  logouttypeId     Int?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model logouttype {
  id                Int                 @id @default(autoincrement())
  name              String              @default("") @db.VarChar(50)
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  userlogininfo     userlogininfo[]
  recruiterlogininfo recruiterlogininfo[]
}

model Recruiterdocument {
  id                   Int                 @id @default(autoincrement())
  aadhaarNumber        String?             @db.VarChar(255)
  panNumber            String?             @db.VarChar(255)
  gstin                String?             @db.VarChar(255)
  fssai                String?             @db.VarChar(255)
  udyogAadhaar         String?             @db.VarChar(255)
  aadhaarImgUrl        String?             @db.VarChar(255)
  panImgUrl            String?             @db.VarChar(255)
  gstinImgUrl          String?             @db.VarChar(255)
  fssaiImgUrl          String?             @db.VarChar(255)
  udyogAadhaarImgUrl   String?             @db.VarChar(255)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  recruiter            Recruiter           @relation(fields: [recruiterId], references: [id])
  recruiterId          Int                 @unique
  Personalstatus       Verificationstatus? @relation("personalStatus", fields: [personalstatusId], references: [id])
  personalstatusId     Int?                @default(1)
  Companystatus        Verificationstatus? @relation("companyStatus", fields: [companystatusId], references: [id])
  companystatusId      Int?                @default(1)
  personalDocGiven     Verificationdocs?   @relation("personalDoc", fields: [personalDocGivenId], references: [id])
  personalDocGivenId   Int?
  companyDocGiven      Verificationdocs?   @relation("companyDoc", fields: [companyDocGivenId], references: [id])
  companyDocGivenId    Int?
  personalRejectReason String?             @db.VarChar(255)
  companyRejectReason  String?             @db.VarChar(255)

  @@map("recruiterdocument")
}

model RecruiterOTP {
  id             Int      @id @default(autoincrement())
  otp            String   @default("") @db.VarChar(6)
  expirationTime DateTime
  verified       Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model RecruiterEmailOTP {
  id             Int      @id @default(autoincrement())
  otp            String   @default("") @db.VarChar(6)
  expirationTime DateTime
  verified       Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model RecruiterStatus {
  id        Int         @id @default(autoincrement())
  name      String      @default("") @db.VarChar(20)
  isActive  Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  Recruiter Recruiter[]
}

model Verificationstatus {
  id               Int                 @id @default(autoincrement())
  name             String              @default("") @db.VarChar(20)
  isActive         Boolean             @default(true)
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  Personaldocument Recruiterdocument[] @relation("personalStatus")
  Companydocument  Recruiterdocument[] @relation("companyStatus")

  @@map("Verificationstatus")
}

model Verificationdocs {
  id               Int                 @id @default(autoincrement())
  name             String              @default("") @db.VarChar(35)
  isActive         Boolean             @default(true)
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  PersonalDocGiven Recruiterdocument[] @relation("personalDoc")
  CompanyDocGiven  Recruiterdocument[] @relation("companyDoc")

  @@map("verificationdocs")
}

model Designation {
  id        Int         @id @default(autoincrement())
  name      String      @default("") @db.VarChar(100)
  isActive  Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  Recruiter Recruiter[]
}

model HiringFor {
  id        Int         @id @default(autoincrement())
  name      String      @default("") @db.VarChar(20)
  isActive  Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  Recruiter Recruiter[]
  Company   Company[]
  Job       Job[]
}

model RecruiterRole {
  id        Int         @id @default(autoincrement())
  name      String      @default("") @db.VarChar(20)
  isActive  Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  Recruiter Recruiter[]

  @@map("recruiterrole")
}

model CompanyList {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(255)
  name_en       String          @default("") @db.VarChar(255)
  name_ta       String          @default("") @db.VarChar(255)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  Company       Company[]
  ClientCompany ClientCompany[]
  Job           Job[]
}

model Company {
  id                       Int                        @id @default(autoincrement())
  name                     String                     @default("") @db.VarChar(255)
  industry                 Industry?                  @relation(fields: [industryId], references: [id])
  industryId               Int?
  NumberOfEmployees        NumberOfEmployees?         @relation(fields: [numberOfEmployeesId], references: [id])
  numberOfEmployeesId      Int?
  location                 String                     @default("") @db.VarChar(255)
  buildingName             String                     @default("") @db.VarChar(255)
  jobs                     RecruiterJobPostDetail[]
  CompanyType              HiringFor?                 @relation(fields: [companyTypeId], references: [id])
  companyTypeId            Int?
  isCompanyVerified        Boolean                    @default(false)
  isDeleted                Boolean                    @default(false)
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  consultantCompanyMapping ConsultantCompanyMapping[]
  Recruiter                Recruiter                  @relation(fields: [recruiterId], references: [id])
  recruiterId              Int                        @unique
  CompanyList              CompanyList?               @relation(fields: [companyListId], references: [id])
  companyListId            Int?
  Job                      Job[]

  @@map("company")
}

model ConsultantCompanyMapping {
  consultant      Company       @relation(fields: [consultantId], references: [id])
  consultantId    Int
  clientCompany   ClientCompany @relation(fields: [clientCompanyId], references: [id])
  clientCompanyId Int
  createdAt       DateTime      @default(now())

  @@id([consultantId, clientCompanyId])
  @@map("consultantcompanymapping")
}

model ClientCompany {
  id                       Int                        @id @default(autoincrement())
  name                     String                     @default("") @db.VarChar(255)
  industry                 Industry?                  @relation(fields: [industryId], references: [id])
  industryId               Int?
  NumberOfEmployees        NumberOfEmployees?         @relation(fields: [numberOfEmployeesId], references: [id])
  numberOfEmployeesId      Int?
  location                 String                     @default("") @db.VarChar(255)
  buildingName             String                     @default("") @db.VarChar(255)
  isCompanyVerified        Boolean                    @default(false)
  isDeleted                Boolean                    @default(false)
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  CompanyList              CompanyList?               @relation(fields: [companyListId], references: [id])
  companyListId            Int?
  Recruiter                Recruiter                  @relation(fields: [recruiterId], references: [id])
  recruiterId              Int
  consultantCompanyMapping ConsultantCompanyMapping[]
  Job                      Job[]

  @@map("clientcompany")
}

model Industry {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(255)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  Company       Company[]
  ClientCompany ClientCompany[]
}

model NumberOfEmployees {
  id            Int             @id @default(autoincrement())
  name          String          @default("") @db.VarChar(255)
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  Company       Company[]
  ClientCompany ClientCompany[]
}

// Job
model Job {
  id                                 Int                               @id @default(autoincrement())
  companyName                        String                            @db.VarChar(255)
  recruiter                          Recruiter                         @relation(fields: [recruiterId], references: [id])
  recruiterId                        Int
  company                            Company                           @relation(fields: [companyId], references: [id])
  companyId                          Int
  CompanyType                        HiringFor?                        @relation(fields: [companyTypeId], references: [id])
  companyTypeId                      Int?
  CompanyList                        CompanyList                       @relation(fields: [companyListId], references: [id])
  companyListId                      Int
  consultantCompany                  ClientCompany?                    @relation(fields: [consultantId], references: [id])
  consultantId                       Int?
  jobStatus                          JobStatus                         @relation(fields: [jobStatusId], references: [id])
  jobStatusId                        Int
  createdAt                          DateTime?
  updatedAt                          DateTime?                         @updatedAt
  jobExpiry                          DateTime?
  recruiterPlanpurchaseTransaction   recruiterplanpurchasetransaction? @relation(fields: [recruiterPlanpurchaseTransactionId], references: [id])
  recruiterPlanpurchaseTransactionId Int?
  JobDetail                          RecruiterJobPostDetail?
  candidatePreference                RecruiterCandidatePreference?
  interviewPreference                Interview?
  userJobMapping                     UserJobMapping[]
  userJobViewMapping                 UserJobViewMapping[]
  recruiterUserDetails               recruiteruserdetails[]
  userReportJob                      UserReportJob[]
  recruitercredittransaction         recruitercredittransaction[]

  @@map("job")
}

// Recruiter Posted Jobs
model RecruiterJobPostDetail {
  id                    Int                  @id @default(autoincrement())
  companyName           String               @db.VarChar(255)
  jobTitle              String               @default("") @db.VarChar(255)
  jobCategory           JobRole              @relation(fields: [jobCategoryId], references: [id])
  jobCategoryId         Int
  jobLocation           String               @default("") @db.VarChar(255)
  buildingName          String               @default("") @db.VarChar(255)
  area                  String?              @default("") @db.VarChar(100)
  city                  String?              @default("") @db.VarChar(100)
  state                 String?              @default("") @db.VarChar(55)
  zipcode               String?              @default("") @db.VarChar(20)
  latitude              String               @default("")
  longitude             String               @default("")
  numberOpenings        Int                  @default(1)
  workType              WorkType             @relation(fields: [workTypeId], references: [id])
  workTypeId            Int
  jobUrgency            JoinPreference       @relation(fields: [jobUrgencyId], references: [id])
  jobUrgencyId          Int
  jobDescription        String               @db.Text
  minMonthSalary        Int                  @default(0)
  maxMonthSalary        Int?                 @default(0)
  isIncentives          Boolean              @default(false)
  avgMonthlyIncentive   Int?                 @default(0)
  jobBenefits           JobBenefitsMapping[]
  WorkingDays           WorkingDays          @relation(fields: [workingDaysId], references: [id])
  workingDaysId         Int
  feesDeposit           Boolean              @default(false)
  depositAmount         Int?
  depositReason         DepositReason?       @relation(fields: [depositReasonId], references: [id])
  depositReasonId       Int?
  additionalInformation String?              @db.Text
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  company               Company              @relation(fields: [companyId], references: [id])
  companyId             Int
  shiftType             ShiftType            @relation(fields: [shiftTypeId], references: [id])
  shiftTypeId           Int
  employmentType        EmploymentType       @relation(fields: [employmentTypeId], references: [id])
  employmentTypeId      Int
  job                   Job                  @relation(fields: [jobId], references: [id])
  jobId                 Int                  @unique
  JobTitle              jobtitle?            @relation(fields: [jobTitleId], references: [id])
  jobTitleId            Int?

  @@fulltext([jobTitle, companyName, jobDescription, jobLocation, buildingName])
  @@map("recruiterjobpostdetail")
}

// Recruiter Candidate Preference
model RecruiterCandidatePreference {
  id                     Int                      @id @default(autoincrement())
  Gender                 Gender                   @relation(fields: [genderId], references: [id])
  genderId               Int
  Education              QualificationType        @relation(fields: [educationId], references: [id])
  educationId            Int
  WorkExperience         WorkExperience           @relation(fields: [workExperienceId], references: [id])
  workExperienceId       Int
  minExperience          Int                      @default(0)
  maxExperience          Int?                     @default(0)
  applicationRadius      Distance                 @relation(fields: [applicationRadiusId], references: [id])
  applicationRadiusId    Int
  EnglishKnowledge       EnglishKnowledgeLevel    @relation(fields: [englishKnowledgeId], references: [id])
  englishKnowledgeId     Int
  RecruiterSkillsMapping RecruiterSkillsMapping[]
  isAssetsRequired       Boolean                  @default(false)
  RequiredAssetsMapping  RequiredAssetsMapping[]
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  job                    Job                      @relation(fields: [jobId], references: [id])
  jobId                  Int                      @unique

  @@map("recruitercandidatepreference")
}

// Recruiter Interview Schedule
model Interview {
  id                  Int              @id @default(autoincrement())
  hrName              String           @default("") @db.VarChar(100)
  hrContactNumber     String           @default("") @db.VarChar(255)
  hrContactNumberHash String           @default("") @db.VarChar(65)
  faceInterview       FFinterview?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  WalkinInterview     Walkininterview?
  InterviewType       Interviewtype    @relation(fields: [interviewTypeId], references: [id])
  interviewTypeId     Int
  job                 Job              @relation(fields: [jobId], references: [id])
  jobId               Int              @unique

  @@map("interview")
}

model JobStatus {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(20)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  job       Job[]
}

model JobCategory {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(55)
  name_en   String   @default("") @db.VarChar(55)
  name_ta   String   @default("") @db.VarChar(55)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model JobBenefits {
  id                 Int                  @id @default(autoincrement())
  name               String               @default("") @db.VarChar(255)
  name_en            String               @default("") @db.VarChar(255)
  name_ta            String               @default("") @db.VarChar(255)
  isActive           Boolean              @default(true)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  JobBenefitsMapping JobBenefitsMapping[]
}

model DepositReason {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(255)
  name_en                String                   @default("") @db.VarChar(255)
  name_ta                String                   @default("") @db.VarChar(255)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  RecruiterJobPostDetail RecruiterJobPostDetail[]
}

model JobBenefitsMapping {
  RecruiterJobPostDetail   RecruiterJobPostDetail @relation(fields: [recruiterJobPostDetailId], references: [id])
  recruiterJobPostDetailId Int
  JobBenefits              JobBenefits            @relation(fields: [jobBenefitsId], references: [id])
  jobBenefitsId            Int

  @@id([jobBenefitsId, recruiterJobPostDetailId])
}

model Gender {
  id                           Int                            @id @default(autoincrement())
  name                         String                         @default("") @db.VarChar(20)
  name_en                      String                         @default("") @db.VarChar(20)
  name_ta                      String                         @default("") @db.VarChar(20)
  isActive                     Boolean                        @default(true)
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
  RecruiterCandidatePreference RecruiterCandidatePreference[]
}

model WorkExperience {
  id                           Int                            @id @default(autoincrement())
  name                         String                         @default("") @db.VarChar(25)
  name_en                      String                         @default("") @db.VarChar(25)
  name_ta                      String                         @default("") @db.VarChar(25)
  isActive                     Boolean                        @default(true)
  createdAt                    DateTime                       @default(now())
  updatedAt                    DateTime                       @updatedAt
  RecruiterCandidatePreference RecruiterCandidatePreference[]
}

model RecruiterSkillsMapping {
  recruiterCandiateSkill         RecruiterCandidatePreference @relation(fields: [recruiterCandidatePreferenceId], references: [id])
  recruiterCandidatePreferenceId Int
  skill                          SkillList                    @relation(fields: [skillId], references: [id])
  skillId                        Int

  @@id([recruiterCandidatePreferenceId, skillId])
}

model RequiredAssets {
  id                    Int                     @id @default(autoincrement())
  name                  String                  @default("") @db.VarChar(55)
  name_en               String                  @default("") @db.VarChar(55)
  name_ta               String                  @default("") @db.VarChar(55)
  isActive              Boolean                 @default(true)
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt
  RequiredAssetsMapping RequiredAssetsMapping[]
}

model RequiredAssetsMapping {
  RequiredAssets                 RequiredAssets?              @relation(fields: [requiredAssetsId], references: [id])
  requiredAssetsId               Int
  recruiterCandiateSkill         RecruiterCandidatePreference @relation(fields: [recruiterCandidatePreferenceId], references: [id])
  recruiterCandidatePreferenceId Int

  @@id([recruiterCandidatePreferenceId, requiredAssetsId])
}

model Interviewtype {
  id        Int         @id @default(autoincrement())
  name      String      @default("") @db.VarChar(55)
  name_en   String      @default("") @db.VarChar(55)
  name_ta   String      @default("") @db.VarChar(55)
  isActive  Boolean     @default(true)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  Interview Interview[]

  @@map("interviewtype")
}

model FFinterview {
  id           Int       @id @default(autoincrement())
  buildingName String    @default("") @db.VarChar(255)
  fullAddress  String    @default("") @db.VarChar(255)
  interviewId  Int       @unique
  Interview    Interview @relation(fields: [interviewId], references: [id])
  latitude     String    @default("") @db.VarChar(20)
  longitude    String    @default("") @db.VarChar(20)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@map("ffinterview")
}

model Walkininterview {
  id              Int       @id @default(autoincrement())
  walkinStartDate DateTime  @db.Date
  duration        Int       @default(0)
  startTime       String    @default("") @db.VarChar(50)
  endTime         String    @default("") @db.VarChar(50)
  location        String    @default("") @db.VarChar(255)
  buildingName    String    @default("") @db.VarChar(255)
  interviewId     Int       @unique
  Interview       Interview @relation(fields: [interviewId], references: [id])
  latitude        String    @default("") @db.VarChar(20)
  longitude       String    @default("") @db.VarChar(20)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@map("walkininterview")
}

model recruiterappfeedback {
  id                          Int                           @id @default(autoincrement())
  name                        String                        @default("") @db.VarChar(255)
  name_en                     String                        @default("") @db.VarChar(255)
  name_ta                     String                        @default("") @db.VarChar(255)
  isActive                    Boolean                       @default(true)
  createdAt                   DateTime                      @default(now())
  updatedAt                   DateTime                      @updatedAt
  recruiterAppFeedbackMapping recruiterappfeedbackmapping[]
}

model recruiterappfeedbackmapping {
  id                              Int                  @id @default(autoincrement())
  recruiter                       Recruiter            @relation(fields: [recruiterId], references: [id])
  recruiterId                     Int                  @unique
  recruiterappFeedback            recruiterappfeedback @relation(fields: [recruiterappFeedbackId], references: [id])
  recruiterappFeedbackId          Int
  recruiterappFeedbackOtherReason String?              @default("") @db.VarChar(255)
  createdAt                       DateTime             @default(now())
  updatedAt                       DateTime             @updatedAt
}

// CRM Schema
model Crmuser {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @db.VarChar(50)
  email                  String                   @unique @db.VarChar(255)
  mobileNumber           String?                  @db.VarChar(255)
  password               String                   @db.VarChar(255)
  role                   Crmuserrole              @relation(fields: [roleId], references: [id])
  roleId                 Int                      @default(2)
  emailHash              String?                  @db.VarChar(255)
  mobileHash             String?                  @db.VarChar(255)
  recruiterrequest       recruiterrequest[]
  recruitercreditrequest recruitercreditrequest[]

  @@map("crmuser")
}

model Crmuserrole {
  id        Int       @id @default(autoincrement())
  name      String    @default("") @db.VarChar(20)
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  Crmuser   Crmuser[]

  @@map("crmuserrole")
}

model recruiterrequest {
  id                         Int                       @id @default(autoincrement())
  recruiterId                Int
  crmUserId                  Int?
  requestCallBackStatusId    Int?                      @default(1)
  requestCallBackStatus      requestcallbackstatus?    @relation(fields: [requestCallBackStatusId], references: [id])
  recruiterRequestFeedbackId Int?
  recruiterRequestFeedback   recruiterrequestfeedback? @relation(fields: [recruiterRequestFeedbackId], references: [id])
  requestCallBackOtherReason String?                   @default("") @db.VarChar(255)
  createdAt                  DateTime                  @default(now())
  updatedAt                  DateTime                  @updatedAt
  recruiter                  Recruiter                 @relation(fields: [recruiterId], references: [id])
  crmuser                    Crmuser?                  @relation(fields: [crmUserId], references: [id])
}

model requestcallbackstatus {
  id               Int                @id @default(autoincrement())
  name             String             @default("") @db.VarChar(20)
  isActive         Boolean            @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  recruiterRequest recruiterrequest[]
}

model recruiterrequestfeedback {
  id               Int                @id @default(autoincrement())
  name             String             @default("") @db.VarChar(50)
  name_en          String             @default("") @db.VarChar(200)
  name_ta          String             @default("") @db.VarChar(200)
  isActive         Boolean            @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  recruiterRequest recruiterrequest[]
}

model emaildomains {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(100)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// *** Plan Payment and Credits Section ***//

// Create Plans
model plan {
  id                               Int                                @id @default(autoincrement())
  planName                         String                             @default("") @db.VarChar(100)
  planTitle                        String                             @default("") @db.VarChar(150)
  planDescription                  String                             @default("") @db.VarChar(250)
  slapPrice                        Int                                @default(0)
  discount                         Decimal                            @default(0) @db.Decimal(5, 2)
  jobCredits                       Int                                @default(0)
  databaseCredits                  Int                                @default(0)
  jobValidity                      Int                                @default(0)
  planValidity                     Int                                @default(0)
  refundWindow                     Int                                @default(0)
  gst                              Decimal                            @default(0) @db.Decimal(5, 2)
  isActive                         Boolean                            @default(true)
  planType                         plantype                           @relation(fields: [planTypeId], references: [id])
  planTypeId                       Int
  createdAt                        DateTime                           @default(now())
  updatedAt                        DateTime                           @updatedAt
  recruiterplanpurchasetransaction recruiterplanpurchasetransaction[]
  recruiterlatestplaninfo          recruiterlatestplaninfo[]
  recruitercredittransaction       recruitercredittransaction[]
}

// Plan Type (Regular/Customize)
model plantype {
  id                         Int                          @id @default(autoincrement())
  name                       String                       @default("") @db.VarChar(100)
  isActive                   Boolean                      @default(true)
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  plan                       plan[]
}

// Credit Transaction Type (credit/debit, refund)
model credittransactiontype {
  id                         Int                          @id @default(autoincrement())
  name                       String                       @default("") @db.VarChar(100)
  isActive                   Boolean                      @default(true)
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  recruitercredittransaction recruitercredittransaction[]
}

// Credit spend types (db/job credit)
model creditspendtype {
  id                         Int                          @id @default(autoincrement())
  name                       String                       @default("") @db.VarChar(100)
  isActive                   Boolean                      @default(true)
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  recruitercredittransaction recruitercredittransaction[]
}

// Payment Status type (Success/failed)
model paymentstatus {
  id                               Int                                @id @default(autoincrement())
  name                             String                             @default("") @db.VarChar(100)
  isActive                         Boolean                            @default(true)
  createdAt                        DateTime                           @default(now())
  updatedAt                        DateTime                           @updatedAt
  recruiterplanpurchasetransaction recruiterplanpurchasetransaction[]
}

// Credit Deduction Details
model creditdeductioncriteria {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(100)
  value     Int      @default(0)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// GST Supply type (inter/intra city)
model gstsupplytype {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(30)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  recruiterplanpurchasetransaction recruiterplanpurchasetransaction[]
}

model refundstatus {
  id        Int      @id @default(autoincrement())
  name      String   @default("") @db.VarChar(30)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  recruiterplanpurchasetransaction recruiterplanpurchasetransaction[]
}

// Recruiter Purchase Plan
model recruiterplanpurchasetransaction {
  id                         Int                          @id @default(autoincrement())
  recruiterId                Int
  recruiter                  Recruiter                    @relation(fields: [recruiterId], references: [id])
  internalTransactionId      String                       @unique
  planId                     Int
  plan                       plan                         @relation(fields: [planId], references: [id])
  totalAmountPaid            Int                          @default(0)
  actualAmountPaid           Int                          @default(0)
  discount                   Decimal                      @default(0) @db.Decimal(5, 2)
  totalgst                   Decimal                      @default(0) @db.Decimal(5, 2)
  sgst                       Decimal                      @default(0) @db.Decimal(5, 2)
  cgst                       Decimal                      @default(0) @db.Decimal(5, 2)
  igst                       Decimal                      @default(0) @db.Decimal(5, 2)
  gstSupplyType              gstsupplytype                @relation(fields: [gstSupplyTypeId], references: [id])
  gstSupplyTypeId            Int
  gstin                      String?                      @db.VarChar(255)
  paymentTransactionId       String?                      @unique
  paymentOrderId             String?                      @unique
  paymentMeta                Json?
  paymentMode                String?                      @default("") @db.VarChar(155)           
  paymentStatusId            Int
  paymentStatus              paymentstatus                @relation(fields: [paymentStatusId], references: [id])
  transactionFailedReason    String?                      @db.VarChar(600)
  location                   String                       @default("") @db.VarChar(250)
  refundWindow               Int                          @default(0)
  refundEligibility          Boolean                      @default(false)
  refundStatus               refundstatus                 @relation(fields: [refundStatusId], references: [id])
  refundStatusId             Int
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  recruiterlatestplaninfo    recruiterlatestplaninfo?
  recruitercredittransaction recruitercredittransaction[]
  Job                        Job[]
  recruitercreditrequest     recruitercreditrequest[]
  invoice                    recruiterinvoice[]
}

// Recruiter Credit Usage Transaction Table
model recruitercredittransaction {
  id                        Int                              @id @default(autoincrement())
  creditTransactionTypeId   Int
  creditTransactionType     credittransactiontype            @relation(fields: [creditTransactionTypeId], references: [id])
  creditSpendTypeId         Int
  creditSpendType           creditspendtype                  @relation(fields: [creditSpendTypeId], references: [id])
  jobCredit                 Int
  databaseCredit            Int
  recruiterId               Int
  recruiter                 Recruiter                        @relation(fields: [recruiterId], references: [id])
  creditTransactionId       String                           @unique
  planId                    Int
  plan                      plan                             @relation(fields: [planId], references: [id])
  planPurchaseTransactionId Int
  planPurchaseTransaction   recruiterplanpurchasetransaction @relation(fields: [planPurchaseTransactionId], references: [id])
  job                       Job?                             @relation(fields: [jobId], references: [id])
  jobId                     Int?
  createdAt                 DateTime                         @default(now())
  updatedAt                 DateTime                         @updatedAt
}

// Recruiter latest plan info
model recruiterlatestplaninfo {
  id                       Int                              @id @default(autoincrement())
  recruiterId              Int                              @unique
  recruiter                Recruiter                        @relation(fields: [recruiterId], references: [id])
  planId                   Int
  plan                     plan                             @relation(fields: [planId], references: [id])
  currentPlanTransactionId Int                              @unique
  currentPlanTransaction   recruiterplanpurchasetransaction @relation(fields: [currentPlanTransactionId], references: [id])
  currentJobCredits        Int                              @default(0)
  currentDBCredtis         Int                              @default(0)
  previousCarryJobCredits  Int                              @default(0)
  previousCarryDBCredits   Int                              @default(0)
  currentJobPostValidity   DateTime
  currentPlanValidity      DateTime
  isPlanActive             Boolean                          @default(true)
  createdAt                DateTime                         @default(now())
  updatedAt                DateTime                         @updatedAt
}

// Credit Request Approval Status
model creditrequestapprovestatus {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @default("") @db.VarChar(100)
  isActive               Boolean                  @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  recruitercreditrequest recruitercreditrequest[]
}

// Credit Request Type (DB/Job/JOB&DB credit)
model creditrequesttype {
  id                         Int                          @id @default(autoincrement())
  name                       String                       @default("") @db.VarChar(100)
  isActive                   Boolean                      @default(true)
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  recruitercreditrequest     recruitercreditrequest[]
}

// Recruiter Credit Request
model recruitercreditrequest {
  id                           Int                              @id @default(autoincrement())
  recruiterId                  Int
  recruiter                    Recruiter                        @relation(fields: [recruiterId], references: [id])
  crmUserId                    Int?
  crmUser                      Crmuser?                         @relation(fields: [crmUserId], references: [id])
  creditRequestApproveStatusId Int
  creditRequestApproveStatus   creditrequestapprovestatus       @relation(fields: [creditRequestApproveStatusId], references: [id])
  creditRequestTypeId          Int
  creditRequestType            creditrequesttype                @relation(fields: [creditRequestTypeId], references: [id])
  currentPlanTransactionId     Int
  currentPlanTransaction       recruiterplanpurchasetransaction @relation(fields: [currentPlanTransactionId], references: [id])
  creditRejectReason           String?                          @default("")
  jobCredit                    Int                              @default(0)
  databaseCredit               Int                              @default(0)
  lastCreditRequestDate        DateTime?
  createdAt                    DateTime                         @default(now())
  updatedAt                    DateTime                         @updatedAt
}

// Invoice

// Invoice Document Type (Inv/Credit note)
model invoicedoctype {
  id                         Int                          @id @default(autoincrement())
  name                       String                       @default("") @db.VarChar(100)
  value                      String                       @default("") @db.VarChar(50)
  isActive                   Boolean                      @default(true)
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  recruiterinvoice           recruiterinvoice[]
}

// Invoice Table
model recruiterinvoice {
  id                           Int                              @id @default(autoincrement())
  sellerGstin                  String                           @default("") @db.VarChar(255)
  sellerCin                    String                           @default("") @db.VarChar(255)
  buyerGstin                   String?                          @db.VarChar(255)
  invoiceDocTypeId             Int
  invoiceDocType               invoicedoctype                   @relation(fields: [invoiceDocTypeId], references: [id])
  currentPlanTransactionId     Int
  currentPlanTransaction       recruiterplanpurchasetransaction @relation(fields: [currentPlanTransactionId], references: [id])
  invoiceDocDate               DateTime                         @default(now())
  udId                         String?                          @db.VarChar(255)
  loadId                       String?                          @db.VarChar(255)
  irnNo                        String?                          @db.VarChar(255)
  ackNo                        String?                          @db.VarChar(255)
  ackDate                      DateTime                         @default(now())
  signedInvoice                String?                          @db.Text
  signedQr                     String?                          @db.Text
  qrBase64encoded              String?                          @db.Text
  invoiceMeta                  Json?
  generatedInvoiceUrl          String?                          @db.VarChar(255)         
  createdAt                    DateTime                         @default(now())
  updatedAt                    DateTime                         @updatedAt
}