/*
  Warnings:

  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.

*/
-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- CreateTable
CREATE TABLE `crmuser` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `mobileNumber` VARCHAR(255) NULL,
    `password` VARCHAR(255) NOT NULL,
    `roleId` INTEGER NOT NULL DEFAULT 2,
    `emailHash` VARCHAR(255) NULL,
    `mobileHash` VARCHAR(255) NULL,

    UNIQUE INDEX `crmuser_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `crmuserrole` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `crmuser` ADD CONSTRAINT `crmuser_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `crmuserrole`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
