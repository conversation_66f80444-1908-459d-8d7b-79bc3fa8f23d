// Update personal verification status
export interface UpdatePersonalVerifyInput {
  recruiterId: string;
  verifyStatus: boolean;
  personalRejectReason: string;
}

export interface UpdatePersonalVerifyArgs<T> {
  input: T;
}

interface VerifyDocumentDetails {
  aadhaarNumber: string;
  panNumber: string;
  gstin: string;
  fssai: string
  udyogAadhaar: string
  personalstatusId: string
  companystatusId: string
  personalDocGivenId: string
  companyDocGivenId: string
  updatedAt: string
}

export interface UpdatePersonalVerifyPayload {
  UpdatePersonalVerifyError: {
    message: string;
  }[];
  message: string | null;
  status: number;
  // data: VerifyDocumentDetails
}

// Update Company verification status
export interface UpdateCompanyVerifyInput {
  recruiterId: string;
  verifyStatus: boolean;
  companyRejectReason: string;
  comapnyListId: number;
  companyId: number;
  companyNameStatus?: boolean;
}

export interface UpdateCompanyVerifyArgs<T> {
  input: T;
}

interface VerifyDocumentDetails {
  aadhaarNumber: string;
  panNumber: string;
  gstin: string;
  fssai: string
  udyogAadhaar: string
  personalstatusId: string
  companystatusId: string
  personalDocGivenId: string
  companyDocGivenId: string
  updatedAt: string
}

export interface UpdateCompanyVerifyPayload {
  UpdateCompanyVerifyError: {
    message: string;
  }[];
  message: string | null;
  status: number;
  data: VerifyDocumentDetails
}

// Verify Requested Job
export interface UpdateJobVerificationInput {
  recruiterId: string;
  jobId: string;
  verifyStatus: boolean;
  jobRejectReason: string;
  // jobTitleId: number;
  // jobTitleStatus ?: boolean;
}

export interface UpdateJobVerificationArgs<T> {
  input: T;
}

export interface UpdateJobVerificationPayload {
  UpdateJobVerificationError: {
    message: string;
  }[];
  message: string | null;
  status: number;
}


export interface UpdateCompanyStatusInput {
  recruiterId: string;
  companyId:  number;
}

export interface UpdateCompanyStatusArgs<T> {
  input: T;
}

export interface UpdateCompanyStatusPayload {
  updateCompanyStatusErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}

export interface BlockRecruiterInput {
  recruiterId: number;
  recruiterStatus: string;
}

export interface BlockRecruiterArgs<T> {
  input: T;
}

export interface BlockRecruiterPayload {
  blockRecruiterErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}



export interface JobDetailInput {
  // candidateProfile: JobCandidatePref;
  // interview: JobInterviewPref;
  companyName: string,
  companyId: number,
  companyTypeId: number,
  companyListId: number,
  consultantId: number,
  jobStatusId: number,
  jobTitle: string,
  jobTitleId:number,
  jobCategoryId: number,
  jobLocation: string,
  lat: string,
  lng: string,
  numberOpenings: number,
  workTypeId: number,
  employmentTypeId: number,
  jobUrgencyId: number,
  jobDescription: string,
  minMonthSalary: number,
  maxMonthSalary: number,
  isIncentives: boolean,
  avgMonthlyIncentive: number,
  jobBenefits: number[],
  shiftTypeId: number,
  workingDaysId: number,
  feesDeposit: boolean,
  depositAmount: number,
  depositReasonId: number,
  additionalInformation: string,
  buildingName: string,
  area:string,
  city:string,
  state:string,
  zipcode:string
}

// Candidate Preference Details
export interface CandidatePreferenceInput {
  job_id: number;
  gender: number;
  education: number;
  workExperience: number;
  minExperience?: number;
  maxExperience?: number;
  applicationRadius: number;
  englishKnowledge: number;
  skills: number[];
  isAssetsRequired: boolean;
  requiredAssets: number[];
}

interface faceInterview {
  buildingName:string;
  lat: string;
  lng: string;
  fullAddress: string;
}

interface walkInterview {
  walkinStartDate: string
  duration : number
  walkinStartTime: string
  walkinEndTime: string    
  location: string
  buildingName:string;
  lat: string;
  lng: string;
}

// Interview Details
export interface InterviewDetailsInput {
  job_id: number;
  interviewId: number;
  hrName:string;
  hrContactNumber: string;
  interviewType: number;
  faceInterview?: faceInterview
  walkIn?: walkInterview
}

export interface UpdateJobDetailInput {
  jobId: string;
  jobTitle: string;
  jobCategoryId: string;
  jobLocation: string;
  latitude: string;
  longitude: string;
  numberOpenings: number;
  workTypeId: string;
  employmentTypeId: string;
  jobUrgencyId: string;
  jobDescription: string;
  minMonthSalary: number;
  maxMonthSalary: number;
  isIncentives: boolean;
  avgMonthlyIncentive: number;
  jobBenefits: number[];
  shiftTypeId: string;
  workingDaysId: string;
  feesDeposit: boolean;
  depositAmount: number;
  depositReasonId: string;
  additionalInformation: string;
  buildingName: string;
  area:string;
  city:string;
  state:string;
  zipcode:string;
}

export interface UpdateJobInput {
  job_id: number;
  jobPref: JobDetailInput;
  candidatePref: CandidatePreferenceInput;
  interviewPref: InterviewDetailsInput;
  // isJobEdited: boolean;
  // titleDetails?: TitleDetails;
}

export interface TitleDetails {
  jobTitle: string; 
  jobId: number; 
  jobRoleId: number;
  jobTitleId?: number;
  updatedTitleId?: number
  jobTitleStatus?: boolean
}

export interface UpdateJobArgs<T> {
  input: T;
}

export interface UpdateJobPayload {
  updateJobError: {
    message: string;
  }[];
  message: string | null;
  status: number;
}



export interface companyNameInput {
  companyListId: number;
  companyName: string; 
  companyId: number; 
  updatedCompanyListID?: number;
  removedCompanyListId?: number
}

export interface companyNameArgs<T> {
  input: T;
}

export interface companyNamePayload {
  companyNameErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}

export interface jobTitleInput {
  jobTitleId: number; 
  jobRoleId: number; 
  jobTitle: string; 
  jobId: number; 
  updatedJobTitleId?: number; 
  removedJobTitleId?: number;
}

export interface jobTitleArgs<T> {
  input: T;
}

export interface jobTitlePayload {
  jobTitleErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}


export interface ReActiveJobInput {
  jobId: number; 
  
}

export interface ReActiveJobArgs<T> {
  input: T;
}

export interface ReActiveJobPayload {
  ReActiveJobErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}