-- AlterTable
ALTER TABLE `job` ADD COLUMN `createdAt` DATETIME(3) NULL,
    ADD COLUMN `jobExpiry` DATETIME(3) NULL,
    ADD COLUMN `recruiterPlanpurchaseTransactionId` INTEGER NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NULL;

-- CreateTable
CREATE TABLE `plan` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `planName` VARCHAR(100) NOT NULL DEFAULT '',
    `planTitle` VARCHAR(150) NOT NULL DEFAULT '',
    `planDescription` VARCHAR(250) NOT NULL DEFAULT '',
    `slapPrice` INTEGER NOT NULL DEFAULT 0,
    `discount` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `jobCredits` INTEGER NOT NULL DEFAULT 0,
    `databaseCredits` INTEGER NOT NULL DEFAULT 0,
    `jobValidity` INTEGER NOT NULL DEFAULT 0,
    `planValidity` INTEGER NOT NULL DEFAULT 0,
    `refundWindow` INTEGER NOT NULL DEFAULT 0,
    `gst` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `planTypeId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `plantype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `credittransactiontype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `creditspendtype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `paymentstatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `creditdeductioncriteria` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `value` INTEGER NOT NULL DEFAULT 0,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `gstsupplytype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `refundstatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterplanpurchasetransaction` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `internalTransactionId` VARCHAR(191) NOT NULL,
    `planId` INTEGER NOT NULL,
    `totalAmountPaid` INTEGER NOT NULL DEFAULT 0,
    `actualAmountPaid` INTEGER NOT NULL DEFAULT 0,
    `discount` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `totalgst` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `sgst` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `cgst` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `igst` DECIMAL(5, 2) NOT NULL DEFAULT 0,
    `gstSupplyTypeId` INTEGER NOT NULL,
    `gstin` VARCHAR(255) NULL,
    `paymentTransactionId` VARCHAR(191) NULL,
    `paymentOrderId` VARCHAR(191) NULL,
    `paymentMeta` JSON NULL,
    `paymentMode` VARCHAR(155) NULL DEFAULT '',
    `paymentStatusId` INTEGER NOT NULL,
    `transactionFailedReason` VARCHAR(600) NULL,
    `location` VARCHAR(250) NOT NULL DEFAULT '',
    `refundWindow` INTEGER NOT NULL DEFAULT 0,
    `refundEligibility` BOOLEAN NOT NULL DEFAULT false,
    `refundStatusId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `recruiterplanpurchasetransaction_internalTransactionId_key`(`internalTransactionId`),
    UNIQUE INDEX `recruiterplanpurchasetransaction_paymentTransactionId_key`(`paymentTransactionId`),
    UNIQUE INDEX `recruiterplanpurchasetransaction_paymentOrderId_key`(`paymentOrderId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruitercredittransaction` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `creditTransactionTypeId` INTEGER NOT NULL,
    `creditSpendTypeId` INTEGER NOT NULL,
    `jobCredit` INTEGER NOT NULL,
    `databaseCredit` INTEGER NOT NULL,
    `recruiterId` INTEGER NOT NULL,
    `creditTransactionId` VARCHAR(191) NOT NULL,
    `planId` INTEGER NOT NULL,
    `planPurchaseTransactionId` INTEGER NOT NULL,
    `jobId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `recruitercredittransaction_creditTransactionId_key`(`creditTransactionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterlatestplaninfo` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `planId` INTEGER NOT NULL,
    `currentPlanTransactionId` INTEGER NOT NULL,
    `currentJobCredits` INTEGER NOT NULL DEFAULT 0,
    `currentDBCredtis` INTEGER NOT NULL DEFAULT 0,
    `previousCarryJobCredits` INTEGER NOT NULL DEFAULT 0,
    `previousCarryDBCredits` INTEGER NOT NULL DEFAULT 0,
    `currentJobPostValidity` DATETIME(3) NOT NULL,
    `currentPlanValidity` DATETIME(3) NOT NULL,
    `isPlanActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `recruiterlatestplaninfo_recruiterId_key`(`recruiterId`),
    UNIQUE INDEX `recruiterlatestplaninfo_currentPlanTransactionId_key`(`currentPlanTransactionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `creditrequestapprovestatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `creditrequesttype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruitercreditrequest` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `crmUserId` INTEGER NULL,
    `creditRequestApproveStatusId` INTEGER NOT NULL,
    `creditRequestTypeId` INTEGER NOT NULL,
    `currentPlanTransactionId` INTEGER NOT NULL,
    `creditRejectReason` VARCHAR(191) NULL DEFAULT '',
    `jobCredit` INTEGER NOT NULL DEFAULT 0,
    `databaseCredit` INTEGER NOT NULL DEFAULT 0,
    `lastCreditRequestDate` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_recruiterPlanpurchaseTransactionId_fkey` FOREIGN KEY (`recruiterPlanpurchaseTransactionId`) REFERENCES `recruiterplanpurchasetransaction`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `plan` ADD CONSTRAINT `plan_planTypeId_fkey` FOREIGN KEY (`planTypeId`) REFERENCES `plantype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterplanpurchasetransaction` ADD CONSTRAINT `recruiterplanpurchasetransaction_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterplanpurchasetransaction` ADD CONSTRAINT `recruiterplanpurchasetransaction_planId_fkey` FOREIGN KEY (`planId`) REFERENCES `plan`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterplanpurchasetransaction` ADD CONSTRAINT `recruiterplanpurchasetransaction_gstSupplyTypeId_fkey` FOREIGN KEY (`gstSupplyTypeId`) REFERENCES `gstsupplytype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterplanpurchasetransaction` ADD CONSTRAINT `recruiterplanpurchasetransaction_paymentStatusId_fkey` FOREIGN KEY (`paymentStatusId`) REFERENCES `paymentstatus`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterplanpurchasetransaction` ADD CONSTRAINT `recruiterplanpurchasetransaction_refundStatusId_fkey` FOREIGN KEY (`refundStatusId`) REFERENCES `refundstatus`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercredittransaction` ADD CONSTRAINT `recruitercredittransaction_creditTransactionTypeId_fkey` FOREIGN KEY (`creditTransactionTypeId`) REFERENCES `credittransactiontype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercredittransaction` ADD CONSTRAINT `recruitercredittransaction_creditSpendTypeId_fkey` FOREIGN KEY (`creditSpendTypeId`) REFERENCES `creditspendtype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercredittransaction` ADD CONSTRAINT `recruitercredittransaction_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercredittransaction` ADD CONSTRAINT `recruitercredittransaction_planId_fkey` FOREIGN KEY (`planId`) REFERENCES `plan`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercredittransaction` ADD CONSTRAINT `recruitercredittransaction_planPurchaseTransactionId_fkey` FOREIGN KEY (`planPurchaseTransactionId`) REFERENCES `recruiterplanpurchasetransaction`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercredittransaction` ADD CONSTRAINT `recruitercredittransaction_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterlatestplaninfo` ADD CONSTRAINT `recruiterlatestplaninfo_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterlatestplaninfo` ADD CONSTRAINT `recruiterlatestplaninfo_planId_fkey` FOREIGN KEY (`planId`) REFERENCES `plan`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterlatestplaninfo` ADD CONSTRAINT `recruiterlatestplaninfo_currentPlanTransactionId_fkey` FOREIGN KEY (`currentPlanTransactionId`) REFERENCES `recruiterplanpurchasetransaction`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercreditrequest` ADD CONSTRAINT `recruitercreditrequest_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercreditrequest` ADD CONSTRAINT `recruitercreditrequest_crmUserId_fkey` FOREIGN KEY (`crmUserId`) REFERENCES `crmuser`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercreditrequest` ADD CONSTRAINT `recruitercreditrequest_creditRequestApproveStatusId_fkey` FOREIGN KEY (`creditRequestApproveStatusId`) REFERENCES `creditrequestapprovestatus`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercreditrequest` ADD CONSTRAINT `recruitercreditrequest_creditRequestTypeId_fkey` FOREIGN KEY (`creditRequestTypeId`) REFERENCES `creditrequesttype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercreditrequest` ADD CONSTRAINT `recruitercreditrequest_currentPlanTransactionId_fkey` FOREIGN KEY (`currentPlanTransactionId`) REFERENCES `recruiterplanpurchasetransaction`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
