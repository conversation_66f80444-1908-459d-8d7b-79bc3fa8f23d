-- AlterTable
ALTER TABLE `recruiteruserdetails` ADD COLUMN `otherReportReason` VARCHAR(255) NULL,
    ADD COLUMN `recruiterReportCandidateId` INTEGER NULL;

-- CreateTable
CREATE TABLE `recruiterreportcandidate` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(70) NOT NULL DEFAULT '',
    `name_en` VARCHAR(70) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(70) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_recruiterReportCandidateId_fkey` FOREIGN KEY (`recruiterReportCandidateId`) REFERENCES `recruiterreportcandidate`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
