-- CreateTable
CREATE TABLE `crmpermission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL,
    `description` VARCHAR(255) NULL,
    `resource` VARCHAR(50) NOT NULL,
    `action` VARCHAR(20) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `crmpermission_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `crmrolepermission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `roleId` INTEGER NOT NULL,
    `permissionId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `crmrolepermission_roleId_permissionId_key`(`roleId`, `permissionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddColumn
ALTER TABLE `crmuserrole` ADD COLUMN `description` VARCHAR(255) NULL;

-- AddForeignKey
ALTER TABLE `crmrolepermission` ADD CONSTRAINT `crmrolepermission_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `crmuserrole`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `crmrolepermission` ADD CONSTRAINT `crmrolepermission_permissionId_fkey` FOREIGN KEY (`permissionId`) REFERENCES `crmpermission`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
