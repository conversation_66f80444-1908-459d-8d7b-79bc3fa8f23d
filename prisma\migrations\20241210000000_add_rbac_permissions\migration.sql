-- CreateTable for CRM Permissions
CREATE TABLE `crmpermission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL,
    `description` VARCHAR(255) NULL,
    `resource` VARCHAR(50) NOT NULL,
    `action` VARCHAR(20) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `crmpermission_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable for CRM Role-Permission Mapping
CREATE TABLE `crmrolepermission` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `roleId` INTEGER NOT NULL,
    `permissionId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `crmrolepermission_roleId_permissionId_key`(`roleId`, `permissionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add description column to existing crmuserrole table
ALTER TABLE `crmuserrole` ADD COLUMN `description` VARCHAR(255) NULL;

-- Insert CRM Roles (following your seeding pattern)
INSERT INTO `crmuserrole` (`id`, `name`, `description`, `isActive`, `createdAt`, `updatedAt`) VALUES
(1, 'Admin', 'System Administrator with full access', true, NOW(), NOW()),
(2, 'User', 'Regular CRM user with limited access', true, NOW(), NOW())
ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `description` = VALUES(`description`),
    `isActive` = VALUES(`isActive`),
    `updatedAt` = NOW();

-- Insert CRM Permissions (following your seeding pattern)
INSERT INTO `crmpermission` (`id`, `name`, `description`, `resource`, `action`, `isActive`, `createdAt`, `updatedAt`) VALUES
-- User Management Permissions
(1, 'user.create', 'Create new CRM users', 'user', 'create', true, NOW(), NOW()),
(2, 'user.read', 'View CRM user details', 'user', 'read', true, NOW(), NOW()),
(3, 'user.update', 'Update CRM user information', 'user', 'update', true, NOW(), NOW()),
(4, 'user.delete', 'Delete CRM users', 'user', 'delete', true, NOW(), NOW()),
(5, 'user.assign_role', 'Assign roles to CRM users', 'user', 'assign_role', true, NOW(), NOW()),

-- Recruiter Management Permissions
(6, 'recruiter.read', 'View recruiter profiles and details', 'recruiter', 'read', true, NOW(), NOW()),
(7, 'recruiter.update', 'Update recruiter information', 'recruiter', 'update', true, NOW(), NOW()),
(8, 'recruiter.approve', 'Approve recruiter verification', 'recruiter', 'approve', true, NOW(), NOW()),
(9, 'recruiter.reject', 'Reject recruiter verification', 'recruiter', 'reject', true, NOW(), NOW()),
(10, 'recruiter.block', 'Block/unblock recruiters', 'recruiter', 'block', true, NOW(), NOW()),

-- Company Management Permissions
(11, 'company.read', 'View company details', 'company', 'read', true, NOW(), NOW()),
(12, 'company.update', 'Update company information', 'company', 'update', true, NOW(), NOW()),
(13, 'company.approve', 'Approve company verification', 'company', 'approve', true, NOW(), NOW()),
(14, 'company.reject', 'Reject company verification', 'company', 'reject', true, NOW(), NOW()),

-- Job Management Permissions
(15, 'job.read', 'View job postings', 'job', 'read', true, NOW(), NOW()),
(16, 'job.update', 'Update job postings', 'job', 'update', true, NOW(), NOW()),
(17, 'job.approve', 'Approve job postings', 'job', 'approve', true, NOW(), NOW()),
(18, 'job.reject', 'Reject job postings', 'job', 'reject', true, NOW(), NOW()),
(19, 'job.delete', 'Delete job postings', 'job', 'delete', true, NOW(), NOW()),

-- Role Management Permissions
(20, 'role.read', 'View roles and permissions', 'role', 'read', true, NOW(), NOW()),
(21, 'role.create', 'Create new roles', 'role', 'create', true, NOW(), NOW()),
(22, 'role.update', 'Update role permissions', 'role', 'update', true, NOW(), NOW()),
(23, 'role.delete', 'Delete roles', 'role', 'delete', true, NOW(), NOW()),

-- System Management Permissions
(24, 'system.admin', 'Full system administration access', 'system', 'admin', true, NOW(), NOW()),
(25, 'reports.read', 'View system reports and analytics', 'reports', 'read', true, NOW(), NOW()),
(26, 'notifications.send', 'Send notifications to users', 'notifications', 'send', true, NOW(), NOW()),

-- Request Callback Permissions
(27, 'callback.read', 'View callback requests', 'callback', 'read', true, NOW(), NOW()),
(28, 'callback.update', 'Update callback request status', 'callback', 'update', true, NOW(), NOW())

ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `description` = VALUES(`description`),
    `resource` = VALUES(`resource`),
    `action` = VALUES(`action`),
    `isActive` = VALUES(`isActive`),
    `updatedAt` = NOW();

-- Insert Role-Permission Mappings (following your seeding pattern)
INSERT INTO `crmrolepermission` (`roleId`, `permissionId`, `isActive`, `createdAt`, `updatedAt`) VALUES
-- Admin Role (roleId: 1) - Full access to everything
(1, 1, true, NOW(), NOW()), (1, 2, true, NOW(), NOW()), (1, 3, true, NOW(), NOW()), (1, 4, true, NOW(), NOW()),
(1, 5, true, NOW(), NOW()), (1, 6, true, NOW(), NOW()), (1, 7, true, NOW(), NOW()), (1, 8, true, NOW(), NOW()),
(1, 9, true, NOW(), NOW()), (1, 10, true, NOW(), NOW()), (1, 11, true, NOW(), NOW()), (1, 12, true, NOW(), NOW()),
(1, 13, true, NOW(), NOW()), (1, 14, true, NOW(), NOW()), (1, 15, true, NOW(), NOW()), (1, 16, true, NOW(), NOW()),
(1, 17, true, NOW(), NOW()), (1, 18, true, NOW(), NOW()), (1, 19, true, NOW(), NOW()), (1, 20, true, NOW(), NOW()),
(1, 21, true, NOW(), NOW()), (1, 22, true, NOW(), NOW()), (1, 23, true, NOW(), NOW()), (1, 24, true, NOW(), NOW()),
(1, 25, true, NOW(), NOW()), (1, 26, true, NOW(), NOW()), (1, 27, true, NOW(), NOW()), (1, 28, true, NOW(), NOW()),

-- User Role (roleId: 2) - Limited access for regular CRM users
(2, 2, true, NOW(), NOW()), (2, 3, true, NOW(), NOW()), (2, 6, true, NOW(), NOW()), (2, 7, true, NOW(), NOW()),
(2, 11, true, NOW(), NOW()), (2, 12, true, NOW(), NOW()), (2, 15, true, NOW(), NOW()), (2, 16, true, NOW(), NOW()),
(2, 20, true, NOW(), NOW()), (2, 27, true, NOW(), NOW()), (2, 28, true, NOW(), NOW())

ON DUPLICATE KEY UPDATE
    `isActive` = VALUES(`isActive`),
    `updatedAt` = NOW();

-- Add Foreign Key Constraints
ALTER TABLE `crmrolepermission` ADD CONSTRAINT `crmrolepermission_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `crmuserrole`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `crmrolepermission` ADD CONSTRAINT `crmrolepermission_permissionId_fkey` FOREIGN KEY (`permissionId`) REFERENCES `crmpermission`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
