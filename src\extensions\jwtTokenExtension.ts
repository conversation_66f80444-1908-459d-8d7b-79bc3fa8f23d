import jwt from "jsonwebtoken";
import { prisma } from "../db";

export const getSignedJwtToken = prisma.$extends({
  name: "Generate JWT Token",
  result: {
    crmuser: {
      user_id: {
        needs: {id: true},
        compute(user) {
          return prisma.crmuser.findUnique({
            where: {
              id: user.id
            },
            include: {
              role: true
            }
          })
        },
      },
    }
  },
  model: {
    crmuser: {
      async getJwtToken(user_id: number) {
        if (process.env.JWT_JJCRM_SECRET && process.env.JWT_EXPIRE) {
          // Get user with role information following your pattern
          const user = await prisma.crmuser.findUnique({
            where: { id: user_id },
            include: { role: true }
          });

          if (!user) {
            throw new Error("User not found");
          }

          const tokenPayload = {
            id: user_id.toString(),
            roleId: user.roleId,
            roleName: user.role.name,
            email: user.email
          };

          return jwt.sign(tokenPayload, process.env.JWT_JJCRM_SECRET, {
            expiresIn: process.env.JWT_EXPIRE,
          });
        }
      },
      async currentUser(user_id: number) {
        return await prisma.crmuser.findUnique({
          where: {id: user_id},
          include: {
            role: true
          }
        })
      }
    },
  },
});

