import jwt from "jsonwebtoken";
import { prisma } from "../db";

export const getSignedJwtToken = prisma.$extends({
  name: "Generate JWT Token",
  result: {
    crmuser: {
      user_id: {
        needs: {id: true},
        compute(user) {
          return prisma.crmuser.findUnique({
            where: {
              id: user.id
            }
          })
        },
      },
    }
  },
  model: {
    crmuser: {
      async getJwtToken(user_id: number) {
        if (process.env.JWT_JJCRM_SECRET && process.env.JWT_EXPIRE) {
          console.log("user", user_id)

          return jwt.sign({ id: user_id?.toString() }, process.env.JWT_JJCRM_SECRET, {
            expiresIn: process.env.JWT_EXPIRE,
          });
        }
      },
      async currentUser(user_id: number) {
        return await prisma.crmuser.findUnique({where: {id: user_id}})
      }
    },
  },
});

