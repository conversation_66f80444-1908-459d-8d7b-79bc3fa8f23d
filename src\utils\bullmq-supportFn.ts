import axios from "axios";
import { checkIfVerifiedExists } from "./commonInterFun";
import { sendWhatsAppNotification } from "../services/notification";
import unverifiedRecruiter from "../templates/email/unverifiedRecruiterTemplate";
import MailService from "../services/mailservice";

const SOLR_URL = `${process.env.SOLR_URL}/api/collections/${process.env.SOLR_JOB_COLLECTION}/update/json`;

// Function to handle Solr Job
export const handleSolrJob = async (jobData: any) => {
  if (await checkIfVerifiedExists(jobData)) {
    try {
      const response = await axios.post(SOLR_URL, jobData);
      console.log("Solr update response:", response.data);
    } catch (error: any) {
      console.error("Solr job error:", error.response?.data || error.message);
    }
  } else {
    console.log("Solr job: No verified data found");
  }
};

// Function to handle WhatsApp notification
export const handleWhatsappNotification = async (value: any) => {
  const {recDetails, templateName} = value

  if (recDetails && recDetails.phone && templateName) {
    try {
      await sendWhatsAppNotification(recDetails.phone, recDetails, templateName);
      console.log(`WhatsApp message sent to ${recDetails.phone}`);
    } catch (error) {
      console.error(`Failed to send WhatsApp message to ${recDetails.phone}`, error);
    }
  } else {
    console.log("WhatsApp job: Recruiter data is empty or missing mobile number");
  }
};


export const handleEmailNotification = async (value: any) => {
  const {recDetails, templateName} = value;

  console.log("====recDetails====emailnoti", recDetails)

  if (recDetails && recDetails.email) {
    try {
      const emailTemplate = unverifiedRecruiter(
        recDetails.name
      );
      const mailService = MailService.getInstance();
      
      const one = await mailService.sendMail(1234, {
        from: process.env.SMTP_SENDER,
        to: recDetails.email,
        cc: process.env.CCMAIL,
        subject: "Unverified Recruiter - Email",
        html: emailTemplate.html,
        dsn: {
          id: '123456',
          return: 'headers',
          notify: ['success', 'failure', 'delay'],
          recipient: '<EMAIL>'
        }
      });

      console.log("===one===", one)
      
      console.log(`Email sent to ${recDetails.email}`);
    } catch (error) {
      console.error(`Failed to send Email to ${recDetails.email}`, error);
    }
  } else {
    console.log("Email job: Recruiter data is empty or missing email");
  }
};
