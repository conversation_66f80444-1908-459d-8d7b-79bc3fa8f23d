export interface GetJobPreferencePayload {
  id: number; 
  userId: number; 
  city: string | null; 
  area: string | null; 
  distanceId: number | null; 
  createdAt: Date; 
  updatedAt: Date; 
}

export interface GetUserData {
  id: number;
  mobileNumber: string;
  profilePicture: string | null;
  name: string | null;
  dateOfBirth: Date | null;
  whatsAppUpdate: boolean;
  gender: string | null;
  mobileNumberHash: string | null;
  email: string | null;
  UserRating: number | null;
  location: any;
  latitude: any;
  longitude: any;
  twoWheelerLicense: boolean
  coachMarkStatus: boolean
  deleteFeedbackId: number | null;
  userStatusId: number | null;
  jobzAppStatusId: number | null;
  languageId: number | null;
  pauseUntil: Date | null;
  createdAt: Date; 
  updatedAt: Date; 
}

export interface UserProfessional {
  id: number;
  userId: number;
  workExperience: string | null;
  experienceYear: number | null;
  experienceMonth: number | null;
  monthlySalary: number | null;
  joinPreferenceId: number | null;
  previousJobTitle: string | null;
  companyName: string | null;
  englishKnowledgeId: number | null;
  resume: string | null;
  professionalSummary: string | null;
  createdAt: Date; 
  updatedAt: Date;
}

export interface UserEducation {
  id: number;
  userId: number;
  qualificationId: number | null;
  courseId: number | null;
  specializationId: number | null;
  higherSecondaryPercentage: number | null;
  graduatePercentage: string | null;
  degreeModeId: number | null;
  createdAt: Date; 
  updatedAt: Date;
}

export interface UserFeedback {
  id: number; 
  userId: number; 
  reviewId: number | null; 
  suggestionId: number | null; 
  createdAt: Date; 
  updatedAt: Date; 
}

export interface RecDetails {
  name: string; 
  email?: string | null; 
  mobileNumber: string; 
  jobTitle?: string; 
  companyName?: string;
}