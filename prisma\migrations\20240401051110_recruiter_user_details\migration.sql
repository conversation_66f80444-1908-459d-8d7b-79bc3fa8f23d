/*
  Warnings:

  - You are about to drop the column `shortlisted` on the `userjobmapping` table. All the data in the column will be lost.
  - You are about to drop the column `profileViewed` on the `userjobviewmapping` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `userjobmapping` DROP COLUMN `shortlisted`;

-- AlterTable
ALTER TABLE `userjobviewmapping` DROP COLUMN `profileViewed`;

-- CreateTable
CREATE TABLE `recruiteruserdetails` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `shortlisted` <PERSON>O<PERSON>EAN NOT NULL DEFAULT false,
    `shortlistedDate` DATETIME(3) NULL,
    `profileViewed` BOOLEAN NOT NULL DEFAULT false,
    `profileViewedDate` DATETIME(3) NULL,

    UNIQUE INDEX `recruiteruserdetails_userId_recruiterId_jobId_key`(`userId`, `recruiterId`, `jobId`),
    PRIMARY KEY (`id`, `userId`, `jobId`, `recruiterId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
