import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function verifyRBACSetup() {
  try {
    console.log("🔍 Verifying RBAC Setup from Migration");
    console.log("=====================================");

    // Check if roles exist
    console.log("\n1. Checking CRM Roles:");
    const roles = await prisma.crmuserrole.findMany({
      where: { isActive: true },
      select: { id: true, name: true, description: true }
    });
    
    console.log(`Found ${roles.length} roles:`);
    roles.forEach(role => {
      console.log(`  - ID: ${role.id}, Name: ${role.name}, Description: ${role.description}`);
    });

    // Check if permissions exist
    console.log("\n2. Checking CRM Permissions:");
    const permissions = await prisma.crmPermission.findMany({
      where: { isActive: true },
      select: { id: true, name: true, resource: true, action: true }
    });
    
    console.log(`Found ${permissions.length} permissions:`);
    permissions.slice(0, 5).forEach(permission => {
      console.log(`  - ${permission.name} (${permission.resource}.${permission.action})`);
    });
    if (permissions.length > 5) {
      console.log(`  ... and ${permissions.length - 5} more permissions`);
    }

    // Check role-permission mappings
    console.log("\n3. Checking Role-Permission Mappings:");
    const adminPermissions = await prisma.crmRolePermission.findMany({
      where: { roleId: 1, isActive: true },
      include: { permission: true }
    });
    
    const userPermissions = await prisma.crmRolePermission.findMany({
      where: { roleId: 2, isActive: true },
      include: { permission: true }
    });

    console.log(`Admin role has ${adminPermissions.length} permissions`);
    console.log(`User role has ${userPermissions.length} permissions`);

    // Verify specific permissions
    console.log("\n4. Verifying Key Permissions:");
    const keyPermissions = [
      'user.create', 'user.read', 'user.update', 'user.delete',
      'recruiter.read', 'company.read', 'job.read', 'system.admin'
    ];

    for (const permName of keyPermissions) {
      const perm = await prisma.crmPermission.findUnique({
        where: { name: permName }
      });
      console.log(`  ${permName}: ${perm ? '✅ Found' : '❌ Missing'}`);
    }

    // Check if admin has all permissions
    console.log("\n5. Verifying Admin Access:");
    const adminHasAllPerms = adminPermissions.length === permissions.length;
    console.log(`Admin has all permissions: ${adminHasAllPerms ? '✅ Yes' : '❌ No'}`);

    // Check if user has limited permissions
    console.log("\n6. Verifying User Access:");
    const userHasLimitedPerms = userPermissions.length < permissions.length;
    console.log(`User has limited permissions: ${userHasLimitedPerms ? '✅ Yes' : '❌ No'}`);

    // Summary
    console.log("\n📊 RBAC Setup Summary:");
    console.log(`  - Roles: ${roles.length} (Expected: 2)`);
    console.log(`  - Permissions: ${permissions.length} (Expected: 28)`);
    console.log(`  - Admin Permissions: ${adminPermissions.length}`);
    console.log(`  - User Permissions: ${userPermissions.length}`);

    if (roles.length === 2 && permissions.length === 28 && adminHasAllPerms && userHasLimitedPerms) {
      console.log("\n✅ RBAC Setup is COMPLETE and CORRECT!");
      console.log("🚀 You can now create admin users and start using the system.");
    } else {
      console.log("\n⚠️  RBAC Setup has issues. Please check the migration.");
    }

  } catch (error) {
    console.error("❌ Error verifying RBAC setup:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
if (require.main === module) {
  verifyRBACSetup();
}

export { verifyRBACSetup };
