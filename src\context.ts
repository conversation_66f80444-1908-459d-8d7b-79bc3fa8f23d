import { PrismaClient } from "@prisma/client";
import { prisma } from "./db";
import { Request, Response } from "express";

export interface Context {
  req: Request;
  res: Response;
  // prisma: PrismaClient;
  prisma: typeof prisma; // replaced the prismaClient by typeof prisma due to replication db
}

export const createContext = async (param: { req: Request; res: Response }) => {
  return {
    req: param.req,
    res: param.res,
    prisma,
  };
};
