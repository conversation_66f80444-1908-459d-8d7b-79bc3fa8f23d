import jwt from "jsonwebtoken";
import { prisma } from "../db";
import { GraphQLError } from "graphql";
import { Request } from "express";

// Role constants - Get from database instead of hardcoding
export const getRoles = async () => {
  const roles = await prisma.crmuserrole.findMany({
    where: { isActive: true },
    select: { id: true, name: true }
  });

  const roleMap: { [key: string]: number } = {};
  roles.forEach(role => {
    roleMap[role.name.toUpperCase()] = role.id;
  });

  return roleMap;
};

export type UserRole = number;

// Protect routes - Basic authentication following your pattern
export const protect = async (req: Request) => {

  const cookies: any = {};

  if (req.headers.cookie) {
    const cookiesArray = req.headers.cookie.split(";");

    cookiesArray.forEach((cookie) => {
      const [key, value] = cookie.trim().split("=");
      cookies[key] = value;
    });

    req.cookies = cookies;
  }

  let token;
  
  if (req.cookies && req.cookies.jjcrm_token ) {   //taking token from cookies in user
    token = req.cookies.jjcrm_token ;
  } else if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    // Set token from Bearer token in header
    token = req.headers.authorization.split(" ")[1];
    // Set token from cookie
  } else {
    throw new GraphQLError("User is not authenticated to access this route", {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }

  try {
    if (token && process.env.JWT_JJCRM_SECRET) {
      const decoded: any = jwt.verify(token, process.env.JWT_JJCRM_SECRET);

      const user = await prisma.crmuser.findUnique({
        where: {
          id: +decoded.id,
        },
        include: {
          role: true,
        },
      });

      if (!user) {
        throw new GraphQLError("User is not authenticated", {
          extensions: {
            code: "UNAUTHENTICATED",
            http: { status: 401 },
          },
        });
      }

      return user;
    }
  } catch (err) {
    console.log("Not authorized to access this route", err);
    throw new GraphQLError("User is not authenticated", {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }
};

// Role-based authorization middleware following your pattern
export const requireRole = (allowedRoles: UserRole[]) => {
  return async (req: Request) => {
    const user = await protect(req);

    if (!user || !allowedRoles.includes(user.roleId as UserRole)) {
      // Get role names for error message
      const roleNames = await Promise.all(
        allowedRoles.map(async (roleId) => {
          const role = await prisma.crmuserrole.findUnique({
            where: { id: roleId },
            select: { name: true }
          });
          return role?.name || `Role ${roleId}`;
        })
      );

      throw new GraphQLError(`Access denied. Required role: ${roleNames.join(' or ')}`, {
        extensions: {
          code: "FORBIDDEN",
          http: { status: 403 },
        },
      });
    }

    return user;
  };
};

// Dynamic role guards that get role IDs from database
export const requireAdmin = async (req: Request) => {
  const roles = await getRoles();
  return requireRole([roles.ADMIN])(req);
};

export const requireUser = async (req: Request) => {
  const roles = await getRoles();
  return requireRole([roles.USER])(req);
};

export const requireAdminOrUser = async (req: Request) => {
  const roles = await getRoles();
  return requireRole([roles.ADMIN, roles.USER])(req);
};

// Helper functions following your pattern
export const hasRole = (user: any, role: UserRole): boolean => {
  return user.roleId === role;
};

export const isAdmin = async (user: any): Promise<boolean> => {
  const roles = await getRoles();
  return hasRole(user, roles.ADMIN);
};

export const isUser = async (user: any): Promise<boolean> => {
  const roles = await getRoles();
  return hasRole(user, roles.USER);
};

// Synchronous helper functions for when you already know the role IDs
export const isAdminById = (user: any, adminRoleId: number): boolean => {
  return hasRole(user, adminRoleId);
};

export const isUserById = (user: any, userRoleId: number): boolean => {
  return hasRole(user, userRoleId);
};

// Utility function to get role by name
export const getRoleByName = async (roleName: string): Promise<number | null> => {
  try {
    const role = await prisma.crmuserrole.findFirst({
      where: {
        name: roleName,
        isActive: true
      },
      select: { id: true }
    });
    return role?.id || null;
  } catch (error) {
    console.log("Error getting role by name:", error);
    return null;
  }
};

// Utility function to check if user has specific role by name
export const hasRoleName = async (user: any, roleName: string): Promise<boolean> => {
  const roleId = await getRoleByName(roleName);
  return roleId ? hasRole(user, roleId) : false;
};

// Permission checking function for future use
export const hasPermission = async (userId: number, permissionName: string): Promise<boolean> => {
  try {
    const user = await prisma.crmuser.findUnique({
      where: { id: userId }
    });

    if (!user) return false;

    // Get roles from database
    const roles = await getRoles();

    // Simple role-based check - Admin has all permissions, User has limited
    if (user.roleId === roles.ADMIN) {
      return true; // Admin has all permissions
    }

    if (user.roleId === roles.USER) {
      // Define basic user permissions
      const userPermissions = [
        'user.read', 'user.update', 'recruiter.read', 'recruiter.update',
        'company.read', 'company.update', 'job.read', 'job.update',
        'role.read', 'callback.read', 'callback.update'
      ];
      return userPermissions.includes(permissionName);
    }

    return false;
  } catch (error) {
    console.log("Error checking permission:", error);
    return false;
  }
};
