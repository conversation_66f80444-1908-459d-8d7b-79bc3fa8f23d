import jwt from "jsonwebtoken";
import { prisma } from "../db";
import { GraphQLError } from "graphql";
import { Request } from "express";

// Protect routes
export const protect = async (req: Request) => {

  const cookies: any = {};

  if (req.headers.cookie) {
    const cookiesArray = req.headers.cookie.split(";");

    cookiesArray.forEach((cookie) => {
      const [key, value] = cookie.trim().split("=");
      cookies[key] = value;
    });

    req.cookies = cookies;
  }

  let token;
  
  if (req.cookies && req.cookies.jjcrm_token ) {   //taking token from cookies in user
    token = req.cookies.jjcrm_token ;
  } else if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    // Set token from Bearer token in header
    token = req.headers.authorization.split(" ")[1];
    // Set token from cookie
  } else {
    throw new GraphQLError("User is not authenticated to access this route", {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }

  try {
    if (token && process.env.JWT_JJCRM_SECRET) {
      const decoded: any = jwt.verify(token, process.env.JWT_JJCRM_SECRET);

      const user = await prisma.crmuser.findUnique({
        where: {
          id: +decoded.id,
        },
      });

      if (!user) {
        throw new GraphQLError("User is not authenticated", {
          extensions: {
            code: "UNAUTHENTICATED",
            http: { status: 401 },
          },
        });
      }

      return user;
    }
  } catch (err) {
    console.log("Not authorized to access this route", err);
    throw new GraphQLError("User is not authenticated", {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }
};
