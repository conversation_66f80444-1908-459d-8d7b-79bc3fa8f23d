import jwt from "jsonwebtoken";
import { prisma } from "../db";
import { GraphQLError } from "graphql";
import { Request } from "express";

// Role constants following your pattern
export const ROLES = {
  ADMIN: 1,
  USER: 2,
} as const;

export type UserRole = typeof ROLES[keyof typeof ROLES];

// Protect routes - Basic authentication following your existing pattern
export const protect = async (req: Request) => {

  const cookies: any = {};

  if (req.headers.cookie) {
    const cookiesArray = req.headers.cookie.split(";");

    cookiesArray.forEach((cookie) => {
      const [key, value] = cookie.trim().split("=");
      cookies[key] = value;
    });

    req.cookies = cookies;
  }

  let token;
  
  if (req.cookies && req.cookies.jjcrm_token ) {   //taking token from cookies in user
    token = req.cookies.jjcrm_token ;
  } else if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    // Set token from Bearer token in header
    token = req.headers.authorization.split(" ")[1];
    // Set token from cookie
  } else {
    throw new GraphQLError("User is not authenticated to access this route", {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }

  try {
    if (token && process.env.JWT_JJCRM_SECRET) {
      const decoded: any = jwt.verify(token, process.env.JWT_JJCRM_SECRET);

      const user = await prisma.crmuser.findUnique({
        where: {
          id: +decoded.id,
        },
        include: {
          role: true,
        },
      });

      if (!user) {
        throw new GraphQLError("User is not authenticated", {
          extensions: {
            code: "UNAUTHENTICATED",
            http: { status: 401 },
          },
        });
      }

      return user;
    }
  } catch (err) {
    console.log("Not authorized to access this route", err);
    throw new GraphQLError("User is not authenticated", {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }
};

// Role-based authorization middleware following your pattern
export const requireRole = (allowedRoles: UserRole[]) => {
  return async (req: Request) => {
    const user = await protect(req);

    if (!user || !allowedRoles.includes(user.roleId as UserRole)) {
      throw new GraphQLError(`Access denied. Required role: ${allowedRoles.map(role =>
        Object.keys(ROLES).find(key => ROLES[key as keyof typeof ROLES] === role)
      ).join(' or ')}`, {
        extensions: {
          code: "FORBIDDEN",
          http: { status: 403 },
        },
      });
    }

    return user;
  };
};

// Specific role guards following your naming pattern
export const requireAdmin = requireRole([ROLES.ADMIN]);
export const requireUser = requireRole([ROLES.USER]);
export const requireAdminOrUser = requireRole([ROLES.ADMIN, ROLES.USER]);

// Helper functions following your pattern
export const hasRole = (user: any, role: UserRole): boolean => {
  return user.roleId === role;
};

export const isAdmin = (user: any): boolean => {
  return hasRole(user, ROLES.ADMIN);
};

export const isUser = (user: any): boolean => {
  return hasRole(user, ROLES.USER);
};

// Permission checking function for future use
export const hasPermission = async (userId: number, permissionName: string): Promise<boolean> => {
  const userWithPermissions = await prisma.crmuser.findUnique({
    where: { id: userId },
    include: {
      role: {
        include: {
          rolePermissions: {
            include: {
              permission: true
            },
            where: {
              isActive: true
            }
          }
        }
      }
    }
  });

  if (!userWithPermissions) return false;

  return userWithPermissions.role.rolePermissions.some(
    rp => rp.permission.name === permissionName && rp.permission.isActive
  );
};
