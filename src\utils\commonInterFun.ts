// Function to calculate section score
export function calculateSectionScore(section: Record<string, string | number | boolean | null>): number {
  if (!section) {
    return 0;
  }
  const keysToCheck = ['pauseUntil', 'deleteFeedbackId', 'UserRating', 'joinPreferenceId', 'latitude', 'longitude'];
  const filteredSection = Object.keys(section)
    .filter((key) => !keysToCheck.includes(key)) // Exclude keys from keysToCheck
    .reduce((obj: any, key) => {
      obj[key] = section[key];
      return obj;
    }, {});

  const keysWithValue = Object.values(filteredSection).filter(value => value !== null).length;
  const totalKeys = Object.keys(filteredSection).length;

  return (keysWithValue / totalKeys) * 100;

}


export async function findEmptyFields(obj: Record<string, any>): Promise<string[]> {
  const emptyFields: string[] = [];
  const keysToCheck = ['pauseUntil', "deleteFeedbackId", "UserRating", "joinPreferenceId"]

  for (const key in obj) {
    if (!(key in obj)) continue;
    if(keysToCheck.includes(key)) continue;

    const value = obj[key];

    if (value === null || value === undefined || value === '') {
      emptyFields.push(key);
    } else if (typeof value === 'object') {
      emptyFields.push(...await findEmptyFields(value));
    }
  }

  return emptyFields;
}

export async function checkIfVerifiedExists(value: any) {
  if (
    value &&
    value.jobId &&
    Object.keys(value.JobDetail).length &&
    Object.keys(value.candidatePreference).length &&
    Object.keys(value.interviewPreference).length
  ) {
    return true;
  }
  return false;
}

export async function validObject(obj:any) {
  Object.values(obj).every(x => x === null || x === '');
}

