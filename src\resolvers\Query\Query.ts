import { Context } from "../../context";
import { protect, requireAdmin, requireAdminOrUser, isAdmin } from "../../middleware/auth";
import { addJobToQueue } from "../../services/bullmq-queue";
import { UserLoginType } from "../../utils/constants";
import { decode, encode } from "../../utils/crypto";

export const Query = {
  // Get CRM user roles - Admin only following your pattern
  getCRMUserRole: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    // Only admins can view all roles
    const currentUser = await requireAdmin(req);

    const getCRMRole = await prisma.crmuserrole.findMany({
      where: {
        isActive: true,
      },
    });
    return getCRMRole;
  },

  // Get all CRM users - Admin only following your pattern
  getAllCRMUsers: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    // Only admins can view all users
    await requireAdmin(req);

    const users = await prisma.crmuser.findMany({
      include: {
        role: true,
      },
      orderBy: {
        id: 'desc',
      },
    });

    return users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      roleId: user.roleId,
      roleName: user.role.name,
    }));
  },

  //to get particular Recruiter details with verficiation doc details
  getRecruiterVerificationDetail: async (
    parent: unknown,
    args: any,
    { prisma, req }: Context
  ) => {
    try {
      const currentUser = await protect(req);

      if (currentUser) {
        const getRecruiterVerifyDetails = await prisma.recruiter.findUnique({
          where: {
            // mobileNumber: await encode(args.searchId),
            id: +args.recruiterId
          },
          select: {
            id: true,
            name: true,
            mobileNumber: true,
            email: true,
            company: {
              select: {
                name: true,
                id: true,
              },
            },
            Recruiterdocument: {
              select: {
                aadhaarNumber: true,
                panNumber: true,
                gstin: true,
                fssai: true,
                udyogAadhaar: true,
                personalstatusId: true,
                companystatusId: true,
                personalDocGivenId: true,
                companyDocGivenId: true,
                updatedAt: true,
                Personalstatus:{
                  select: {
                    id: true,
                    name: true
                  }
                }
              },
            },
          },
        });

        if (
          getRecruiterVerifyDetails &&
          getRecruiterVerifyDetails.mobileNumber
        ) {
          getRecruiterVerifyDetails.mobileNumber = await decode(
            getRecruiterVerifyDetails.mobileNumber
          );
        }
        if (getRecruiterVerifyDetails && getRecruiterVerifyDetails.email) {
          getRecruiterVerifyDetails.email = await decode(
            getRecruiterVerifyDetails.email
          );
        }

        switch (
          getRecruiterVerifyDetails?.Recruiterdocument?.companyDocGivenId
        ) {
          case 1:
            {
              if (getRecruiterVerifyDetails.Recruiterdocument.gstin) {
                getRecruiterVerifyDetails.Recruiterdocument.gstin =
                  await decode(
                    getRecruiterVerifyDetails.Recruiterdocument.gstin
                  );
              }
            }
            break;

          case 2:
            {
              if (getRecruiterVerifyDetails.Recruiterdocument.fssai) {
                getRecruiterVerifyDetails.Recruiterdocument.fssai =
                  await decode(
                    getRecruiterVerifyDetails.Recruiterdocument.fssai
                  );
              }
            }
            break;

          case 3:
            {
              if (getRecruiterVerifyDetails.Recruiterdocument.udyogAadhaar) {
                getRecruiterVerifyDetails.Recruiterdocument.udyogAadhaar =
                  await decode(
                    getRecruiterVerifyDetails.Recruiterdocument.udyogAadhaar
                  );
              }
            }
            break;
        }

        switch (
          getRecruiterVerifyDetails?.Recruiterdocument?.personalDocGivenId
        ) {
          case 4:
            {
              if (getRecruiterVerifyDetails.Recruiterdocument.panNumber) {
                getRecruiterVerifyDetails.Recruiterdocument.panNumber =
                  await decode(
                    getRecruiterVerifyDetails.Recruiterdocument.panNumber
                  );
              }
            }
            break;

          case 5:
            {
              if (getRecruiterVerifyDetails.Recruiterdocument.aadhaarNumber) {
                getRecruiterVerifyDetails.Recruiterdocument.aadhaarNumber =
                  await decode(
                    getRecruiterVerifyDetails.Recruiterdocument.aadhaarNumber
                  );
              }
            }
            break;
        }

        return getRecruiterVerifyDetails;
      }
    } catch (error) {
      console.log("=====getRecruiterVerifyDetails error==", error);
    }
  },

  recruiterVerificationList: async (
    parent: unknown,
    args: any,
    { prisma, req }: Context
  ) => {
    try {
      const currentUser = await protect(req);

      if (currentUser) {
        const getRecruiterVerifyList = await prisma.recruiter.findMany({
          where: {
            OR: [{ isCompanyVerified: false }, { isPersonalVerified: false }],
          },
          select: {
            id: true,
            name: true,
            mobileNumber: true,
            email: true,
            company: {
              select: {
                name: true,
                id: true,
              },
            },
            Recruiterdocument: {
              select: {
                aadhaarNumber: true,
                panNumber: true,
                gstin: true,
                fssai: true,
                udyogAadhaar: true,
                personalstatusId: true,
                companystatusId: true,
                personalDocGivenId: true,
                companyDocGivenId: true,
                companyRejectReason: true,
                personalRejectReason: true,
                updatedAt: true,
              },
            },
          },
        });

        if (getRecruiterVerifyList && getRecruiterVerifyList.length > 0) {
          for (const recruiter of getRecruiterVerifyList) {
            try {
              if (recruiter && recruiter.mobileNumber) {
                recruiter.mobileNumber = await decode(recruiter.mobileNumber);
              }
            } catch (error) {
              console.log("Failed to decode mobileNumber: recruiter verification list", error);
            }
            try {
              if (recruiter && recruiter.email) {
                recruiter.email = await decode(recruiter.email);
              }
            } catch (error) {
              console.log("Failed to decode mobileNumber: recruiter verification list", error);
            } 
          }
        }

        //     switch (recruiter?.Recruiterdocument?.companyDocGivenId) {
        //       case 1:
        //         {
        //           if (recruiter.Recruiterdocument.gstin) {
        //             recruiter.Recruiterdocument.gstin = await decode(
        //               recruiter.Recruiterdocument.gstin
        //             );
        //           }
        //         }
        //         break;

        //       case 2:
        //         {
        //           if (recruiter.Recruiterdocument.fssai) {
        //             recruiter.Recruiterdocument.fssai = await decode(
        //               recruiter.Recruiterdocument.fssai
        //             );
        //           }
        //         }
        //         break;

        //       case 3:
        //         {
        //           if (recruiter.Recruiterdocument.udyogAadhaar) {
        //             recruiter.Recruiterdocument.udyogAadhaar = await decode(
        //               recruiter.Recruiterdocument.udyogAadhaar
        //             );
        //           }
        //         }
        //         break;
        //     }

        //     switch (recruiter?.Recruiterdocument?.personalDocGivenId) {
        //       case 4:
        //         {
        //           if (recruiter.Recruiterdocument.panNumber) {
        //             recruiter.Recruiterdocument.panNumber = await decode(
        //               recruiter.Recruiterdocument.panNumber
        //             );
        //           }
        //         }
        //         break;

        //       case 5:
        //         {
        //           if (recruiter.Recruiterdocument.aadhaarNumber) {
        //             recruiter.Recruiterdocument.aadhaarNumber = await decode(
        //               recruiter.Recruiterdocument.aadhaarNumber
        //             );
        //           }
        //         }
        //         break;
        //     }

        //     console.log("===getRecruiterVerifyList=", getRecruiterVerifyList);
        //     // return getRecruiterVerifyList;
        //   });
        // }
        return getRecruiterVerifyList;
      }
    } catch (error) {
      console.log("=====getRecruiterVerifyList error==", error);
    }
  },

  getPendingJobs: async (
    parent: unknown,
    args: {filter: any},
    { prisma, req }: Context
  ) => {
    try {
      const currentUser = await protect(req);
      if(currentUser) {
        const skip = (args.filter.pageNumber - 1) * 15;
        const take = args.filter.take ? args.filter.take : 15;
        let where: any = {};

        switch (args.filter.status) {
          case 'completedRequest': //active jobs
            where = {
              jobStatusId: 1
            };
            break;
          case 'pendingRequest': // hold
            where = {
              jobStatusId: 2
            };
            break;
          case 'expiredRequest':
            where = {
              jobStatusId: 5
            };
            break;
          case `allRequest`:
            break;
          default:
            // No additional where condition
            break;
        }
        const getPendingJobs = await prisma.job.findMany({
          where,
          skip,
          take,
          orderBy:{
            JobDetail:{
              updatedAt: 'desc'
            }
          },
          select: {
            id: true,
            companyName: true,
            companyId: true,
            jobStatusId: true,
            recruiter: {
              select: {
                id: true,
                name: true,
                mobileNumber: true,
                email: true
              }
            },
            JobDetail: {
              select: {
                jobTitle: true,
                numberOpenings: true,
                minMonthSalary: true,
                maxMonthSalary: true,
                jobLocation: true,
                buildingName: true,
                updatedAt: true,
                createdAt: true,
                city: true,
                area: true
              }
            },
            candidatePreference: {
              select: {
                minExperience: true,
                maxExperience: true
              }
            }
          }
        });
        const count = await prisma.job.count({
          where
        })
        const responseData = {
          jobs: getPendingJobs,
          count
        }
        return responseData;
      }
    } catch (error) {
      console.log("====error reading pending jobs", error)
    }
  },

  getPendingCompanyList: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {     
        const getCompanyList = await prisma.company.findMany({
          where: {
            isCompanyVerified: false
          },
          select: {
            id: true,
            name: true,
            createdAt: true,
            CompanyType: true,
            isCompanyVerified: true,
            CompanyList: true,
            Recruiter: true,
            location:true
          }
        })
        return getCompanyList;
      } catch (error) {
        console.log("error reading getCompanyList", error);
      }
    }
  },

  syncSolrDB: async (parent: unknown, args: unknown, { prisma }: Context) => {
    try {
      const getJob = await prisma.job.findMany({
        // where: {
        //   jobStatusId: 1
        // },
        include: {
          JobDetail: true,
          candidatePreference: true,
          interviewPreference: true
        }
      });

      console.log("====getJob====", getJob.length);

      if(getJob && getJob.length > 0) {
        for (const job of getJob) {
          const jobdata = {
            "id": job.id,
            "jobId": job.id,
            "companyName": job.companyName,
            "recruiterId": job.recruiterId,
            "companyId": job.companyId,
            "companyTypeId": job.companyTypeId,
            "companyListId": job.companyListId,
            "consultantId": job.consultantId,
            "jobStatusId": job.jobStatusId,
            "JobDetail": {
              "companyName": job.JobDetail?.companyName,
              "jobTitle": job.JobDetail?.jobTitle,
              "jobCategoryId": job.JobDetail?.jobCategoryId,
              "jobLocation": job.JobDetail?.jobLocation,
              "buildingName": job.JobDetail?.buildingName,
              "latitude": job.JobDetail?.latitude,
              "longitude": job.JobDetail?.longitude,
              "location": `${job.JobDetail?.latitude} ${job.JobDetail?.longitude}`,
              "numberOpenings": job.JobDetail?.numberOpenings,
              "workTypeId": job.JobDetail?.workTypeId,
              "jobUrgencyId": job.JobDetail?.jobUrgencyId,
              "jobDescription": job.JobDetail?.jobDescription,
              "minMonthSalary": job.JobDetail?.minMonthSalary,
              "maxMonthSalary": job.JobDetail?.maxMonthSalary,
              "isIncentives": job.JobDetail?.isIncentives,
              "avgMonthlyIncentive": job.JobDetail?.avgMonthlyIncentive,
              "workingDaysId": job.JobDetail?.workingDaysId,
              "feesDeposit": job.JobDetail?.feesDeposit,
              "depositAmount": job.JobDetail?.depositAmount,
              "depositReasonId": job.JobDetail?.depositReasonId,
              "additionalInformation": job.JobDetail?.additionalInformation,
              "createdAt": job.JobDetail?.createdAt,
              "companyId": job.JobDetail?.companyId,
              "shiftTypeId": job.JobDetail?.shiftTypeId,
              "employmentTypeId": job.JobDetail?.employmentTypeId,
            },
            "candidatePreference": {
              "genderId": job.candidatePreference?.genderId,
              "educationId": job.candidatePreference?.educationId,
              "workExperienceId": job.candidatePreference?.workExperienceId,
              "minExperience": job.candidatePreference?.minExperience,
              "maxExperience": job.candidatePreference?.maxExperience,
              "applicationRadiusId": job.candidatePreference?.applicationRadiusId,
              "englishKnowledgeId": job.candidatePreference?.englishKnowledgeId,
              "isAssetsRequired": job.candidatePreference?.isAssetsRequired,
              "createdAt": job.candidatePreference?.createdAt,
              "updatedAt": job.candidatePreference?.updatedAt,
            },
            "interviewPreference": {
              "hrName": job.interviewPreference?.hrName,
              "hrContactNumber": job.interviewPreference?.hrContactNumber,
              "interviewTypeId": job.interviewPreference?.interviewTypeId,
              "createdAt": job.interviewPreference?.createdAt,
              "updatedAt": job.interviewPreference?.updatedAt,
            }
          }
  
          // await addJobToQueue({
          //   jobName: 'solrJob',
          //   value: jobdata
          // }); 
        }
      } else {
        console.log("=====no active jobs in the db")
      }
    } catch (error) {
      console.log("====solr error==", error)
    }
  },

  getRecruiterRequestCallback: async (
    parent: unknown,
    args: { filter: any },
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = args.filter.take ? args.filter.take : 15;
      const whereCondition: any = {
        AND: [
          {
            requestCallBackStatusId: +args.filter.requestStatusId,
          },
        ],
      };
      
      if (args.filter.searchText) {
        whereCondition.AND.push({
          recruiter: {
            mobileNumber: await encode(args.filter.searchText),
          },
        });
      }
      
      const getCallbackRequest = await prisma.recruiterrequest.findMany({
        where: whereCondition,
        skip,
        take,
        orderBy: {
          updatedAt: 'desc'
        },
        include: {
          requestCallBackStatus: {
            select: {
              name: true
            }
          },
          recruiter: {
            select: {
              id: true,
              name: true,
              email: true,
              mobileNumber: true,
              company: true
            }
          },
          crmuser: {
            select: {
              id: true,
              email: true,
              mobileNumber: true,
            }
          }
        }
      });
      console.log(getCallbackRequest);
      if(getCallbackRequest.length >= 1) {
        for(const item of getCallbackRequest) {
          if(item && item.recruiter) {
            if(item.recruiter.mobileNumber) {
              item.recruiter.mobileNumber = await decode(item.recruiter.mobileNumber)
            }
            if (item.recruiter.email) {
              item.recruiter.email= await decode(item.recruiter.email)
            }
          }
        }
      }
      return getCallbackRequest;
    }
  },

  // to list all request status in seeded in database 
  getRequestCallbackStatus: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getStatus = await prisma.requestcallbackstatus.findMany({
          where: {
            isActive: true,
          }
        });
        return getStatus;
      } catch (error) {
        console.log("error getting request callback status", error);
      }
    }
  },

  //to list all companies (all, approved, pending, rejected)
  getCompanyApprovalDetails: async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = args.filter.take ? args.filter.take : 15;
      let where: any = {}; // Default to empty where condition

      // Apply where condition based on the option
      switch (args.filter.type) {
        case 'completedRequest':
          where = {
            companystatusId: 3
          };
          break;
        case 'pendingRequest':
          where = {
            companystatusId: 2
          };
          break;
        case 'rejectedRequest':
          where = {
            companystatusId: 4
          };
          break;
        case `allRequest`:
          where = {
            NOT: {
              companystatusId: 1
            }
          }
          break;
        default:
          // No additional where condition
          break;
      }

      if(args.filter.searchText) {
        where = {
          AND: [
            where, // Retain the existing conditions
            {
              recruiter: {
                mobileNumber: await encode(args.filter.searchText)
              }
            }
          ]
        }
      }

      const [companyDetails, totalCount] = await prisma.$transaction([
        prisma.recruiterdocument.findMany({
          where: where,
          skip: skip,
          take: take,
          orderBy:{
            recruiter:{
              company:{
                updatedAt: 'desc'
              }
            }
          },
          select: {
            id: true,
            createdAt: true,
            companyDocGivenId: true,
            companyDocGiven: {
              select: {
                id: true,
                name: true
              }
            },
            companystatusId: true,
            Companystatus: {
              select: {
                id: true,
                name: true,
              }
            },
            recruiter: {
              include: {
                RecruiterRole: {
                  select: {
                    id: true,
                    name: true,
  
                  }
                },
                RecruiterStatus: {
                  select: {
                    id: true,
                    name: true
                  }
                },
                HiringFor: {
                  select: {
                    id: true,
                    name: true,
                  }
                },
                Designation: {
                  select: {
                    id: true,
                    name: true,
                  }
                },
                company: {
                  include: {
                    industry: {
                      select: {
                        id: true,
                        name: true
                      }
                    },
                    NumberOfEmployees: {
                      select: {
                        id: true,
                        name: true
                      }
                    }
                  }
                }
              }
            }
          }
        }),
        prisma.recruiterdocument.count({
          where: where,
        })
      ])

      console.log(companyDetails, totalCount)
      
      if (companyDetails.length > 0) {
        for (const i of companyDetails) {
          try {
            if (i.recruiter && i.recruiter.mobileNumber) {
              i.recruiter.mobileNumber = await decode(i.recruiter.mobileNumber)
            }
          } catch (error) {
            console.log("Failed to decode mobileNumber:", error);
          }
          try {
            if (i.recruiter && i.recruiter.email) {
              i.recruiter.email = await decode(i.recruiter.email)
            }
          } catch (error) {
            console.log("Failed to decode email:", error);
          }
        }
      }
      return{ companyDetails: companyDetails, totalCount: totalCount }
    }
  },

  // to get list of recruiters(all, approved, pending, rejected)
  getIdentityApprovalDetails: async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = args.filter.take ? args.filter.take : 15;
      let where: any = {}; // Default to empty where condition

      // Apply where condition based on the option
      switch (args.filter.type) {
        case 'completedRequest':
          where = {
            personalstatusId: 3
          };
          break;
        case 'pendingRequest':
          where = {
            personalstatusId: 2
          };
          break;
        case 'rejectedRequest':
          where = {
            personalstatusId: 4
          };
          break;
        case `allRequest`:
          where = {
            NOT: {
              personalstatusId: 1
            }
          }
          break;
        default:
          // No additional where condition
          break;
      }

      if(args.filter.searchText) {
        where = {
          AND: [
            where, // Retain the existing conditions
            {
              OR: [
                {
                  recruiter: {
                    mobileNumber: await encode(args.filter.searchText)
                  }
                },
                {
                  recruiter: {
                    id: +args.filter.searchText // Assuming searchText could also be the recruiterId
                  }
                }
              ]
            }
          ]
        }
      }

      const [identityDetails, totalCount] = await prisma.$transaction([
        prisma.recruiterdocument.findMany({
          where: where,
          skip: skip,
          take: take,
          orderBy: {
            recruiter: {
              updatedAt: 'desc',
            },
          },
          select: {
            id: true,
            createdAt: true,
            personalDocGivenId: true,
            personalDocGiven: {
              select: {
                id: true,
                name: true
              }
            },
            companystatusId: true,
            Companystatus: {
              select: {
                id: true,
                name: true,
              }
            },
            recruiter: {
              include: {
                RecruiterRole: {
                  select: {
                    id: true,
                    name: true,
  
                  }
                },
                RecruiterStatus: {
                  select: {
                    id: true,
                    name: true
                  }
                },
                HiringFor: {
                  select: {
                    id: true,
                    name: true,
                  }
                },
                Designation: {
                  select: {
                    id: true,
                    name: true,
                  }
                },
                company: {
                  include: {
                    industry: {
                      select: {
                        id: true,
                        name: true
                      }
                    },
                    NumberOfEmployees: {
                      select: {
                        id: true,
                        name: true
                      }
                    }
                  }
                }
              }
            }
          }
        }),

        prisma.recruiterdocument.count({
          where:where
        })
      ])
      
      
      console.log("====identityDetails====", identityDetails, totalCount);

      if (identityDetails.length > 0) {
        for (const i of identityDetails) {
          try {
            if (i.recruiter && i.recruiter.mobileNumber) {
              i.recruiter.mobileNumber = await decode(i.recruiter.mobileNumber);
            }
          } catch (error) {
            console.log("Failed to decode mobileNumber:", error);
          }
      
          try {
            if (i.recruiter && i.recruiter.email) {
              i.recruiter.email = await decode(i.recruiter.email);
            }
          } catch (error) {
            console.log("Failed to decode email:", error);
          }
      }
    }
      
      return {identityDetails: identityDetails, totalCount: totalCount}
    }
  },

  //for CRM dashboard with all detailed counts
  getCRMDetails: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);

    if(currentUser) {
      try {
        const recruiterCount = await prisma.recruiter.count();
        const [CompanyCount, verifiedCompCount, PendingCompCount, rejectedCompCount] = await prisma.$transaction([
          prisma.recruiterdocument.count({
            where:{
              NOT: {
                companystatusId: 1
              }
            }
          }), //total count
  
          //Company status doc approved
          prisma.recruiterdocument.count({
            where:{
              companystatusId: 3
            }
          }),
  
          //Company status doc pending
          prisma.recruiterdocument.count({
            where:{
              companystatusId: 2
            }
          }),
  
          //Company status doc rejected
          prisma.recruiterdocument.count({
            where:{
              companystatusId: 4
            }
          })
  
  
        ]);
        const [identityCount, completedIdenCount, PendingIdenCount, rejectedIdenCount] = await prisma.$transaction([
          prisma.recruiterdocument.count({
            where:{
              NOT:{
                personalstatusId: 1
              }
            }
          }), //total count
  
          //personal status doc approved
          prisma.recruiterdocument.count({
            where:{
              personalstatusId: 3
            }
          }),
  
          //personal status doc pending
          prisma.recruiterdocument.count({
            where:{
              personalstatusId: 2
            }
          }),
  
          //personal status doc rejected
          prisma.recruiterdocument.count({
            where:{
              personalstatusId: 4
            }
          })
  
        ]);
        const [jobsCount, activeJobsCount, expiredJobsCount, pendingJobsCount ] = await prisma.$transaction([
          prisma.job.count(),
          prisma.job.count({
            where:{
              jobStatusId: 1
            }
          }),
          prisma.job.count({
            where:{
              jobStatusId: 5
            }
          }),
          prisma.job.count({
            where:{
              jobStatusId: 2
            }
          }),
        ])
        const [totalCallback, openRequest, closedRequest ] = await prisma.$transaction([
          prisma.recruiterrequest.count(),
          prisma.recruiterrequest.count({
            where:{
              requestCallBackStatusId: 1
            }
          }),
          prisma.recruiterrequest.count({
            where:{
              requestCallBackStatusId: 2
            }
          })
        ])
        
        const now = new Date();
        // Start of yesterday at 12:00 AM
        const startOfYesterday = new Date(now);
        startOfYesterday.setDate(now.getDate() - 1); // Go to yesterday
        startOfYesterday.setHours(0, 0, 0, 0); // Set to 12:00 AM

        // End of yesterday at 11:59 PM
        const endOfYesterday = new Date(startOfYesterday);
        endOfYesterday.setHours(23, 59, 59, 999); // Set to 11:59 PM
        const [recruiterLoginCount, CandidateCount] = await prisma.$transaction([
          prisma.recruiterlogininfo.count({
            where: {
              loginDate:{
                gte: startOfYesterday,
                lte: endOfYesterday,
              }
            }
          }),
          prisma.userlogininfo.count({
            where: {
              loginDate:{
                gte: startOfYesterday,
                lte: endOfYesterday,
              }
            }
          })
        ]
        )
        const averageRating = await prisma.recruiter.aggregate({
          _avg: {
            appRating: true,
          },
        })
        const [totalCount, deletedCount ] = await prisma.$transaction([
          prisma.user.count(),
          prisma.user.count({
            where: {
              userStatusId: 6
            }
          })
  
        ])
        const [allRequest, recruiterCreated, recruiterDetailsUpdated, companyCreated] = await prisma.$transaction([
          prisma.recruiter.count({
            where:{
              OR: [
                { recruiterStatusId: 1 }, 
                { recruiterStatusId: 2 },
                { recruiterStatusId: 3 }
              ]
            }
          }),
          prisma.recruiter.count({
            where:{
              recruiterStatusId: 1 
            }
          }),
          prisma.recruiter.count({
            where:{
              recruiterStatusId: 2 
            }
          }),
          prisma.recruiter.count({
            where:{
              recruiterStatusId: 3
            }
          })
        ])
        
        // const [
        //   recruiterCount,
        //   companyStatusCounts,
        //   personalStatusCounts,
        //   jobCounts,
        //   averageRating,
        // ] = await Promise.all([
        //   prisma.recruiter.count(),
        //   prisma.recruiterdocument.groupBy({
        //     by: ['companystatusId'],
        //     _count: true,
        //   }),
        //   prisma.recruiterdocument.groupBy({
        //     by: ['personalstatusId'],
        //     _count: true,
        //   }),
        //   prisma.job.groupBy({
        //     by: ['jobStatusId'],
        //     _count: true,
        //   }),
        //   prisma.recruiter.aggregate({
        //     _avg: {
        //       appRating: true,
        //     },
        //   }),
        // ]);
  
        // console.log("testttt",  recruiterCount,
        //   companyStatusCounts,
        //   personalStatusCounts,
        //   jobCounts,
        //   averageRating,)
  
        return {
          recruiterDetails:{totalCount: recruiterCount},
          companyManagement: {totalCount: CompanyCount,verified: verifiedCompCount,pending: PendingCompCount , rejected: rejectedCompCount},
          recruiterManagement: {totalCount: identityCount, verified: completedIdenCount, pending: PendingIdenCount, rejected: rejectedIdenCount},
          jobDetails: {totalCount: jobsCount,active: activeJobsCount, expired: expiredJobsCount, pending: pendingJobsCount},
          callBack: {totalCount: totalCallback, open: openRequest,closed: closedRequest},
          feedback: (averageRating._avg.appRating)?.toFixed(0),
          jobSeeker: {totalCount: totalCount,deleted: deletedCount},
          loginDetails: {recruiter: recruiterLoginCount, candidate: CandidateCount},
          unverifiedDetails: {totalCount: allRequest, createdCount: recruiterCreated, updatedDetailsCount: recruiterDetailsUpdated,
            updatedCompanyCount: companyCreated
          }
        }
      } catch (error) {
        console.log("error in getting recruiter status", error)
      }
    }
  },

  // to list all call back details( openRequest, closedRequest)
  getCallBackDetails: async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = 15;
      let where: any = {}; // Default to empty where condition

      // Apply where condition based on the option
      switch (args.filter.type) {
        case 'openRequest':
          where = {
            requestCallBackStatusId: 1
          };
          break;
        case 'closedRequest':
          where = {
            requestCallBackStatusId: 2
          };
          break;
        // case 'unansweredRequest':
        //   where = {
        //     requestCallBackStatusId: 3
        //   };
        //   break;
        case `allRequest`:
          break;
        default:
          // No additional where condition
          break;
      }

      const callBackDetails = await prisma.recruiterrequest.findMany({
        where: where,
        skip: skip,
        take: take,
        select: {
          id: true,
          crmUserId: true,
          createdAt: true,
          updatedAt: true,
          crmuser: {
            select: {
              id: true,
              email: true,
              mobileNumber: true
            }
          },
          requestCallBackStatusId: true,
          requestCallBackStatus: {
            select: {
              id: true,
              name: true
            }
          },
          recruiterId: true,
          recruiter: {
            include: {
              RecruiterRole: {
                select: {
                  id: true,
                  name: true,

                }
              },
              RecruiterStatus: {
                select: {
                  id: true,
                  name: true
                }
              },
              HiringFor: {
                select: {
                  id: true,
                  name: true,
                }
              },
              Designation: {
                select: {
                  id: true,
                  name: true,
                }
              },
              company: {
                include: {
                  industry: {
                    select: {
                      id: true,
                      name: true
                    }
                  },
                  NumberOfEmployees: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            }
          }

        }
      })

      if (callBackDetails.length > 0) {
        callBackDetails.forEach(async (i: any) => {
          if (i.recruiter) {
            if (i.recruiter.mobileNumber) {
              i.recruiter.mobileNumber = await decode(i.recruiter.mobileNumber)
            }
            if (i.recruiter.email) {
              i.recruiter.email = await decode(i.recruiter.email)
            }
          }
        })
      }
      return callBackDetails
    }
  },

  //to get particular Recruiter details with verficiation doc details
  getCompanyVerificationDetail: async (
    parent: unknown,
    args: any,
    { prisma, req }: Context
  ) => {
    try {
      const currentUser = await protect(req);

      if (currentUser) {
        const getRecruiterVerifyDetails = await prisma.company.findUnique({
          where: {
            id: +args.companyId
          },
          select: {
            id: true,
            name: true,
            NumberOfEmployees: {
              select:{
                id: true,
                name: true
              }
            },
            companyListId: true,
            industry:{
              select:{
                id: true,
                name: true
              }
            },
            CompanyList:{
              select: {
                id: true,
                name: true,
                isActive: true
              }
            },
            isCompanyVerified: true,
            Recruiter:{
              select: {
                id: true, 
                name: true,
                mobileNumber: true,
                email: true,
                isCompanyVerified: true,
                isPersonalVerified: true,
                Recruiterdocument: {
                  select: {
                    Companystatus:{
                      select:{
                        name: true,
                        id: true
                      }
                    },
                    aadhaarNumber: true,
                    panNumber: true,
                    gstin: true,
                    fssai: true,
                    udyogAadhaar: true,
                    personalstatusId: true,
                    companystatusId: true,
                    personalDocGivenId: true,
                    companyDocGivenId: true,
                    updatedAt: true,
                  },
                },
              }
            }
            
          },
        });

        if ( getRecruiterVerifyDetails?.Recruiter?.mobileNumber ) {
          getRecruiterVerifyDetails.Recruiter.mobileNumber = await decode(
            getRecruiterVerifyDetails?.Recruiter?.mobileNumber
          );
        }
        if ( getRecruiterVerifyDetails?.Recruiter?.email) {
          getRecruiterVerifyDetails.Recruiter.email = await decode(
            getRecruiterVerifyDetails.Recruiter.email
          );
        }
        
        
        switch (
          getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.companyDocGivenId
        ) {
          case 1:
            {
              if (getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.gstin) {
                getRecruiterVerifyDetails.Recruiter.Recruiterdocument.gstin =
                  await decode(
                    getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument.gstin
                  );
              }
            }
            break;

          case 2:
            {
              if (getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.fssai) {
                getRecruiterVerifyDetails.Recruiter.Recruiterdocument.fssai =
                  await decode(
                    getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument.fssai
                  );
              }
            }
            break;

          case 3:
            {
              if (getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.udyogAadhaar) {
                getRecruiterVerifyDetails.Recruiter.Recruiterdocument.udyogAadhaar =
                  await decode(
                    getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument.udyogAadhaar
                  );
              }
            }
            break;
        }

        switch (
          getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.personalDocGivenId
        ) {
          case 4:
            {
              if (getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.panNumber) {
                getRecruiterVerifyDetails.Recruiter.Recruiterdocument.panNumber =
                  await decode(
                    getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument.panNumber
                  );
              }
            }
            break;

          case 5:
            {
              if (getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument?.aadhaarNumber) {
                getRecruiterVerifyDetails.Recruiter.Recruiterdocument.aadhaarNumber =
                  await decode(
                    getRecruiterVerifyDetails?.Recruiter?.Recruiterdocument.aadhaarNumber
                  );
              }
            }
            break;
        }

        return getRecruiterVerifyDetails;
      }
    } catch (error) {
      console.log("=====getRecruiterVerifyDetails error==", error);
    }
  },

  // to get particular Job
  GetSelectedJob: async (parent: any, {job_id}: any, { prisma, req }: Context) => {
    const currentUser = await protect(req);

    if(currentUser) {
      try {
        const getSelectedJob: any = await prisma.job.findUnique({
          where: {
            id: +job_id,
          },
          include: {
            jobStatus:{
              select:{
                id: true,
                name: true
              }
            },
            JobDetail:{
              include:{
                jobCategory: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                workType: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                jobBenefits: {
                  select: {
                    JobBenefits: {
                      select: {
                        id: true,
                        name: true,
                        name_en: true,
                        name_ta: true
                      }
                    }
                  }
                },
                employmentType: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  }
                },
                jobUrgency: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                shiftType: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  }
                },
                WorkingDays: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  }
                },
                depositReason: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  }
                },
                JobTitle:{
                  select: {
                    id: true,
                    name: true,
                    isActive: true
                  }
                }
              }
            },
            candidatePreference: {
              select: {
                id: true,
                isAssetsRequired: true,
                Gender: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                Education: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                WorkExperience: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                minExperience: true,
                maxExperience: true,
                applicationRadius: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                EnglishKnowledge: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                RecruiterSkillsMapping: {
                  select: {
                    skill: {
                      select: {
                        id: true,
                        name: true,
                        name_en: true,
                        name_ta: true
                      },
                    },
                  },
                },
                RequiredAssetsMapping: {
                  select: {
                    RequiredAssets: {
                      select: {
                        id: true,
                        name: true,
                        name_en: true,
                        name_ta: true
                      },
                    },
                  },
                },
              },
            },
            interviewPreference: {
              select: {
                id: true,
                hrName: true,
                hrContactNumber: true,
                InterviewType: {
                  select: {
                    id: true,
                    name: true,
                    name_en: true,
                    name_ta: true
                  },
                },
                faceInterview: {
                  select:{
                    buildingName: true,
                    fullAddress: true,
                    latitude: true,
                    longitude: true
                  }
                },
                WalkinInterview: {
                  select: {
                    walkinStartDate: true,
                    startTime: true,
                    duration: true,
                    endTime: true,
                    location:true,
                    buildingName: true,
                    latitude: true,
                    longitude: true
                  }
                }
              },
            }
          },
        });
        if(getSelectedJob && getSelectedJob.interviewPreference && getSelectedJob.interviewPreference.hrContactNumber) {
          getSelectedJob.interviewPreference.hrContactNumber= await decode(getSelectedJob.interviewPreference.hrContactNumber);
        }
        
        if (getSelectedJob && getSelectedJob.JobDetail && getSelectedJob.candidatePreference) {
          const candiateSkills: any = [];
          const assetRequired: any = [];
          const jobBenefits: any = [];
          // const shiftType: any = [];

          getSelectedJob.JobDetail.jobBenefits.forEach((benefit: any) => jobBenefits.push(Object.values(benefit)[0]))
          // getSelectedJob.shiftType.forEach((shift: any) => shiftType.push(Object.values(shift)[0]))

          getSelectedJob.candidatePreference.RecruiterSkillsMapping.forEach(
            (skill: any) => {
              candiateSkills.push(Object.values(skill)[0]);
            }
          );
          getSelectedJob.candidatePreference.RequiredAssetsMapping.forEach(
            (asset: any) => {
              assetRequired.push(Object.values(asset)[0]);
            }
          );

          getSelectedJob.candidatePreference.RecruiterSkillsMapping = [];
          getSelectedJob.candidatePreference.RequiredAssetsMapping = [];
          getSelectedJob.JobDetail.jobBenefits = [];
          // getSelectedJob.shiftType = [];

          getSelectedJob.candidatePreference.RecruiterSkillsMapping.push(
            ...candiateSkills
          );
          getSelectedJob.candidatePreference.RequiredAssetsMapping.push(
            ...assetRequired
          );
          getSelectedJob.JobDetail.jobBenefits.push(...jobBenefits);
          // getSelectedJob.shiftType.push(...shiftType);
        }

        return getSelectedJob;
      } catch (error) {
        console.log("=====get seleted job error", error);
      }
    }
  },

  workType: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getWorkType = await prisma.workType.findMany({
          where: {
            isActive: true,
          },
        });
        return getWorkType;
      } catch (error) {
        console.log("error reading getWorkType", error);
      }
    }
  },

  employmentType: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getEmploymentType = await prisma.employmentType.findMany({
          where: {
            isActive: true,
          },
        });
        return getEmploymentType;
      } catch (error) {
        console.log("error reading getEmploymentType", error);
      }
    }
  },

  
  shiftType: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getShiftType = await prisma.shiftType.findMany({
          where: {
            isActive: true,
          },
        });
        return getShiftType;
      } catch (error) {
        console.log("error reading getShiftType", error);
      }
    }
  },

  workingDays: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
      if(currentUser) {
      try {
        const getWorkingDays = await prisma.workingDays.findMany({
          where: {
            isActive: true,
          },
        });
        return getWorkingDays;
      } catch (error) {
        console.log("error reading getWorkingDays", error);
      }
    }
  },

  joinPreference: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getJoinPreference = await prisma.joinPreference.findMany({
          where: {
            isActive: true,
          },
        });
        return getJoinPreference;
      } catch (error) {
        console.log("error getting Join Preference", error);
      }
    }
  },

  jobBenefits: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getJobBenefits = await prisma.jobBenefits.findMany({
          where: {
            isActive: true,
          },
        });
        return getJobBenefits;
      } catch (error) {
        console.log("error reading getJobBenefits", error);
      }
    }
  },

  depositReason: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getDepositReason = await prisma.depositReason.findMany({
          where: {
            isActive: true,
          },
        });
        return getDepositReason;
      } catch (error) {
        console.log("error reading getDepositReason", error);
      }
    }
  },

  interviewType: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getInterviewType = await prisma.interviewtype.findMany({
          where: {
            isActive: true,
          },
        });
        return getInterviewType;
      } catch (error) {
        console.log("error in getting interviewType", error);
      }
    }
  },

  requiredAssets: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getRequiredAssets = await prisma.requiredAssets.findMany({
          where: {
            isActive: true,
          },
        });
        return getRequiredAssets;
      } catch (error) {
        console.log("error reading getRequiredAssets", error);
      }
    }
  },

  englishKnowledge: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getEnglishKnowledge = await prisma.englishKnowledgeLevel.findMany({
          where: {
            isActive: true,
          },
        });
        return getEnglishKnowledge;
      } catch (error) {
        console.log("error reading getEnglishKnowledge", error);
      }
    }
  },

  distance: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getDistance = await prisma.distance.findMany({
          where: {
            isActive: true,
          },
        });
        return getDistance;
      } catch (error) {
        console.log("error reading getDistance", error);
      }
    }
  },

  workExperience: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getWorkExperience = await prisma.workExperience.findMany({
          where: {
            isActive: true,
          },
        });
        return getWorkExperience;
      } catch (error) {
        console.log("error reading getWorkExperience", error);
      }
    }
  },

  educationQualification: async (
    parent: unknown,
    args: unknown,
    { prisma, req }: Context
  ) => {
    const currentUser = await protect(req);
    if(currentUser) {
      const getEducationQualification = await prisma.qualificationType.findMany({
        where: {
          isActive: true,
        },
      });
      return getEducationQualification;
    }
  },

  gender: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getGenders = await prisma.$queryRaw`select * from Gender where(isActive = true) order by field(id, 3,1,2);`
        console.log("====getGenders====", getGenders);
        // const getGenders = await prisma.gender.findMany({
        //   where: {
        //     isActive: true,
        //   },
        // });
        return getGenders;
      } catch (error) {
        console.log("error reading getGenders", error);
      }
    }
  },

  companyList: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getCompanyList = await prisma.companyList.findMany({
          where: {
            isActive: true,
          },
        });
        return getCompanyList;
      } catch (error) {
        console.log("error reading getCompanyList", error);
      }
    }
  },

  getJobTitle: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getJobTitle = await prisma.jobtitle.findMany({
          where: {
            isActive: true,
          },
          include:{
            jobRole: true
          }
        });
        return getJobTitle;
      } catch (error) {
        console.log("error getting getAppLanguages", error);
      }
    }
  },

  jobRole: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getjobRole = await prisma.jobRole.findMany({
          where: {
            isActive: true,
          },
        });
        return getjobRole;
      } catch (error) {
        console.log("error reading jobRole", error);
      }
    }
  },

  skills: async (parent: unknown, args: unknown, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      try {
        const getSkills = await prisma.skillList.findMany({
          where: {
            isActive: true,
          },
        });
        return getSkills;
      } catch (error) {
        console.log("error reading getSkills", error);
      }
    }
  },

  getSeekerDetail: async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);

    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = args.filter.take ? args.filter.take : 15;
      let where: any = {};

      switch (args.filter.type) {
        case 'allUsers':
          where = {
            userStatusId: {
              in: [4, 5, 6]
            }
          };
          break;
        case 'deletedUsers':
          where = {
            userStatusId: 6
          };
          break;
        case 'activeUsers':
          where = {
            userStatusId: 4
          };
          break;
        default:
        break;
      }

      if(args.filter.searchText) {
        where = {
          AND: [
            where,
            {
              OR: [
                {
                  mobileNumber: await encode(args.filter.searchText)
                },
                {
                  id: +args.filter.searchText
                }
              ]
            }
          ]
        }
      }
      const [identityDetails, totalCount]=  await prisma.$transaction([
        prisma.user.findMany({
          where: where,
          skip: skip,
          take: take,
          orderBy:{
            updatedAt: 'desc'
          },
          select: {
            id: true,
            mobileNumber: true,
            city:{
              select:{
                id:true,
                name:true
              }
            },
            state:{
              select:{
                id:true,
                name:true
              }
            },
            name: true,
            email: true,
            createdAt: true,
            updatedAt: true,
            userEducation:{
              select:{
                collegeName: true,
                collegesId: true,
                colleges:{
                  select: {
                    name: true,
                    id: true
                  }
                }
              }
            },
            userProfessional:{
              select: {
                resume: true
              }
            }
          }
        }),
        prisma.user.count({
          where: where
        })
      ])
      console.log("====identityDetails====", identityDetails, totalCount)
      if(identityDetails.length > 0) {
        for (const i of identityDetails) {
            if(i.mobileNumber) {
              i.mobileNumber = await decode(i.mobileNumber)
            }
            if(i.email) {
              i.email = await decode(i.email)
            }
          } 
      }
      return {seekerDetails: identityDetails, totalCount: totalCount }
    }
  },

  getSelectedUser: async (parent: unknown, {userId}: any, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if(currentUser) {
      const userDetails = await prisma.user.findUnique ({
        where: {
          id: +userId
        },
        select: {
          mobileNumber: true,
          id: true,
          gender: true,
          name: true,
          email: true,
          dateOfBirth: true,
          twoWheelerLicense: true,
          profilePicture: true,
          userStatusId: true,
          latitude: true,
          longitude: true,
          language: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          userEducation: {
            include: {
              qualification: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                  name_ta: true
                },
              },
              course: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                  name_ta: true,
                },
              },
              specialization: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                  name_ta: true
                },
              },
            },
          },
          userJobPreferences: {
            include: {
              prefRoleCategory: {
                select: {
                  id: true,
                  name_en: true,
                  name_ta: true
                },
              },
              distance: {
                select: {
                  id: true,
                  name_en: true,
                  name_ta: true
                },
              },
              userCommuteMode: {
                select: {
                  commuteMode: {
                    select: {
                      id: true,
                      name: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
              userEmploymentType: {
                select: {
                  employmentType: {
                    select: {
                      id: true,
                      name: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
              userWorkType: {
                select: {
                  workType: {
                    select: {
                      id: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
              userShiftType: {
                select: {
                  shiftType: {
                    select: {
                      id: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
              userWorkingDays: {
                select: {
                  workingDays: {
                    select: {
                      id: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
              prefJobRole: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                  name_ta: true
                }
              }
            },
          },
          userProfessional: {
            include: {
              currentJobRole: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                  name_ta: true,
                  jobRoleCategoryId: true
                }
              },
              currentRoleCategory: {
                select: {
                  id: true,
                  name: true,
                  name_en: true,
                  name_ta: true
                }
              },
              englishKnowledge: {
                select: {
                  id: true,
                  name_en: true,
                  name_ta: true
                },
              },
              languagesKnown: {
                select: {
                  profileLanguage: {
                    select: {
                      id: true,
                      name: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
              skills: {
                // include: {
                //   skill: true
                // }
                select: {
                  skill: {
                    select: {
                      id: true,
                      name: true,
                      name_en: true,
                      name_ta: true
                    },
                  },
                },
              },
            },
          },
        },
      });
      if(userDetails) {
        if(userDetails.mobileNumber) {
          userDetails.mobileNumber = await decode(userDetails.mobileNumber)
        }
        if(userDetails.email) {
          userDetails.email = await decode(userDetails.email)
        }
      }
      return userDetails;
    }
  },

  getUnverifiedRecruiter: async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = args.filter.take ? args.filter.take : 15;
      let where: any = {}; // Default to empty where condition

      // Apply where condition based on the option
      switch (args.filter.type) {
        case 'recruiterCreated':
          where = {
            recruiterStatusId: 1
          };
          break;
        case 'recruiterUpdated':
          where = {
            recruiterStatusId: 2
          };
          break;
        case 'companyCreated':
          where = {
            recruiterStatusId: 3
          };
          break;
        case `allRequest`:
          where= {
            OR: [
              { recruiterStatusId: 1 }, 
              { recruiterStatusId: 2 },
              { recruiterStatusId: 3 },
              { recruiterStatusId: 11 }
            ]
          }
          break;
        default:
          break;
      }

      if (args.filter?.recruiterWhatsAppUpdate) {
        where.whatsAppUpdate= true ;
      }

      if(args.filter.recruiterEmailUpdate) {
        where.email = {not: null}
      }

      if(args.filter.searchText) {
        where = {
          AND: [
            where, // Retain the existing conditions
            {
              mobileNumber: await encode(args.filter.searchText)
            }
          ]
        }
      }

      const fetchAll = args.filter?.isFetchAllData || false;

      let pagination: { skip?: number, take?: number } = {};
      if (!fetchAll) {
        pagination = {
          skip: skip,
          take: take,
        };
      }

      const [unverifiedRecruiters, totalcount] = await prisma.$transaction([
        prisma.recruiter.findMany({
          where: where,
          ...pagination,
          orderBy: {
            updatedAt: 'desc',
          },
          include: {
            RecruiterRole: {
              select: {
                id: true,
                name: true,
  
              }
            },
            RecruiterStatus: {
              select: {
                id: true,
                name: true
              }
            },
            HiringFor: {
              select: {
                id: true,
                name: true,
              }
            },
            Designation: {
              select: {
                id: true,
                name: true,
              }
            },
            company: {
              include: {
                industry: {
                  select: {
                    id: true,
                    name: true
                  }
                },
                NumberOfEmployees: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        }),
        prisma.recruiter.count({
          where: where
        })
      ]) ;

      if (unverifiedRecruiters.length > 0) {
        for (const i of unverifiedRecruiters) {
          try {
            if (i.mobileNumber) {
              i.mobileNumber = await decode(i.mobileNumber);
            }
          } catch (error) {
            console.log("Failed to decode mobileNumber:", error);
          }
      
          try {
            if (i.email) {
              i.email = await decode(i.email);
            }
          } catch (error) {
            console.log("Failed to decode email:", error);
          }
      }
    }
      return { recruiterDetails: unverifiedRecruiters, totalCount: totalcount };
    }
  } ,

  recruiterWhatsappNotification:async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const {recruiterDetails, templateType, modeOfCommunication} = args.filter
      if (recruiterDetails.length > 0 && modeOfCommunication === "whatsapp") {
        for (const recruiter of recruiterDetails) {
          try {
            await addJobToQueue({
              jobName: 'sendWhatsappNotification',
              value: {recDetails: recruiter, templateName: templateType }, // Pass the recruiter object to the job data
            });
          } catch (error) {
            console.log("Failed to decode email:", error);
          }
        }
        return {message: "whatsapp notification successfully sent"}
      } else if (recruiterDetails.length > 0 && modeOfCommunication === "email") {
        for(const recruiter of recruiterDetails) {

          await addJobToQueue({
            jobName: 'sendEmailNotification',
            value: {recDetails: recruiter, templateName: templateType }, // Pass the recruiter object to the job data
          });
          
        }
        return {message: "Email notification successfully sent"}
      } else {
        return {"message": "Please provide valid data"}
      }
    }
  },
  
  getLoginInfo: async (parent: unknown, args: { filter: any }, { prisma, req }: Context) => {
    const currentUser = await protect(req);
    if (currentUser) {
      const skip = (args.filter.pageNumber - 1) * 15;
      const take = args.filter.take ? args.filter.take : 15;
      const where: any = {};
      const now = args.filter.date ? new Date(args.filter.date) : new Date();
      if (!args.filter.date) now.setDate(now.getDate() - 1);
  
      const startOfDay = new Date(now);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(startOfDay);
      endOfDay.setHours(23, 59, 59, 999);
      where.loginDate = { gte: startOfDay, lte: endOfDay };
  
      if (args.filter.searchText) {
        where.AND = [
          {
            [args.filter.type === UserLoginType.Recruiter ? "recruiter" : "user"]: {
              mobileNumber: await encode(args.filter.searchText)
            }
          }
        ];
      }
  
      if (args.filter.type === UserLoginType.Recruiter) {
        const recruiterInfo = await prisma.recruiterlogininfo.findMany({
          where,
          skip,
          take,
          include: {
            loginType: { select: { id: true, name: true } },
            logoutType: { select: { id: true, name: true } },
            recruiter: {
              select: {
                id: true,
                name: true,
                mobileNumber: true,
                company: true,
                RecruiterRole: { select: { id: true, name: true } },
                HiringFor:  { select: { id: true, name: true } }
              }
            }
          }
        });  
        for (const item of recruiterInfo) {
          try {
            if (item && item.recruiter && item.recruiter.mobileNumber) {
              item.recruiter.mobileNumber = await decode(item.recruiter.mobileNumber);
            }
          } catch (error) {
            console.log("Failed to decode mobileNumber: recruiter login info", error);
          }
        }
        const recruiterCount = await prisma.recruiterlogininfo.count({
          where
        });
        return {loginDetails: recruiterInfo, totalCount : recruiterCount};
      } else {
        const userInfo = await prisma.userlogininfo.findMany({
          where,
          skip,
          take,
          include: {
            loginType: { select: { id: true, name: true } },
            logoutType: { select: { id: true, name: true } },
            user: {
              select: {
                id: true,
                name: true,
                mobileNumber: true
              }
            }
          }
        });
        for (const item of userInfo) {
          try {
            if (item && item.user && item.user.mobileNumber) {
              item.user.mobileNumber = await decode(item.user.mobileNumber);
            }
          } catch (error) {
            console.log("Failed to decode mobileNumber: user login info", error);
          }
        }
        const userCount = await prisma.userlogininfo.count({
          where
        });
  
        return {loginDetails: userInfo, totalCount : userCount};
      }
    }
  }
  
};
