// import { Queue } from "bullmq";
// import setUpWorker from "./bullmq-worker";

// const myQueue: Queue = new Queue("JOBS", {
//   connection: {
//     host: process.env.REDIS_HOST,
//     port: process.env.REDIS_PORT as unknown as number,
//   },
// });
// // myQueue.setMaxListeners(myQueue.getMaxListeners() + 100);

// setUpWorker();

// const addJobToQueue = (data: any): Promise<any> => {
//   return myQueue.add(data.jobName, data.value, {
//     removeOnComplete: {
//       age: process.env.BULLREMOVEONCOMPLETE as unknown as number,
//     },
//     removeOnFail: {
//       age: 24 * (process.env.BULLREMOVEONCOMPLETE as unknown as number),
//     },
//   });

//   // myQueue.close();
// };

// const closeQueueAndExit = async (): Promise<void> => {
//   // Close the BullMQ queue after all jobs are added
//   await myQueue.close();
//   process.exit(); // Exit the terminal
// };

// const handleShutdown = async (): Promise<void> => {
//   // Add any additional cleanup operations here
//   await closeQueueAndExit();
// };

// // Listen for process signals to trigger graceful shutdown
// process.on("SIGINT", handleShutdown);
// process.on("SIGTERM", handleShutdown);

// export { addJobToQueue, closeQueueAndExit };
