/*
  Warnings:

  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.

*/

-- AlterTable
ALTER TABLE `Company` RENAME TO `company`;
ALTER TABLE `Recruiter` RENAME TO `recruiter`;
ALTER TABLE `RecruiterRole` RENAME TO `recruiterrole`;

-- DropForeignKey
ALTER TABLE `company` DROP FOREIGN KEY `Company_industryId_fkey`;

-- DropForeignKey
ALTER TABLE `company` DROP FOREIGN KEY `Company_numberOfEmployeesId_fkey`;

-- DropForeignKey
ALTER TABLE `company` DROP FOREIGN KEY `Company_recruiterId_fkey`;

-- DropForeignKey
ALTER TABLE `recruiter` DROP FOREIGN KEY `Recruiter_recruiterRoleId_fkey`;

-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_recruiterRoleId_fkey` FOREIGN KEY (`recruiterRoleId`) REFERENCES `recruiterrole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_industryId_fkey` FOREIGN KEY (`industryId`) REFERENCES `Industry`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_numberOfEmployeesId_fkey` FOREIGN KEY (`numberOfEmployeesId`) REFERENCES `NumberOfEmployees`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- RenameIndex
ALTER TABLE `company` RENAME INDEX `Company_recruiterId_key` TO `company_recruiterId_key`;

-- RenameIndex
ALTER TABLE `recruiter` RENAME INDEX `Recruiter_email_key` TO `recruiter_email_key`;

-- RenameIndex
ALTER TABLE `recruiter` RENAME INDEX `Recruiter_mobileNumber_key` TO `recruiter_mobileNumber_key`;
