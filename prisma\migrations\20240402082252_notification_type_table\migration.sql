-- AlterTable
ALTER TABLE `usernotification` ADD COLUMN `usernotificationtypeId` INTEGER NULL;

-- CreateTable
CREATE TABLE `usernotificationtype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON>ignKey
ALTER TABLE `usernotification` ADD CONSTRAINT `usernotification_usernotificationtypeId_fkey` FOREIGN KEY (`usernotificationtypeId`) REFERENCES `usernotificationtype`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
