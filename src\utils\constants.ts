// the below type will autogenerate id as per the order provided. 
// if we need to change id we need to rearrange the order.
// The enum approach is straightforward and automatically handles immutability and type safety.
export enum RecruiterStatus {
  CREATED = 1,
  RECRUITERDETAILS,
  COMPANYCREATED,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ER<PERSON><PERSON><PERSON><PERSON><PERSON>FIED,
  CO<PERSON><PERSON><PERSON><PERSON><PERSON>FI<PERSON>,
  HOLD,
  ACTIVE,
  PAUSED,
  <PERSON>LETED,
  BLOCKED,
  UNBLOCKED
}


export const whatsappTemplateType  = {
  REGISTRATION_INCOMPLETE : "registrations_incomplete",
  VERIFICATION_PENDING:"verification_pending",
  PROFILE_VERIFIED:"profile_verified",
  JOB_POSTING_INCOMPLETE:"job_posting_incomplete",
  JOBPOSTED_SUCCESSFULLY:"jobpost_successful"
}

export enum JobStatus {
  ACTIVE = 1,
  HOLD,
  DRAFT,
  DELETED,
  EXPIRED
}

export enum CreditApprovalStatus {
  REQUESTED =1,
  INPROGRESS,
  HOLD,
  <PERSON>EPTED,
  REJECTED
}

export enum PaymentStauts {
  INPROGRESS = 1,
  SUCCE<PERSON>,
  FAILED
}

export enum CreditTransactionType {
  CREDIT = 1,
  DEBIT,
  REQUESTCREDIT,
  REFUND
}

export enum CreditSpendType {
  JOBCREDIT = 1,
  DBCREDIT,
  JOBDBCREDIT
}

export enum CreditDeductionCriteria {
  JOBPOST = 1,
  EDITJOB,
  BOOSTJOB,
  REACTIVEJOB,
  JOBCLOSE
}

export const UserLoginType  = {
  Recruiter : "RECRUITER",
  Candidate : "CANDIDATE"
}

