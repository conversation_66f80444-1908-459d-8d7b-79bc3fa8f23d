interface DateObject {
  year: number;
  month: number;
  date: number;
}

class DateUtils {
  static convert(d: Date | [number, number, number] | number | string | DateObject): any {
    return (
      d instanceof Date ? d :
      Array.isArray(d) ? new Date(d[0], d[1], d[2]) :
      typeof d === 'number' ? new Date(d) :
      typeof d === 'string' ? new Date(d) :
      typeof d === 'object' ? new Date(d.year, d.month, d.date) :
      NaN
    );
  }

  static compare(a: Date | [number, number, number] | number | string | DateObject,
                 b: Date | [number, number, number] | number | string | DateObject): any {
    return (
      isFinite(a = this.convert(a).valueOf()) &&
      isFinite(b = this.convert(b).valueOf()) ? (+(a > b) - +(a < b)) : NaN
    );
  }

  static inRange(d: Date | [number, number, number] | number | string | DateObject,
                 start: Date | [number, number, number] | number | string | DateObject,
                 end: Date | [number, number, number] | number | string | DateObject): any {
    return (
      isFinite(d = this.convert(d).valueOf()) &&
      isFinite(start = this.convert(start).valueOf()) &&
      isFinite(end = this.convert(end).valueOf()) ?
      start <= d && d <= end :
      NaN
    );
  }
}

export default DateUtils;