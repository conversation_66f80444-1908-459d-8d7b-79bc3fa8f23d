import axios from "axios";

export const generateOtp = (): string => {
  const digits = "0123456789";
  const otpLength = 4;
  let OTP = "";
  for (let i = 0; i < otpLength; i++) {
    OTP += digits[Math.floor(Math.random() * 10)];
  }
  return OTP;
};

export const sendSMS = (mobileNumber: string, otp: string) => {
  try {

    interface ApiResponse {
      data: string;
      status: number;
    }

    interface ApiError {
      message: string;
      status: number;
    }

    const msgtext =
      "<#> Dear Member, Use" +
      " " +
      otp +
      " " +
      "as the OTP to login to your profile on Astromatch App. " + process.env.SMS_OTP_HASH;

    const smsSendDomain = process.env.SMS_GATEWAY;
    axios.defaults.timeout = 15000;

    if (smsSendDomain) {
      const getResponse = async (): Promise<ApiResponse | ApiError> => {
        try {
          const { data } = await axios.post<ApiResponse>(smsSendDomain, null, {
            params: {
              enterpriseid: process.env.SMS_USERNAME,
              subEnterpriseid: process.env.SMS_USERNAME,
              pusheid: process.env.SMS_USERNAME,
              pushepwd: process.env.SMS_PASSWORD,
              msisdn: `91${mobileNumber}`,
              sender: process.env.SMS_SENDER,
              msgtext,
            },
          });
          return data;
        } catch (error) {
          console.log("====error===", error)
          if (axios.isAxiosError(error)) {
            throw new Error("Error generating OTP");
          } else {
            throw new Error("different error than axios");
          }
        }
      };
      getResponse();
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: `Things exploded (${error.message})`,
      };
    }
  }
};
