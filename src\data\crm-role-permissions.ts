export const crmRolePermissions = [
  // Admin Role (roleId: 1) - Full access to everything
  { roleId: 1, permissionId: 1, isActive: true },  // user.create
  { roleId: 1, permissionId: 2, isActive: true },  // user.read
  { roleId: 1, permissionId: 3, isActive: true },  // user.update
  { roleId: 1, permissionId: 4, isActive: true },  // user.delete
  { roleId: 1, permissionId: 5, isActive: true },  // user.assign_role
  { roleId: 1, permissionId: 6, isActive: true },  // recruiter.read
  { roleId: 1, permissionId: 7, isActive: true },  // recruiter.update
  { roleId: 1, permissionId: 8, isActive: true },  // recruiter.approve
  { roleId: 1, permissionId: 9, isActive: true },  // recruiter.reject
  { roleId: 1, permissionId: 10, isActive: true }, // recruiter.block
  { roleId: 1, permissionId: 11, isActive: true }, // company.read
  { roleId: 1, permissionId: 12, isActive: true }, // company.update
  { roleId: 1, permissionId: 13, isActive: true }, // company.approve
  { roleId: 1, permissionId: 14, isActive: true }, // company.reject
  { roleId: 1, permissionId: 15, isActive: true }, // job.read
  { roleId: 1, permissionId: 16, isActive: true }, // job.update
  { roleId: 1, permissionId: 17, isActive: true }, // job.approve
  { roleId: 1, permissionId: 18, isActive: true }, // job.reject
  { roleId: 1, permissionId: 19, isActive: true }, // job.delete
  { roleId: 1, permissionId: 20, isActive: true }, // role.read
  { roleId: 1, permissionId: 21, isActive: true }, // role.create
  { roleId: 1, permissionId: 22, isActive: true }, // role.update
  { roleId: 1, permissionId: 23, isActive: true }, // role.delete
  { roleId: 1, permissionId: 24, isActive: true }, // system.admin
  { roleId: 1, permissionId: 25, isActive: true }, // reports.read
  { roleId: 1, permissionId: 26, isActive: true }, // notifications.send
  { roleId: 1, permissionId: 27, isActive: true }, // callback.read
  { roleId: 1, permissionId: 28, isActive: true }, // callback.update

  // User Role (roleId: 2) - Limited access for regular CRM users
  { roleId: 2, permissionId: 2, isActive: true },  // user.read (own profile only)
  { roleId: 2, permissionId: 3, isActive: true },  // user.update (own profile only)
  { roleId: 2, permissionId: 6, isActive: true },  // recruiter.read
  { roleId: 2, permissionId: 7, isActive: true },  // recruiter.update
  { roleId: 2, permissionId: 11, isActive: true }, // company.read
  { roleId: 2, permissionId: 12, isActive: true }, // company.update
  { roleId: 2, permissionId: 15, isActive: true }, // job.read
  { roleId: 2, permissionId: 16, isActive: true }, // job.update
  { roleId: 2, permissionId: 20, isActive: true }, // role.read (view only)
  { roleId: 2, permissionId: 27, isActive: true }, // callback.read
  { roleId: 2, permissionId: 28, isActive: true }, // callback.update
];
