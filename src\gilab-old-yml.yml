image: node:latest

variables:
    RUNNER_TAG: Xuwy8esu

cache:
  paths:
    - node_modules

stages:
  - lint
  - build
  - test
  - sonarscanner
  - deploy

before_script:
  - apt-get update -qq && apt-get install -y -qq sshpass rsync
  - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
  - 'which sshpass || ( apt-get update -y && apt-get install sshpass -y )'
  - |
    if [[ "$CI_COMMIT_REF_NAME" == "sprint" ]] || [[ "$CI_COMMIT_REF_NAME" == "develop" ]]; then
      echo "$SSH_PRIVATE_KEY" > id_ed25519
    elif [[ "$CI_COMMIT_REF_NAME" == "staging" ]]; then
      echo "$SSH_STG_PRIVATE_KEY" > id_ed25519
    fi
  - chmod 600 id_ed25519

build:
  stage: build
  script:
    - npm run build 
  allow_failure: false

eslint:
  stage: lint
  script:
    - npm run lint 
  allow_failure: false

sonarqube-check:
  stage: sonarscanner
  image: 
    name: node:lts
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  before_script:
    - add-apt-repository ppa:openjdk-r/ppa && apt-get update && apt-get install -y openjdk-11-jre wget unzip
    - wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.6.2.2472-linux.zip
    - unzip sonar-scanner-cli-4.6.2.2472-linux.zip
    - export PATH=$PATH:$PWD/sonar-scanner-4.6.2.2472-linux/bin
  script: 
    - sonar-scanner -Dsonar.login=${SONAR_TOKEN} -Dsonar.host.url=${SONAR_HOST_URL}
  #needs:
    #- test
  allow_failure: true
  #rules:
    #- if: $CI_COMMIT_BRANCH == 'master'
  
deploy_sprint:
  stage: deploy
  script:
    - npm install pm2 -g && pm2 deploy ecosystem.config.js sprint
  allow_failure: true
  rules:
    - if: $CI_COMMIT_BRANCH == 'sprint'

