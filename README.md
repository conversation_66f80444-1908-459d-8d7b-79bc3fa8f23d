# ProjectJJ-Backend



## Requirements
* Node 16.13 / 18.X / 20.X and later
* Typescript 4.7.X and later
* Graphql v16.6.0 and later
* MySql 8 and later

## Common setup
#### Clone the repo and install the dependencies.

```
<NAME_EMAIL>:jj/projectjj-backend.git

cd projectjj-backend

git checkout sprint

```

#### Setup dotenv in Local Setup

```
Create an .env file in the project directory.

Paste the environment variables from the team member.
```
#### To start the express server in development enviroment, run the following

```
npm install

npm run start:dev

```

##### Open http://localhost:4000 and take a look around.

## Prisma migration development environment

### DB migration
```
npx prisma migrate dev --name "name of the migration (ex: new-field)"
```

### Customize a migration file

#### To edit a migration file before applying it, the general procedure is the following
```
npx prisma migrate dev --create-only
```
##### Modify the generated SQL file and apply the modified SQL by running
```
npx prisma migrate dev
```
### To run a seed file
```
npx prisma db seed
```

### Prisma team migration
```
https://www.prisma.io/docs/guides/migrate/developing-with-prisma-migrate/team-development
```