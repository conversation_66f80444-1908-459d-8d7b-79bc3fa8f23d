{"name": "jj-crm-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start:dev": "npx nodemon", "build": "rimraf ./build && tsc", "start": "npm run build && node build/index.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint . --ext .ts src/", "pm2_sprint": "pm2 start ecosystem.config.js --env sprint", "allSeed": "npx prisma db seed -- --environment allSeed", "updateSeed": "npx prisma db seed -- --environment updatedSeed"}, "prisma": {"seed": "ts-node src/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/body-parser": "^1.19.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.11.20", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.0", "nodemon": "^2.0.22", "prisma": "^5.10.2", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "dependencies": {"@apollo/server": "^4.10.0", "@apollo/server-plugin-response-cache": "^4.1.3", "@apollo/utils.keyvadapter": "^3.1.0", "@aws-sdk/client-s3": "^3.521.0", "@aws-sdk/s3-request-presigner": "^3.521.0", "@keyv/redis": "^2.8.4", "@prisma/client": "^5.10.2", "@prisma/extension-read-replicas": "^0.3.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "bullmq": "^4.17.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "graphql": "^16.8.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "keyv": "^4.5.4", "nodemailer": "^6.9.10", "redis": "^4.6.13", "uuid": "^9.0.1"}}