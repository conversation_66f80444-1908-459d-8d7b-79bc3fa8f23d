import { PrismaClient } from "@prisma/client";
import { parseArgs } from 'node:util';
import {
  userStatus,
  specialization,
  courses,
  qualification,
  profileLanguages,
  skillsList,
  commuteMode,
  employementType,
  jobRole,
  joinPreference,
  shiftType,
  workingDays,
  appLanguage,
  workType,
  englishKnowledgeLevel,
  distance,
  deleteFeedback,
  reviewFeedback,
  suggestionFeedback,
  jobBenefits,
  industry,
  jobCategory,
  numberOfEmployees,
  recruiterRole,
  requiredAssets,
  WorkExperience,
  depositReason,
  interviewType,
  gender,
  degreeMode,
  jobRoleCategory,
  jobzAppStatus,
  workingDomain,
  reportJob,
  colleges,
  RecruiterStatus,
  designation,
  hiringFor,
  companyList,
  VerificationStatus,
  jobStatus,
  VerificationDocuments,
  crmUserRole,
  crmPermissions,
  crmRolePermissions,
  monthlySalaryRange,
  userNotificationType,
  emailDomain,
  jobTitle,
  paymentStatus,
  State,
  City,
  creditTransactionType,
  creditSpendType,
  creditDeductionCriteria,
  creditApprovalStatus,
  creditRequestType,
  loginType,
  logoutType,
  planType,
  gstSupplyType,
  refundStatus,
  invoiceDocType
} from "../src/data/index";
// import RedisServer from "./utils/redis_server";
import { addJobToQueue, closeQueueAndExit } from "./services/bullmq-queue";
import { PutBucketCorsCommand, S3Client } from "@aws-sdk/client-s3";
import { CandidateProfileStatus } from "./data/candidate-profile-status";
import { recruiterAppFeedback } from "./data/recruiter-app-feedback";
import { jobReview } from "./data/job-review-feedback";
import { recruiterRequestFeedback } from "./data/recruiter-request-feedback";
import { recruiterReportCandidate } from "./data/recruiter_report_candidate";
import { requestCallbackStatus } from "./data/request-callback-status";

const prisma = new PrismaClient();
// const redis = new RedisServer();

const options = {
  environment: { type: 'string' },
} as const

async function main() {
  const {
    values: { environment },
  } = parseArgs({ options })

  switch (environment) {
    case 'updatedSeed':

      await addJobToQueue({
        jobName: "paymentStatus",
        value: await Promise.all(
          paymentStatus.map((data) => 
            prisma.paymentstatus.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "creditTransactionType",
        value: await Promise.all(
          creditTransactionType.map((data) => 
            prisma.credittransactiontype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "creditSpendType",
        value: await Promise.all(
          creditSpendType.map((data) => 
            prisma.creditspendtype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "creditDeductionCriteria",
        value: await Promise.all(
          creditDeductionCriteria.map((data) => 
            prisma.creditdeductioncriteria.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "creditapprovalstatus",
        value: await Promise.all(
          creditApprovalStatus.map((data) => 
            prisma.creditrequestapprovestatus.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "creditrequesttype",
        value: await Promise.all(
          creditRequestType.map((data) => 
            prisma.creditrequesttype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "plantype",
        value: await Promise.all(
          planType.map((data) => 
            prisma.plantype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "gstsupplytype",
        value: await Promise.all(
          gstSupplyType.map((data) => 
            prisma.gstsupplytype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "refundstatus",
        value: await Promise.all(
          refundStatus.map((data) => 
            prisma.refundstatus.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });
      await addJobToQueue({
        jobName: "invoicetype",
        value: await Promise.all(
          invoiceDocType.map((data) => 
            prisma.invoicedoctype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });

    break;

    case 'allSeed':
      await addJobToQueue({
        jobName: "state",
        value: await Promise.all(
          State.map((detail) =>
            prisma.state.upsert({
              where: {
                id:detail.id
              },
              update: {
                ...detail,
              },
              create: {
                ...detail,
              },
            })
          )
        )
      });
      
      await addJobToQueue({
        jobName: "city",
        value: await Promise.all(
          City.map((detail) =>
            prisma.city.upsert({
              where: {
                id:detail.id
              },
              update: {
                ...detail,
              },
              create: {
                ...detail,
              },
            })
          )
        )
      });

      await addJobToQueue({
        jobName: "emailDomains",
        value: await prisma.emaildomains.createMany({
          data: emailDomain,
        })
      });

      await addJobToQueue({
        jobName: "appLanguage",
        value: await prisma.appLanguage.createMany({
          data: appLanguage,
        }),
      });

      await addJobToQueue({
        jobName: "userStatus",
        value: await prisma.userStatus.createMany({
          data: userStatus,
        }),
      });

      await addJobToQueue({
        jobName: "jobzAppStatus",
        value: await prisma.jobzAppStatus.createMany({
          data: jobzAppStatus,
        }),
      });

      await addJobToQueue({
        jobName: "deleteFeedback",
        value: await prisma.deleteFeedback.createMany({
          data: deleteFeedback,
        }),
      });

      await addJobToQueue({
        jobName: "qualification",
        value: await prisma.qualificationType.createMany({
          data: qualification,
        }),
      });

      await addJobToQueue({
        jobName: "courses",
        value: await prisma.course.createMany({
          data: courses,
        }),
      });

      await addJobToQueue({
        jobName: "specialization",
        value: await prisma.specialization.createMany({
          data: specialization,
        }),
      });

      await addJobToQueue({
        jobName: "degreeMode",
        value: await prisma.degreeMode.createMany({
          data: degreeMode,
        }),
      });

      await addJobToQueue({
        jobName: "profileLanguages",
        value: await prisma.profileLanguages.createMany({
          data: profileLanguages,
        }),
      });

      await addJobToQueue({
        jobName: "skillsList",
        value: await prisma.skillList.createMany({
          data: skillsList,
        }),
      });

      await addJobToQueue({
        jobName: "commuteMode",
        value: await prisma.commuteMode.createMany({
          data: commuteMode,
        }),
      });

      await addJobToQueue({
        jobName: "employementType",
        value: await prisma.employmentType.createMany({
          data: employementType,
        }),
      });

      await addJobToQueue({
        jobName: "jobRoleCategory",
        value: await prisma.jobRoleCategory.createMany({
          data: jobRoleCategory,
        }),
      });

      await addJobToQueue({
        jobName: "jobRole",
        value: await prisma.jobRole.createMany({
          data: jobRole,
        }),
      });

      await addJobToQueue({
        jobName: "workingDomain",
        value: await prisma.workingDomainList.createMany({
          data: workingDomain,
        }),
      });

      await addJobToQueue({
        jobName: "shiftType",
        value: await prisma.shiftType.createMany({
          data: shiftType,
        }),
      });

      await addJobToQueue({
        jobName: "workingDays",
        value: await prisma.workingDays.createMany({
          data: workingDays,
        }),
      });

      await addJobToQueue({
        jobName: "workType",
        value: await prisma.workType.createMany({
          data: workType,
        }),
      });

      await addJobToQueue({
        jobName: "englishKnowledgeLevel",
        value: await prisma.englishKnowledgeLevel.createMany({
          data: englishKnowledgeLevel,
        }),
      });

      await addJobToQueue({
        jobName: "distance",
        value: await prisma.distance.createMany({
          data: distance,
        }),
      });

      await addJobToQueue({
        jobName: "joinPreference",
        value: await prisma.joinPreference.createMany({
          data: joinPreference,
        }),
      });

      await addJobToQueue({
        jobName: "reviewFeedback",
        value: await prisma.userReviewFeedback.createMany({
          data: reviewFeedback,
        }),
      });

      await addJobToQueue({
        jobName: "suggestionFeedback",
        value: await prisma.suggestionFeedback.createMany({
          data: suggestionFeedback,
        }),
      });

      await addJobToQueue({
        jobName: "recruiterRole",
        value: await prisma.recruiterRole.createMany({
          data: recruiterRole,
        }),
      });

      await addJobToQueue({
        jobName: "requiredAssets",
        value: await prisma.requiredAssets.createMany({
          data: requiredAssets,
        }),
      });

      await addJobToQueue({
        jobName: "WorkExperience",
        value: await prisma.workExperience.createMany({
          data: WorkExperience,
        }),
      });

      await addJobToQueue({
        jobName: "depositReason",
        value: await prisma.depositReason.createMany({
          data: depositReason,
        }),
      });

      await addJobToQueue({
        jobName: "jobBenefits",
        value: await prisma.jobBenefits.createMany({
          data: jobBenefits,
        }),
      });

      await addJobToQueue({
        jobName: "numberOfEmployees",
        value: await prisma.numberOfEmployees.createMany({
          data: numberOfEmployees,
        }),
      });

      await addJobToQueue({
        jobName: "jobCategory",
        value: await prisma.jobCategory.createMany({
          data: jobCategory,
        }),
      });

      await addJobToQueue({
        jobName: "industry",
        value: await prisma.industry.createMany({
          data: industry,
        }),
      });

      await addJobToQueue({
        jobName: "interviewType",
        value: await prisma.interviewtype.createMany({
          data: interviewType,
        }),
      });

      await addJobToQueue({
        jobName: "gender",
        value: await prisma.gender.createMany({
          data: gender,
        }),
      });

      await addJobToQueue({
        jobName: "reportJob",
        value: await prisma.reportJob.createMany({
          data: reportJob,
        }),
      });

      await addJobToQueue({
        jobName: "colleges",
        value: await prisma.colleges.createMany({
          data: colleges,
        }),
      });

      await addJobToQueue({
        jobName: "notificationtype",
        value: await prisma.usernotificationtype.createMany({
          data: userNotificationType,
        }),
      });

      await addJobToQueue({
        jobName: "companyList",
        value: await prisma.companyList.createMany({
          data: companyList
        })
      })

      await addJobToQueue({
        jobName: "designation",
        value: await prisma.designation.createMany({
          data: designation,
        })
      });

      await addJobToQueue({
        jobName: "recruiterStatus",
        value: await prisma.recruiterStatus.createMany({
          data: RecruiterStatus,
        }),
      });

      await addJobToQueue({
        jobName: "hiringFor",
        value: await prisma.hiringFor.createMany({
          data: hiringFor,
        })
      });

      await addJobToQueue({
        jobName: "verificationStatus",
        value: await prisma.verificationstatus.createMany({
          data: VerificationStatus
        })
      });

      await addJobToQueue({
        jobName: "verificationDocs",
        value: await prisma.verificationdocs.createMany({
          data: VerificationDocuments
        })
      });

      await addJobToQueue({
        jobName: "crmUserRole",
        value: await prisma.crmuserrole.createMany({
          data: crmUserRole
        })
      });

      await addJobToQueue({
        jobName: "crmPermissions",
        value: await prisma.crmPermission.createMany({
          data: crmPermissions
        })
      });

      await addJobToQueue({
        jobName: "crmRolePermissions",
        value: await prisma.crmRolePermission.createMany({
          data: crmRolePermissions
        })
      });

      await addJobToQueue({
        jobName: "jobStatus",
        value: await prisma.jobStatus.createMany({
          data: jobStatus,
        }),
      });

      await addJobToQueue({
        jobName: "salaryRange",
        value: await prisma.monthlySalaryRange.createMany({
          data: monthlySalaryRange,
        }),
      });

      await addJobToQueue({
        jobName: "candidateProfileStatus",
        value: await prisma.candidateprofilestatus.createMany({
          data: CandidateProfileStatus,
        }),
      });

      await addJobToQueue({
        jobName: "recruiterAppFeedback",
        value: await prisma.recruiterappfeedback.createMany({
          data: recruiterAppFeedback,
        }),
      });

      await addJobToQueue({
        jobName: "jobReviewFeedback",
        value: await prisma.jobfeedback.createMany({
          data: jobReview,
        }),
      });

      await addJobToQueue({
        jobName: "recruiterCallbackFeedback",
        value: await prisma.recruiterrequestfeedback.createMany({
          data: recruiterRequestFeedback,
        }),
      });

      await addJobToQueue({
        jobName: "recruiterReportCandidate",
        value: await prisma.recruiterreportcandidate.createMany({
          data: recruiterReportCandidate,
        }),
      });

      await addJobToQueue({
        jobName: "requestCallbackStatus",
        value: await prisma.requestcallbackstatus.createMany({
          data: requestCallbackStatus,
        }),
      });

      await addJobToQueue({
        jobName: "state",
        value: await Promise.all(
          State.map((detail) =>
            prisma.state.upsert({
              where: {
                id:detail.id
              },
              update: {
                ...detail,
              },
              create: {
                ...detail,
              },
            })
          )
        )
      });
      
      await addJobToQueue({
        jobName: "city",
        value: await Promise.all(
          City.map((detail) =>
            prisma.city.upsert({
              where: {
                id:detail.id
              },
              update: {
                ...detail,
              },
              create: {
                ...detail,
              },
            })
          )
        )
      });

      await addJobToQueue({
        jobName: "jobTitle",
        value: await Promise.all(
          jobTitle.map((data) => 
            prisma.jobtitle.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });

      await addJobToQueue({
        jobName: "loginType",
        value: await Promise.all(
          loginType.map((data) => 
            prisma.logintype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });

      await addJobToQueue({
        jobName: "logoutType",
        value: await Promise.all(
          logoutType.map((data) => 
            prisma.logouttype.upsert({
              where: {
                id: data.id
              },
              update: {
                ...data
              },
              create: {
                ...data
              }
            })
          )
        )
      });

      break;
    default:
      break
  }
}

main()
  .then(async () => {
    await prisma.$disconnect();
    await closeQueueAndExit();
  })
  .catch(async (e) => {
    console.error("Error from seed", e);
    await prisma.$disconnect();
    process.exit(1);
  });

  // (async () => {
  //   const putcors = new PutBucketCorsCommand({
  //     Bucket: process.env.S3_BUCKETNAME,
  //     CORSConfiguration: {
  //       CORSRules: [
  //         {
  //           // Allow all headers to be sent to this bucket.
  //           AllowedHeaders: ["*"],
  //           // Allow only GET and PUT methods to be sent to this bucket.
  //           AllowedMethods: ["GET", "PUT"],
  //           // Allow only requests from the specified origin.
  //           AllowedOrigins: process.env.CORS_DOMAIN?.split(","),
  //           // Allow the entity tag (ETag) header to be returned in the response. The ETag header
  //           // The entity tag represents a specific version of the object. The ETag reflects
  //           // changes only to the contents of an object, not its metadata.
  //           ExposeHeaders: ["ETag"],
  //           // How long the requesting browser should cache the preflight response. After
  //           // this time, the preflight request will have to be made again.
  //           MaxAgeSeconds: 3600,
  //         },
  //       ],
  //     },
  //   });
    
  //   const client = new S3Client({
  //     credentials: {
  //       accessKeyId: process.env.S3_ACCESSKEY as string,
  //       secretAccessKey: process.env.S3_SECRETACCESS as string,
  //     },
  //     region: process.env.S3_REGION,
  //   });
  
  //   const response = await client.send(putcors);
  //   console.log("s3 cors add respone===", response);
  // })();
  