import { Context } from "../../context";
import { protect } from "../../middleware/auth";
import { responseHandler } from "../../utils/responseHandler";
import {
  CallbackStatusUpdateArgs,
  CallbackStatusUpdateInput,
  CallbackStatusUpdatePayload,
} from "../../types/requestCallback";

export const requestCallbackResolvers = {
  requestCallbackStatusUpdate: async (
    parent: unknown,
    {
      input: { recruiterRequestId,requestCallBackStatusId },
    }: CallbackStatusUpdateArgs<CallbackStatusUpdateInput>,
    { prisma, req }: Context
  ): Promise<CallbackStatusUpdatePayload> => {
    try{
      const currentUser = await protect(req);
      if (currentUser && currentUser.id) {        
              const updateRequeststatus =  await prisma.recruiterrequest.update({
                  where:{                    
                    id:+recruiterRequestId,
                  },
                  data:{
                    requestCallBackStatusId:+requestCallBackStatusId,
                    crmUserId:+currentUser.id
                  }
                });

              if(updateRequeststatus && updateRequeststatus.id) {
                return responseHandler(
                  "CallbackStatusUpdateErrors",
                  "Request status updated successfully!",
                  201
                );
              } else {
                return responseHandler(
                  "CallbackStatusUpdateErrors",
                  "Request status updated Failed!",
                  400
                );
              }
      }  else {
        return responseHandler("CallbackStatusUpdateErrors", "Invalid User", 401);
      }          
            } catch (error:any) {
              console.log(error,"error")
                return responseHandler("CallbackStatusUpdateErrors", "Request status updated", 400);
              }
            },

};
