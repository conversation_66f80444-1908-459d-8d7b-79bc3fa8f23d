import { Context } from "../../context";
import { protect } from "../../middleware/auth";
import { responseHandler } from "../../utils/responseHandler";
import {
  UpdateCompanyVerifyArgs,
  UpdateCompanyVerifyInput,
  UpdateCompanyVerifyPayload,
  UpdatePersonalVerifyArgs,
  UpdatePersonalVerifyInput,
  UpdatePersonalVerifyPayload,
  UpdateJobVerificationArgs,
  UpdateJobVerificationInput,
  UpdateJobVerificationPayload,
  UpdateCompanyStatusArgs,
  UpdateCompanyStatusInput,
  UpdateCompanyStatusPayload,
  CandidatePreferenceInput,
  InterviewDetailsInput,
  UpdateJobArgs,
  UpdateJobInput,
  UpdateJobPayload,
  UpdateJobDetailInput,
  companyNameArgs,
  companyNameInput,
  companyNamePayload,
  jobTitleArgs,
  jobTitleInput,
  jobTitlePayload,
  BlockRecruiterPayload,
  BlockRecruiterArgs,
  BlockRecruiterInput,
  ReActiveJobPayload,
  ReActiveJobArgs,
  ReActiveJobInput
} from "../../types/verification.types";
// import { addJobToQueue } from "../../services/bullmq-queue";
import { decode, encode } from "../../utils/crypto";
import { createHmac } from "crypto";
import { RecruiterStatus, whatsappTemplateType, JobStatus } from "../../utils/constants";
import { sendWhatsAppNotification } from "../../services/notification";
import verifyRecruiter from "../../templates/email/verifyRecruiterTemplate";
import MailService from "../../services/mailservice";
import verifyJob from "../../templates/email/verifyJobTemplate";

export const verificationResolvers = {
  updatePersonalVerification: async (
    parent: unknown,
    {
      input: { recruiterId, verifyStatus, personalRejectReason },
    }: UpdatePersonalVerifyArgs<UpdatePersonalVerifyInput>,
    { prisma, req }: Context
  ): Promise<UpdatePersonalVerifyPayload> => {
    try {
      if (
        !recruiterId &&
        (verifyStatus === null || undefined) &&
        !personalRejectReason
      ) {
        return responseHandler(
          "UpdatePersonalVerificationErrors",
          "You must provide a required field",
          400
        );
      } else {
        const currentUser = await protect(req);

        if (currentUser) {
          const getRecruiter: any = await prisma.recruiter.findUnique({
            where: {
              id: +recruiterId,
            },
          });

          if (
            getRecruiter &&
            getRecruiter.id &&
            getRecruiter.id === +recruiterId
          ) {
            switch (verifyStatus) {
              case true: {
                const [approveRecruiter, updateRecruiter] =
                  await prisma.$transaction([
                    prisma.recruiterdocument.update({
                      where: {
                        recruiterId: +getRecruiter.id,
                      },
                      data: {
                        personalstatusId: 3,
                        personalRejectReason: null,
                      },
                    }),

                    prisma.recruiter.update({
                      where: {
                        id: +getRecruiter.id,
                      },
                      data: {
                        isPersonalVerified: true,
                      },
                    }),
                  ]);

                if (approveRecruiter && approveRecruiter.id) {
                  const decodenumber = getRecruiter.mobileNumber ? await decode(getRecruiter.mobileNumber) : null;
                  const decodedEmail = getRecruiter.email ? await decode(getRecruiter.email) : null;

                  console.log("===decodenumber==update perfonal verify", decodenumber)
                  console.log("===decodedEmail==update perfonal verify", decodedEmail)

                  if(decodedEmail) {
                    //email notification
                    const emailTemplate = verifyRecruiter(
                      getRecruiter.name,
                    );

                    const mailService = MailService.getInstance();
        
                    await mailService.sendMail(1234, {
                      from: process.env.SMTP_SENDER,
                      to: decodedEmail,
                      subject: "ManyJobs: Profile verification",
                      html: emailTemplate.html,
                    });
                  } else {
                    console.log(`Recruiter ${getRecruiter.id} valid email not exist`)
                  }

                  if(decodenumber) {
                  //whatsapp notification
                    if(getRecruiter.isCompanyVerified && getRecruiter.whatsAppUpdate){
                      await sendWhatsAppNotification(decodenumber,getRecruiter,whatsappTemplateType.PROFILE_VERIFIED)
                      await sendWhatsAppNotification(decodenumber,getRecruiter,whatsappTemplateType.JOB_POSTING_INCOMPLETE)
                    }
                  } else {
                    console.log(`Recruiter ${getRecruiter.id} valid mobile number not exist`)
                  }

                  return responseHandler(
                    "UpdatePersonalVerificationErrors",
                    "Recruiter Personal Document verified",
                    200
                  );
                } else {
                  return responseHandler(
                    "UpdatePersonalVerificationErrors",
                    "Failed to update Recruiter Personal Document",
                    400
                  );
                }
              }

              case false: {
                const [rejectRecruiter, updateRecruiter] =
                  await prisma.$transaction([
                    prisma.recruiterdocument.update({
                      where: {
                        recruiterId: +getRecruiter.id,
                      },
                      data: {
                        personalstatusId: 4,
                        personalRejectReason,
                      },
                    }),

                    prisma.recruiter.update({
                      where: {
                        id: +getRecruiter.id,
                      },
                      data: {
                        isPersonalVerified: false,
                      },
                    }),
                  ]);

                if (rejectRecruiter && rejectRecruiter.id) {
                  return responseHandler(
                    "UpdatePersonalVerificationErrors",
                    "Recruiter Personal Document Rejected",
                    200
                  );
                } else {
                  return responseHandler(
                    "UpdatePersonalVerificationErrors",
                    "Failed to reject the Recruiter Personal Document",
                    400
                  );
                }
              }
            }
          } else {
            return responseHandler(
              "UpdatePersonalVerificationErrors",
              "Recruiter not found",
              400
            );
          }
        } else {
          return responseHandler(
            "CompanyDocVerifyErrors",
            "Recruiter not autorized",
            401
          );
        }
      }
    } catch (error: any) {
      console.log("===error==", error);
      return responseHandler(
        "UpdatePersonalVerificationErrors",
        "unknown issue while verify personal doc",
        400
      );
    }
  },

  updateCompanyVerification: async (
    parent: unknown,
    {
      input: { recruiterId ,companyId,comapnyListId, verifyStatus, companyRejectReason, companyNameStatus },
    }: UpdateCompanyVerifyArgs<UpdateCompanyVerifyInput>,
    { prisma, req }: Context
  ): Promise<UpdateCompanyVerifyPayload> => {
    try {
      if (
        !recruiterId &&
        (verifyStatus === null || undefined) &&
        !companyRejectReason && !companyId && !comapnyListId
      ) {
        return responseHandler(
          "UpdateCompanyVerificationErrors",
          "You must provide a required field",
          400
        );
      } else {
        const currentUser = await protect(req);

        if (currentUser) {
          const getRecruiter = await prisma.recruiter.findUnique({
            where: {
              id: +recruiterId,
            },
          });

          if (
            getRecruiter &&
            getRecruiter.id &&
            getRecruiter.id === +recruiterId
          ) {
            switch (verifyStatus) {
              case true: {
                const [approveCompany, updateRecruiter] =
                  await prisma.$transaction([
                    prisma.recruiterdocument.update({
                      where: {
                        recruiterId: +getRecruiter.id,
                      },
                      data: {
                        companystatusId: 3,
                        companyRejectReason: null,
                      },
                    }),

                    prisma.recruiter.update({
                      where: {
                        id: +getRecruiter.id,
                      },
                      data: {
                        isCompanyVerified: true,
                      },
                    }),

                    prisma.company.update({
                      where: {
                        id: +companyId
                      },
                      data:{
                        isCompanyVerified: true,
                      }
                    }),
                  ]);
                  if(companyNameStatus) {
                    await prisma.companyList.update({
                      where:{
                        id: +comapnyListId
                      },
                      data:{
                        isActive: true
                      }
                    })
                  }
                 
                if (approveCompany && approveCompany.id) {
                  const decodenumber=await decode(getRecruiter.mobileNumber);
                  if(getRecruiter.isPersonalVerified && getRecruiter.whatsAppUpdate){
                    await sendWhatsAppNotification(decodenumber,getRecruiter,whatsappTemplateType.PROFILE_VERIFIED)
                    await sendWhatsAppNotification(decodenumber,getRecruiter,whatsappTemplateType.JOB_POSTING_INCOMPLETE)
                  }
                  return responseHandler(
                    "UpdateCompanyVerificationErrors",
                    "Recruiter Company Document verified",
                    200
                  );
                } else {
                  return responseHandler(
                    "UpdateCompanyVerificationErrors",
                    "Failed to update Recruiter Company Document",
                    400
                  );
                }
              }

              case false: {
                const [rejectCompany, updateRecruiter] =
                  await prisma.$transaction([
                    prisma.recruiterdocument.update({
                      where: {
                        recruiterId: +getRecruiter.id,
                      },
                      data: {
                        companystatusId: 4,
                        companyRejectReason,
                      },
                    }),

                    prisma.recruiter.update({
                      where: {
                        id: +getRecruiter.id,
                      },
                      data: {
                        isCompanyVerified: false,
                      },
                    }),

                    prisma.company.update({
                      where: {
                        id: +companyId
                      },
                      data:{
                        isCompanyVerified: false,
                      }
                    }),

                    // prisma.companyList.update({
                    //   where:{
                    //     id: +comapnyListId
                    //   },
                    //   data:{
                    //     isActive: false
                    //   }
                    // })
                  ]);

                if (rejectCompany && rejectCompany.id) {
                  return responseHandler(
                    "UpdateCompanyVerificationErrors",
                    "Recruiter Company Document Rejected",
                    200
                  );
                } else {
                  return responseHandler(
                    "UpdateCompanyVerificationErrors",
                    "Failed to reject the Recruiter Company Document",
                    400
                  );
                }
              }
            }
          } else {
            return responseHandler(
              "UpdateCompanyVerificationErrors",
              "Recruiter not found",
              400
            );
          }
        } else {
          return responseHandler(
            "CompanyDocVerifyErrors",
            "Recruiter not authorized",
            401
          );
        }
      }
    } catch (error: any) {
      return responseHandler(
        "UpdateCompanyVerificationErrors",
        "unknown issue while verify Company doc",
        400
      );
    }
  },

  updateJobVerification: async (
    parent: unknown,
    {
      input: { recruiterId, jobId, verifyStatus, jobRejectReason },
    }: UpdateJobVerificationArgs<UpdateJobVerificationInput>,
    { prisma, req }: Context
  ): Promise<UpdateJobVerificationPayload> => {
    try {
      if (
        !jobId && !recruiterId &&
        (verifyStatus === null || undefined) &&
        !jobRejectReason
      ) {
        return responseHandler(
          "UpdateJobVerificationErrors",
          "You must provide a required field",
          400
        );
      } else {
        const currentUser = await protect(req);

        if (currentUser) {
          const getRecruiter: any = await prisma.recruiter.findUnique({
            where: {
              id: +recruiterId,
            },
          });

          if (
            getRecruiter &&
            getRecruiter.id &&
            getRecruiter.id === +recruiterId
          ) {
            switch (verifyStatus) {
              case true: {
                const approveJob: any = await prisma.job.update({
                  where: {
                    id: +jobId,
                  },
                  include: {
                    JobDetail: true,
                    candidatePreference: true,
                    interviewPreference: true,
                    recruiter:true
                  },
                  data: {
                    jobStatusId: 1,
                    // companyRejectReason: null,
                  },
                });
                // if(jobTitleStatus){
                //   await prisma.jobtitle.update({
                //     where: {
                //       id: +jobTitleId
                //     },
                //     data:{
                //       isActive: true
                //     }
                //   })
                // }

                if (approveJob && approveJob.id) {
                  console.log("=====approvdjob===", approveJob);

                  // try {
                  //   const solrUpdateData = {
                  //     "id": approveJob.id,
                  //     "jobId": approveJob.id,
                  //     "companyName": approveJob.companyName,
                  //     "recruiterId": approveJob.recruiterId,
                  //     "companyId": approveJob.companyId,
                  //     "companyTypeId": approveJob.companyTypeId,
                  //     "companyListId": approveJob.companyListId,
                  //     "consultantId": approveJob.consultantId,
                  //     "jobStatusId": approveJob.jobStatusId,
                  //     "JobDetail": {
                  //       "companyName": approveJob.JobDetail?.companyName,
                  //       "jobTitle": approveJob.JobDetail?.jobTitle,
                  //       "jobCategoryId": approveJob.JobDetail?.jobCategoryId,
                  //       "jobLocation": approveJob.JobDetail?.jobLocation,
                  //       "buildingName": approveJob.JobDetail?.buildingName,
                  //       "area": approveJob.JobDetail?.area,
                  //       "city": approveJob.JobDetail?.city,
                  //       "state": approveJob.JobDetail?.state,
                  //       "zipcode": approveJob.JobDetail?.zipcode,
                  //       "latitude": approveJob.JobDetail?.latitude,
                  //       "longitude": approveJob.JobDetail?.longitude,
                  //       "location": `${approveJob.JobDetail?.latitude} ${approveJob.JobDetail?.longitude}`,
                  //       "numberOpenings": approveJob.JobDetail?.numberOpenings,
                  //       "workTypeId": approveJob.JobDetail?.workTypeId,
                  //       "jobUrgencyId": approveJob.JobDetail?.jobUrgencyId,
                  //       "jobDescription": approveJob.JobDetail?.jobDescription,
                  //       "minMonthSalary": approveJob.JobDetail?.minMonthSalary,
                  //       "maxMonthSalary": approveJob.JobDetail?.maxMonthSalary,
                  //       "isIncentives": approveJob.JobDetail?.isIncentives,
                  //       "avgMonthlyIncentive": approveJob.JobDetail?.avgMonthlyIncentive,
                  //       "workingDaysId": approveJob.JobDetail?.workingDaysId,
                  //       "feesDeposit": approveJob.JobDetail?.feesDeposit,
                  //       "depositAmount": approveJob.JobDetail?.depositAmount,
                  //       "depositReasonId": approveJob.JobDetail?.depositReasonId,
                  //       "additionalInformation": approveJob.JobDetail?.additionalInformation,
                  //       "createdAt": approveJob.JobDetail?.createdAt,
                  //       "updatedAt": approveJob.JobDetail?.updatedAt,
                  //       "companyId": approveJob.JobDetail?.companyId,
                  //       "shiftTypeId": approveJob.JobDetail?.shiftTypeId,
                  //       "employmentTypeId": approveJob.JobDetail?.employmentTypeId,
                  //     },
                  //     "candidatePreference": {
                  //       "genderId": approveJob.candidatePreference?.genderId,
                  //       "educationId": approveJob.candidatePreference?.educationId,
                  //       "workExperienceId": approveJob.candidatePreference?.workExperienceId,
                  //       "minExperience": approveJob.candidatePreference?.minExperience,
                  //       "maxExperience": approveJob.candidatePreference?.maxExperience,
                  //       "applicationRadiusId": approveJob.candidatePreference?.applicationRadiusId,
                  //       "englishKnowledgeId": approveJob.candidatePreference?.englishKnowledgeId,
                  //       "isAssetsRequired": approveJob.candidatePreference?.isAssetsRequired,
                  //       "createdAt": approveJob.candidatePreference?.createdAt,
                  //       "updatedAt": approveJob.candidatePreference?.updatedAt,
                  //     },
                  //     "interviewPreference": {
                  //       "hrName": approveJob.interviewPreference?.hrName,
                  //       "hrContactNumber": await decode(approveJob.interviewPreference ? approveJob.interviewPreference?.hrContactNumber : ''),
                  //       "interviewTypeId": approveJob.interviewPreference?.interviewTypeId,
                  //       "createdAt": approveJob.interviewPreference?.createdAt,
                  //       "updatedAt": approveJob.interviewPreference?.updatedAt,
                  //     }
                  //   }

                  //   console.log("===solrUpdateData====", solrUpdateData)
                  //   await addJobToQueue({
                  //     jobName: 'solrJob',
                  //     value: solrUpdateData
                  //   });
                  // } catch (error:any) {
                  //   console.log("=====error solr ", error.response.data)
                  // }


                  //email notification
                  if(getRecruiter.email) {
                    const decodedEmail = await decode(getRecruiter.email)
                  const emailTemplate = verifyJob(
                    approveJob.JobDetail?.jobTitle,
                    approveJob.recruiter.name,
                    approveJob.companyName
                  );
                  const mailService = MailService.getInstance();
      
                  await mailService.sendMail(1234, {
                    from: process.env.SMTP_SENDER,
                    to: decodedEmail,
                    subject: "ManyJobs: Job posting update",
                    html: emailTemplate.html,
                  });
                  }
                  

                  const recruiterDetail = {
                    name: approveJob.recruiter.name,
                    mobileNumber: approveJob.recruiter.mobileNumber,
                    jobTitle: approveJob.JobDetail?.jobTitle,
                    companyName: approveJob.companyName
                  }
                  //whatsapp notification
                  const decodeMobileNumber=await decode(getRecruiter.mobileNumber)
                  if(getRecruiter.whatsAppUpdate) {
                    await sendWhatsAppNotification(decodeMobileNumber, recruiterDetail, whatsappTemplateType.JOBPOSTED_SUCCESSFULLY);
                  }
                  return responseHandler(
                  "UpdateJobVerificationErrors",
                  "Recruiter Request job approved",
                  200
                );
                } else {
                  return responseHandler(
                    "UpdateJobVerificationErrors",
                    "Failed to approve the recruiter job",
                    400
                  );
                }
              }

              case false: {
                const rejectJob = await prisma.job.update({
                  where: {
                    id: +jobId,
                  },
                  data: {
                    jobStatusId: 1,
                    // companyRejectReason: null,
                  },
                });

                if (rejectJob && rejectJob.id) {
                  return responseHandler(
                    "UpdateJobVerificationErrors",
                    "Request Job Rejected",
                    200
                  );
                } else {
                  return responseHandler(
                    "UpdateJobVerificationErrors",
                    "Failed to reject the Job",
                    400
                  );
                }
              }
            }
          } else {
            return responseHandler(
              "UpdateJobVerificationErrors",
              "Recruiter not found",
              400
            );
          }
        } else {
          return responseHandler(
            "UpdateJobVerificationErrors",
            "Recruiter not autorized",
            401
          );
        }
      }
    } catch (error: any) {
      console.log("===error==", error);
      return responseHandler(
        "UpdateJobVerificationErrors",
        "unknown issue while verify Company doc",
        400
      );
    }
  },

  updateCompanyStatus: async (
    parent: unknown,
    {
      input: { recruiterId, companyId },
    }: UpdateCompanyStatusArgs<UpdateCompanyStatusInput>,
    { prisma, req }: Context
  ): Promise<UpdateCompanyStatusPayload> => {
    try {
      if (
        !recruiterId && !companyId
      ) {
        return responseHandler(
          "updateCompanyStatusError",
          "You must provide a required field",
          400
        );
      } else {
        const currentUser =  await protect(req);

        if (currentUser) {
          const getRecruiter = await prisma.recruiter.findUnique({
            where: {
              id: +recruiterId,
            },
          });
          if (
            getRecruiter &&
            getRecruiter.id &&
            getRecruiter.id === +recruiterId
          ) {
            await prisma.companyList.update({
              where: {
                id: +companyId
              },
              data: {
                isActive: true
              }
            })
            return responseHandler(
              "updateCompanyStatusError",
              "Requested Company approved",
              200
            );

          } else {
            return responseHandler(
              "updateCompanyStatusError",
              "Recruiter not found",
              400
            );
          }
        } else {
          return responseHandler(
            "updateCompanyStatusError",
            "Recruiter not autorized",
            401
          );
        }
      }
    } catch (error: any) {
      console.log("===error==", error.response.data);
      return responseHandler(
        "updateCompanyStatusError",
        "unknown issue while verify Company doc",
        400
      );
    }
  },

  updateJob: async (
    parent: unknown,
    { input: { job_id, jobPref, candidatePref, interviewPref} }: UpdateJobArgs<UpdateJobInput>,
    { prisma, req }: Context
  ): Promise<UpdateJobPayload> => {
    const currentUser = await protect(req);

    if (!job_id) {
      return responseHandler(
        "updateJobError",
        "Please provide required inputs",
        400
      );
    } else if (!currentUser) {
      return responseHandler("updateJobError", "Invalid Authentication", 401);
    } else {
      try {

        // if(isJobEdited && titleDetails) {
        //   const {jobTitle , jobId , jobRoleId, jobTitleId, updatedTitleId, jobTitleStatus} = titleDetails
        //   if( jobTitle && jobId && jobRoleId && !updatedTitleId){
        //     //created with dropdown value and updating with new entry
        //     const createdJob = await prisma.jobtitle.create({
        //       data:{
        //         name: jobTitle,
        //         name_en: jobTitle,
        //         isActive: false,
        //         jobRoleId: +jobRoleId
        //       },
        //       select: {
        //         id: true
        //       }
        //     });
        //     await prisma.recruiterJobPostDetail.update({
        //       where: {
        //         jobId: +jobId
        //       },
        //       data: {
        //         jobTitle: jobTitle,
        //         jobTitleId: +createdJob.id,
        //         jobCategoryId: +jobRoleId
        //       }
        //     })

        //   } else if (jobTitleId && jobId && jobTitle && jobRoleId && ( jobTitleId == updatedTitleId)) {
        //     // created will be new entry so updating the new entried data
        //     // keys used wiil be companyListId,companyId,companyName
        //     await prisma.jobtitle.update({
        //       where: {
        //         id: +jobTitleId
        //       },
        //       data: {
        //         name: jobTitle,
        //         name_en: jobTitle,
        //         jobRoleId: +jobRoleId
        //       }
        //     });
        //     await prisma.recruiterJobPostDetail.update({
        //       where: {
        //         jobId: +jobId
        //       },
        //       data: {
        //         jobTitle: jobTitle,
        //         jobCategoryId: +jobRoleId
        //       }
        //     });

        //   } else if (jobTitleId && jobId && jobTitle && updatedTitleId && jobRoleId && ( jobTitleId != updatedTitleId) && !jobTitleStatus) {
        //   //  created with new entry and updating with exisitng data so new entry will be deleted
        //   // keys ussed will be companyListId,companyId,companyName,updatedCompanyListID
        //   const jobTitleListDetails = await prisma.jobtitle.findMany({
        //       where: {
        //         id: {
        //           in: [+updatedTitleId, +jobTitleId]
        //         }
        //       },
        //       select: {
        //         id: true,
        //         name: true,
        //         name_en: true,
        //         isActive: true
        //       }
        //     });
        //     if (jobTitleListDetails.length >= 1) {
        //       const updatedJobdetails: any = jobTitleListDetails.find(i => i.id == updatedTitleId);
        //       const oldJobDetails: any = jobTitleListDetails.find(i => i.id == jobTitleId);

        //       if (oldJobDetails && !oldJobDetails.isActive) {
        //         await prisma.jobtitle.delete({
        //           where: {
        //             id: +jobTitleId
        //           }
        //         })
        //       }
        //       if (updatedJobdetails) {
        //         await prisma.recruiterJobPostDetail.update({
        //           where: {
        //             jobId: +jobId
        //           },
        //           data: {
        //             jobTitle: updatedJobdetails.name,
        //             jobTitleId: +updatedJobdetails.id,
        //             jobCategoryId: +jobRoleId
        //           }
        //         })
        //       }
        //     }

        //   }else if (jobTitleId && jobId && jobTitle && updatedTitleId && jobRoleId && ( jobTitleId != updatedTitleId) && jobTitleStatus) {
        //     //  created and updated with exisitng data so new entry will be deleted
        //     const jobTitleListDetails: any = await prisma.jobtitle.findUnique({
        //         where: {
        //           id: +updatedTitleId,
        //         },
        //         select: {
        //           id: true,
        //           name: true,
        //           name_en: true,
        //           isActive: true,
        //           jobRoleId: true
        //         }
        //       });
        //         if (jobTitleListDetails) {
        //           await prisma.recruiterJobPostDetail.update({
        //             where: {
        //               jobId: +jobId
        //             },
        //             data: {
        //               jobTitle: jobTitleListDetails.name,
        //               jobTitleId: +jobTitleListDetails.id,
        //               jobCategoryId: +jobRoleId
        //             }
        //           })
        //         }
        //   }

        // }

        // to check job exist or not
        const job = await prisma.job.findUnique({
          where: {
            id: +job_id
          },
          include: {
            JobDetail: true,
            candidatePreference: true,
            interviewPreference: {
              include: {
                faceInterview: {
                  select: {
                    id: true
                  }
                },
                WalkinInterview: {
                  select: {
                    id: true
                  }
                }
              }
            }
          }
        })

        //constructing the update seperately for each table
        let jobBaseData: UpdateJobDetailInput | any;
        let candidateData: CandidatePreferenceInput | any;
        let interviewPreferenceData: InterviewDetailsInput | any;

        //job detail update i.e considering job detail alone exist so checking job.JobDetail in above res
        if (job && job.JobDetail) {
          jobBaseData = {
            // Dynamically add fields based on whether they are provided in the input
            ...(jobPref.jobTitle && { jobTitle: jobPref.jobTitle }),
            // ...(jobPref.jobTitle && { jobTitle: jobPref.jobTitle }),
            // ...(jobPref.jobTitleId && { jobTitleId: jobPref.jobTitleId }),
            ...(jobPref.jobCategoryId && { jobCategoryId: +jobPref.jobCategoryId }),
            ...(jobPref.jobLocation && { jobLocation: jobPref.jobLocation }),
            ...(jobPref.numberOpenings && { numberOpenings: +jobPref.numberOpenings }),
            ...(jobPref.workTypeId && { workTypeId: +jobPref.workTypeId }),
            ...(jobPref.employmentTypeId && { employmentTypeId: +jobPref.employmentTypeId }),
            ...(jobPref.jobUrgencyId && { jobUrgencyId: +jobPref.jobUrgencyId }),
            ...(jobPref.jobDescription && { jobDescription: jobPref.jobDescription }),
            ...(jobPref.minMonthSalary && { minMonthSalary: jobPref.minMonthSalary }),
            ...(jobPref.maxMonthSalary && { maxMonthSalary: jobPref.maxMonthSalary }),
            ...(jobPref.isIncentives && { isIncentives: jobPref.isIncentives }),
            ...(jobPref.avgMonthlyIncentive && { avgMonthlyIncentive: jobPref.avgMonthlyIncentive }),
            ...(jobPref.shiftTypeId && { shiftTypeId: +jobPref.shiftTypeId }),
            ...(jobPref.workingDaysId && { workingDaysId: +jobPref.workingDaysId }),
            ...(jobPref.feesDeposit && { feesDeposit: jobPref.feesDeposit }),
            ...(jobPref.depositAmount && { depositAmount: jobPref.depositAmount }),
            ...(jobPref.depositReasonId && { depositReasonId: +jobPref.depositReasonId }),
            ...(jobPref.additionalInformation && { additionalInformation: jobPref.additionalInformation }),
            ...(jobPref.buildingName && { buildingName: jobPref.buildingName }),
            ...(jobPref.area && { area: jobPref.area }),
            ...(jobPref.city && { city: jobPref.city }),
            ...(jobPref.state && { state: jobPref.state }),
            ...(jobPref.zipcode && { zipcode: jobPref.zipcode }),
            // Construct jobBenefits based on whether it's provided in the input
            ...(jobPref.jobBenefits && {
              jobBenefits: {
                deleteMany: {}, // Empty deleteMany object to delete existing job benefits
                createMany: {
                  data: jobPref.jobBenefits.map((benefit) => ({
                    jobBenefitsId: +benefit,
                  })),
                },
              },
            }),
          };
        }

        // candidate preference update if exist/create if not exist
        if (job) {
          candidateData = {
            ...(candidatePref.gender && { genderId: +candidatePref.gender }),
            ...(candidatePref.education && { educationId: +candidatePref.education }),
            ...(candidatePref.workExperience && { workExperienceId: +candidatePref.workExperience }),
            ...(candidatePref.minExperience && { minExperience: candidatePref.minExperience }),
            ...(candidatePref.maxExperience && { maxExperience: candidatePref.maxExperience }),
            ...(candidatePref.applicationRadius && {
              applicationRadiusId: +candidatePref.applicationRadius,
            }),
            ...(candidatePref.englishKnowledge && {
              englishKnowledgeId: +candidatePref.englishKnowledge,
            }),
            ...(candidatePref.skills && {
              RecruiterSkillsMapping: {
                deleteMany: {}, // Empty deleteMany object to delete existing job benefits
                createMany: {
                  data: candidatePref.skills.map((skill) => ({
                    skillId: +skill,
                  })),
                },
              },
            }),
            ...(candidatePref.isAssetsRequired && { isAssetsRequired: candidatePref.isAssetsRequired }),
            ...(candidatePref.requiredAssets && {
              RequiredAssetsMapping: {
                deleteMany: {}, // Empty deleteMany object to delete existing job benefits
                createMany: {
                  data: candidatePref.requiredAssets.map((asset) => ({
                    requiredAssetsId: +asset,
                  })),
                },
              },
            }),
          }
        }

        // interview preference update if exist/create if not exist
        if (job) {
          let modelName: "fFinterview" | "walkininterview" | undefined;
          if (job.interviewPreference && job.interviewPreference.interviewTypeId == 3) {
            modelName = "fFinterview";
          } else if (job.interviewPreference && job.interviewPreference.interviewTypeId == 4) {
            modelName = "walkininterview";
          }
          if (modelName && job.interviewPreference) {
            await (prisma[modelName] as any).delete({
              where: {
                interviewId: job.interviewPreference.id
              }
            })
          }

          const mobileHash = createHmac(
            "sha256",
            process.env.MOBILEHASHSECRET as string
          )
            .update(interviewPref.hrContactNumber)
            .digest("hex");

          if (job) {
            interviewPreferenceData = {
              hrName: interviewPref.hrName,
              hrContactNumber: await encode(interviewPref.hrContactNumber),
              interviewTypeId: +interviewPref.interviewType,
              hrContactNumberHash: mobileHash,
            };

            if (+interviewPref.interviewType === 3 && interviewPref.faceInterview) {
              interviewPreferenceData.faceInterview = {
                create: {
                  buildingName: interviewPref.faceInterview.buildingName,
                  latitude: interviewPref.faceInterview.lat,
                  longitude: interviewPref.faceInterview.lng,
                  fullAddress: interviewPref.faceInterview.fullAddress,
                },
              };
            } else if (+interviewPref.interviewType === 4 && interviewPref.walkIn) {
              interviewPreferenceData.WalkinInterview = {
                create: {
                  startTime: interviewPref.walkIn.walkinStartTime,
                  duration: interviewPref.walkIn.duration,
                  endTime: interviewPref.walkIn.walkinEndTime,
                  location: interviewPref.walkIn.location,
                  buildingName: interviewPref.walkIn.buildingName,
                  walkinStartDate: new Date(interviewPref.walkIn.walkinStartDate).toISOString(),
                  latitude: interviewPref.walkIn.lat,
                  longitude: interviewPref.walkIn.lng,
                },
              };
            }
          }
        }

        console.log("======interview data", jobBaseData, candidateData, interviewPreferenceData);

        if(job && (job.jobStatusId === 2)) {
          await prisma.job.update({
            where: {
              id: +job_id,
            },
            data: {
              jobStatusId: job.jobStatusId,
              JobDetail: {
                update: jobBaseData // This will update fields in the JobDetail model
              },
              candidatePreference: {
                upsert: {
                  create: {
                    ...candidateData,
                    ...(candidatePref.skills && {
                      RecruiterSkillsMapping: {
                        createMany: {
                          data: candidatePref.skills.map((skill) => ({
                            skillId: +skill,
                          })),
                        },
                      },
                    }),
                    ...(candidatePref.requiredAssets && {
                      RequiredAssetsMapping: {
                        createMany: {
                          data: candidatePref.requiredAssets.map((asset) => ({
                            requiredAssetsId: +asset,
                          })),
                        },
                      },
                    }),
                  },
                  update: {
                    ...candidateData,
                    ...(candidatePref.skills && {
                      RecruiterSkillsMapping: {
                        deleteMany: {},
                        createMany: {
                          data: candidatePref.skills.map((skill) => ({
                            skillId: +skill,
                          })),
                        },
                      },
                    }),
                    ...(candidatePref.requiredAssets && {
                      RequiredAssetsMapping: {
                        deleteMany: {},
                        createMany: {
                          data: candidatePref.requiredAssets.map((asset) => ({
                            requiredAssetsId: +asset,
                          })),
                        },
                      },
                    }),
                  },
                  where: { jobId: +job_id }, // Specify the condition to find the existing entry
                },
              },
              interviewPreference: {
                upsert: {
                  create: interviewPreferenceData, // Create the entry if it doesn't exist
                  update: interviewPreferenceData,
                  where: { jobId: +job_id }, // Specify the condition to find the existing entry
                },
              },
            },
          });
  
          return responseHandler(
            "updateJobError",
            "job updated Successfully",
            200
          );
        } else {
          return responseHandler(
            "updateJobError",
            "Only draft and active jobs can edit",
            400
          );
        }
      } catch (error) {

        console.log('error', error)
        return responseHandler(
          "updateJobError",
          "Failed to update job",
          400
        );
      }
    }

  },

  //company name updated (name key value update in companylist)
  companyNameUpdate: async (
    parent: unknown,
    { input: { companyListId, companyName, companyId, updatedCompanyListID, removedCompanyListId } }: companyNameArgs<companyNameInput>,
    { prisma, req }: Context
  ): Promise<companyNamePayload> => {
    if (!companyId || !companyName) {
      return responseHandler(
        "companyNameError",
        "You must provide a required field",
        400
      );
    } else {
      const currentUser = await protect(req);

      if (currentUser) {
        try {
          if(removedCompanyListId && companyName && companyId){
            //created with dropdown value and updating with new entry
            const createdCompany = await prisma.companyList.create({
              data:{
                name: companyName,
                name_en: companyName,
                isActive: false
              },
              select: {
                id: true
              }
            });
            await prisma.company.update({
              where: {
                id: +companyId
              },
              data: {
                name: companyName,
                companyListId: +createdCompany.id,
              }
            })

          } else if (companyListId && companyId && companyName && !updatedCompanyListID) {
            // created will be new entry so updating the new entried data
            // keys used wiil be companyListId,companyId,companyName
            await prisma.companyList.update({
              where: {
                id: +companyListId
              },
              data: {
                name: companyName,
                name_en: companyName
              }
            });
            await prisma.company.update({
              where: {
                id: +companyId
              },
              data: {
                name: companyName
              }
            });

          } else if (companyListId && companyId && companyName && updatedCompanyListID) {
          //  created with new entry and updating with exisitng data so new entry will be deleted
          // keys ussed will be companyListId,companyId,companyName,updatedCompanyListID
          const companyListDetails = await prisma.companyList.findMany({
              where: {
                id: {
                  in: [+updatedCompanyListID, +companyListId]
                }
              },
              select: {
                id: true,
                name: true,
                name_en: true,
                isActive: true
              }
            });
            if (companyListDetails.length >= 1) {
              const updatedCompanydetails: any = companyListDetails.find(i => i.id == updatedCompanyListID);
              const oldCompanyDetails: any = companyListDetails.find(i => i.id == companyListId);

              if (oldCompanyDetails && !oldCompanyDetails.isActive) {
                await prisma.companyList.delete({
                  where: {
                    id: +companyListId
                  }
                })
              }
              if (updatedCompanydetails) {
                await prisma.company.update({
                  where: {
                    id: +companyId
                  },
                  data: {
                    name: updatedCompanydetails.name,
                    companyListId: +updatedCompanydetails.id
                  }
                })
              }
            }

          }
          return responseHandler(
            "companyNameError",
            "company name updated successfully",
            200
          );
        } catch (error) {
          console.log("error", error)
          return responseHandler(
            "companyNameError",
            "failed to update company name ",
            400
          );
        }
      } else {
        return responseHandler(
          "companyNameError",
          "User not autorized",
          401
        );
      }
    }
  },

  jobTitleUpdate: async (
    parent: unknown,
    { input: { jobTitleId, jobRoleId, jobTitle, jobId, updatedJobTitleId, removedJobTitleId } }: jobTitleArgs<jobTitleInput>,
    { prisma, req }: Context
  ): Promise<jobTitlePayload> => {
    if (!jobId || !jobTitle) {
      return responseHandler(
        "jobTitleErrors",
        "You must provide a required field",
        400
      );
    } else {
      const currentUser = await protect(req);

      if (currentUser) {
        try {
          if(removedJobTitleId && jobTitle && jobId && jobRoleId){
            //created with dropdown value and updating with new entry
            const createdJob = await prisma.jobtitle.create({
              data:{
                name: jobTitle,
                name_en: jobTitle,
                isActive: false,
                jobRoleId: +jobRoleId
              },
              select: {
                id: true
              }
            });
            await prisma.recruiterJobPostDetail.update({
              where: {
                jobId: +jobId
              },
              data: {
                jobTitle: jobTitle,
                jobTitleId: +createdJob.id,
                jobCategoryId: +jobRoleId
              }
            })

          } else if (jobTitleId && jobId && jobTitle && !updatedJobTitleId && jobRoleId) {
            // created will be new entry so updating the new entried data
            // keys used wiil be companyListId,companyId,companyName
            await prisma.jobtitle.update({
              where: {
                id: +jobTitleId
              },
              data: {
                name: jobTitle,
                name_en: jobTitle,
                jobRoleId: +jobRoleId
              }
            });
            await prisma.recruiterJobPostDetail.update({
              where: {
                jobId: +jobId
              },
              data: {
                jobTitle: jobTitle
              }
            });

          } else if (jobTitleId && jobId && jobTitle && updatedJobTitleId && jobRoleId) {
          //  created with new entry and updating with exisitng data so new entry will be deleted
          // keys ussed will be companyListId,companyId,companyName,updatedCompanyListID
          const jobTitleListDetails = await prisma.jobtitle.findMany({
              where: {
                id: {
                  in: [+updatedJobTitleId, +jobTitleId]
                }
              },
              select: {
                id: true,
                name: true,
                name_en: true,
                isActive: true
              }
            });
            if (jobTitleListDetails.length >= 1) {
              const updatedJobdetails: any = jobTitleListDetails.find(i => i.id == updatedJobTitleId);
              const oldJobDetails: any = jobTitleListDetails.find(i => i.id == jobTitleId);

              if (oldJobDetails && !oldJobDetails.isActive) {
                await prisma.jobtitle.delete({
                  where: {
                    id: +jobTitleId
                  }
                })
              }
              if (updatedJobdetails) {
                await prisma.recruiterJobPostDetail.update({
                  where: {
                    jobId: +jobId
                  },
                  data: {
                    jobTitle: updatedJobdetails.name,
                    jobTitleId: +updatedJobdetails.id,
                    jobCategoryId: +jobRoleId
                  }
                })
              }
            }

          }
          return responseHandler(
            "jobTitleErrors",
            "Job title updated successfully",
            200
          );
        } catch (error) {
          console.log("error", error)
          return responseHandler(
            "jobTitleErrors",
            "failed to update job title ",
            400
          );
        }
      } else {
        return responseHandler(
          "jobTitleErrors",
          "User not autorized",
          401
        );
      }
    }
  },

  blockRecruiter: async (
    parent: unknown,
    { input: { recruiterId, recruiterStatus } }: BlockRecruiterArgs<BlockRecruiterInput>,
    { prisma, req }: Context
  ): Promise<BlockRecruiterPayload> => {
    try {
      if (!recruiterId && !recruiterStatus) {
        return responseHandler(
          "blockRecruiterErrors",
          "You must provide a required field",
          400
        );
      }
  
      const currentUser = await protect(req);
      if (!currentUser) {
        return responseHandler(
          "blockRecruiterErrors",
          "Recruiter not authorized",
          401
        );
      }
  
      let recruiterStatusId: number;
      let jobStatusId: number;
      switch (recruiterStatus) {
        case "BLOCK":
          recruiterStatusId = RecruiterStatus.BLOCKED;
          jobStatusId= JobStatus.EXPIRED
          break;
        case "UNBLOCK":
          recruiterStatusId = RecruiterStatus.COMPANYCREATED;
          jobStatusId= JobStatus.ACTIVE
          break;
        default:
          return responseHandler(
            "blockRecruiterErrors",
            "Invalid recruiter status",
            400
          );
      }
        const getRecruiter = await prisma.recruiter.findUnique({
        where: { id: +recruiterId },
      });
  
      if (!getRecruiter) {
        return responseHandler(
          "blockRecruiterErrors",
          "Recruiter not found",
          404
        );
      }
      const [updateRecruiter, updateJob] =await prisma.$transaction([
        prisma.recruiter.update({
        where: { id: +recruiterId },
        data: { recruiterStatusId },
      }),
        prisma.job.updateMany({
            where: {
                recruiterId: +recruiterId  
            },
            data: {
                jobStatusId:  jobStatusId
            }
        })
      ]);
      return responseHandler(
        "blockRecruiterErrors",
        `Recruiter ${recruiterStatus === "BLOCK" ? "blocked" : "unblocked"} successfully`,
        200
      );
    } catch (error: any) {
      console.log("===error==", error.message);
      return responseHandler(
        "blockRecruiterErrors",
        "unknown issue while blocking recruiter",
        400
      );
    }
  },

  reActiveJob: async (
    parent: unknown,
    { input: { jobId } }: ReActiveJobArgs<ReActiveJobInput>,
    { prisma, req }: Context
  ): Promise<ReActiveJobPayload> => {
    try {
      const currentUser = await protect(req);

      if(currentUser) {
        const selectedJob = await prisma.job.findFirst({
          where:{
            id: +jobId
          },
          select:{
            id: true,
            jobStatusId: true
          }
        })
        if(selectedJob && selectedJob.jobStatusId === JobStatus.EXPIRED ) {
          await prisma.job.update({
            where: {
              id: +jobId
            },
            data:{
              jobStatusId: JobStatus.ACTIVE,
              JobDetail:{
                update: {
                  updatedAt: new Date() // You can use Date.now() or any other timestamp method
                } 
              }
            }
          })
          return responseHandler(
            "ReActiveJobErrors",
            "Job activated successfully",
            200
          )
        } else {
          return responseHandler(
            "ReActiveJobErrors",
            "Only expired jobs can be reactivated",
            400
          )
        }
      } else {
        return responseHandler(
          "ReActiveJobErrors",
          "Invalid Authentication", 
          401
        )
      }
      
    } catch (error: any) {
      return responseHandler(
        "ReActiveJobErrors",
        "unknown issue while Reactivating the job",
        400
      );
    }
  }

};
