-- CreateTable
CREATE TABLE `invoicedoctype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `value` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterinvoice` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `sellerGstin` VARCHAR(255) NOT NULL DEFAULT '',
    `sellerCin` VARCHAR(255) NOT NULL DEFAULT '',
    `buyerGstin` VARCHAR(255) NULL,
    `invoiceDocTypeId` INTEGER NOT NULL,
    `currentPlanTransactionId` INTEGER NOT NULL,
    `invoiceDocDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `udId` VARCHAR(255) NULL,
    `loadId` VARCHAR(255) NULL,
    `irnNo` VARCHAR(255) NULL,
    `ackNo` VARCHAR(255) NULL,
    `ackDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `signedInvoice` TEXT NULL,
    `signedQr` TEXT NULL,
    `qrBase64encoded` TEXT NULL,
    `invoiceMeta` JSON NULL,
    `generatedInvoiceUrl` VARCHAR(255) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiterinvoice` ADD CONSTRAINT `recruiterinvoice_invoiceDocTypeId_fkey` FOREIGN KEY (`invoiceDocTypeId`) REFERENCES `invoicedoctype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterinvoice` ADD CONSTRAINT `recruiterinvoice_currentPlanTransactionId_fkey` FOREIGN KEY (`currentPlanTransactionId`) REFERENCES `recruiterplanpurchasetransaction`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
