import { PrismaClient } from "@prisma/client";
import { readReplicas } from "@prisma/extension-read-replicas";


// const globalForPrisma = globalThis as unknown as {
//   prisma: PrismaClient | undefined;
// };

// export const prisma =
//   globalForPrisma.prisma ??
//   new PrismaClient({
//     datasources: { db: { url: process.env.DATABASE_URL } },
//     log: ["query", "error", "info", "warn"],
//   });

// if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// !------------- ReadReplicas Code ----------!
export const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error']
}).$extends(
  readReplicas({
    url: process.env.DATABASE_URL_REPLICA as string
  })
);
