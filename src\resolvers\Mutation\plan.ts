import { Context } from "../../context";
import { protect } from "../../middleware/auth";
import {ApproveJobCreditRequestArgs, ApproveJobCreditRequestInput, ApproveJobCreditRequestPayload, CreatePlanArgs, CreatePlanInput, CreatePlanPayload, UpdatePlanArgs, UpdatePlanInput, UpdatePlanPayload } from "../../types/plan.type";
import { CreditApprovalStatus, CreditDeductionCriteria, CreditSpendType, CreditTransactionType } from "../../utils/constants";
import { decode } from "../../utils/crypto";
import { responseHandler } from "../../utils/responseHandler";

export const planResolvers = {
  createPlan: async (
    parent: unknown,
    {
      input: {
        planName,
        planTitle,
        planDescription,
        slapPrice,
        discount,
        jobCredits,
        databaseCredits,
        jobValidity,
        planValidity,
        gst,
        isActive,
        refundWindow,
        planTypeId
      },
    }: CreatePlanArgs<CreatePlanInput>,
    { prisma, req }: Context
  ): Promise<CreatePlanPayload> => {
    await protect(req);
    try {
      if(!planName || !planTitle || !planDescription) {
        return responseHandler('createPlanErrors', "Required Field must be given", 400);
      } else {
        const createPlan = await prisma.plan.create({
          data: {
            planName,
            planTitle,
            planDescription,
            slapPrice,
            discount,
            jobCredits,
            databaseCredits,
            jobValidity,
            planValidity,
            gst,
            isActive,
            refundWindow,
            planTypeId: +planTypeId,
          }
        });

        if(createPlan && createPlan.id) {
          return responseHandler('createPlanErrors', `Plan ${createPlan.planName} created successfully`, 201);
        } else {
          return responseHandler('createPlanErrors', "Failed to creat the plan", 400);
        }
      }
    } catch (error) {
      console.log("=====create plan error", error);
      return responseHandler('createPlanErrors', "Unknown error while create the plan", 500);
    }
  },

  updatePlan: async (
    parent: unknown,
    {
      input: {
        planId,
        planName,
        planTitle,
        planDescription,
        slapPrice,
        discount,
        jobCredits,
        databaseCredits,
        jobValidity,
        planValidity,
        gst,
        isActive
      },
    }: UpdatePlanArgs<UpdatePlanInput>,
    { prisma, req }: Context
  ): Promise<UpdatePlanPayload> => {
    await protect(req);
    try {
      if((planName === null || "") || (planTitle === null || "") || (planDescription === null || "")) {
        return responseHandler('updatePlanErrors', "Required Field must be given", 400);
      } else {
        const updatePlan = await prisma.plan.update({
          where: {
            id: +planId
          },
          data: {
            planName,
            planTitle,
            planDescription,
            slapPrice,
            discount,
            jobCredits,
            databaseCredits,
            jobValidity,
            planValidity,
            gst,
            isActive
          }
        });

        if(updatePlan && updatePlan.id) {
          return responseHandler('updatePlanErrors', `Plan ${updatePlan.planName} updated successfully`, 201);
        } else {
          return responseHandler('updatePlanErrors', "Failed to update the plan", 400);
        }
      }
    } catch (error) {
      console.log("=====update plan error", error);
      return responseHandler('updatePlanErrors', "Unknown error while update the plan", 500);
    }
  },

  approveJobCreditRequest: async (
    parent: unknown,
    { input: { requestId, jobCredit, approveStatus, rejectReason } }: ApproveJobCreditRequestArgs<ApproveJobCreditRequestInput>,
    { prisma, req }: Context
  ): Promise<ApproveJobCreditRequestPayload> => {
    try {
      const currentUser = await protect(req);
      if (!currentUser) {
        return responseHandler("updateStatusErrors", "Invalid User", 401);
      }

      if (approveStatus === CreditApprovalStatus.REJECTED && !rejectReason) {
        return responseHandler(
          "updateStatusErrors",
          "Reject reason is required when rejecting a credit request",
          400
        );
      }
      const creditRequest = await prisma.recruitercreditrequest.findUnique({
        where: { id: +requestId },
        include: { currentPlanTransaction: true },
      });

      if (!creditRequest || !creditRequest.currentPlanTransaction) {
        return responseHandler(
          "updateStatusErrors",
          "Invalid credit request or plan purchase transaction not found",
          400
        );
      }

      const { recruiterId, currentPlanTransaction } = creditRequest;
      const { id: planPurchaseTransactionId, planId } = currentPlanTransaction;

      switch (approveStatus) {
        case CreditApprovalStatus.REJECTED:
          await prisma.recruitercreditrequest.update({
            where: { id: +requestId },
            data: {
              creditRequestApproveStatusId: CreditApprovalStatus.REJECTED,
              crmUserId: +currentUser.id,
              creditRejectReason: rejectReason
            },
          });
          return responseHandler(
            "updateStatusErrors",
            "Credit request rejected successfully",
            200
          );

        case CreditApprovalStatus.ACCEPTED:
          return await prisma.$transaction(async (tx) => {
            // Fetch current active plan info
            const currentPlanInfo = await tx.recruiterlatestplaninfo.findUnique({
              where: { recruiterId: +recruiterId, isPlanActive: true },
            });
            if (!currentPlanInfo) {
              throw new Error("You don't have an active plan.");
            }

            // Fetch Credit Deduction Criteria
            const creditDeductionCriteria = await tx.creditdeductioncriteria.findUnique({
              where: { id: +CreditDeductionCriteria.JOBPOST }
            });

            if (!creditDeductionCriteria) {
              throw new Error("Credit deduction criteria not found.");
            }

            if (currentPlanInfo.currentJobCredits >= creditDeductionCriteria.value) {
              throw new Error("Credit deduction criteria not met. Job credits are sufficient.");
            }

            //recruiter credit transaction
            await tx.recruitercredittransaction.create({
              data: {
                creditTransactionTypeId: CreditTransactionType.REQUESTCREDIT,
                creditSpendTypeId: CreditSpendType.JOBCREDIT,
                jobCredit: Math.abs(jobCredit),
                databaseCredit: 0,
                recruiterId,
                creditTransactionId: `MJREITXCT${recruiterId}_${Date.now()}`,
                planPurchaseTransactionId,
                planId,
              }
            });

            // add total job & database credits 
            const { _sum: { jobCredit: totalJobCredits, databaseCredit: totalDBCredits } } =
              await tx.recruitercredittransaction.aggregate({
                where: { recruiterId },
                _sum: { jobCredit: true, databaseCredit: true },
              });

            await tx.recruiterlatestplaninfo.update({
              where: { recruiterId },
              data: {
                currentJobCredits: Math.abs(totalJobCredits ?? 0),
                currentDBCredtis: Math.abs(totalDBCredits ?? 0)
              }
            });

            await tx.recruitercreditrequest.update({
              where: { id: +requestId },
              data: {
                creditRequestApproveStatusId: CreditApprovalStatus.ACCEPTED,
                crmUserId: +currentUser.id,
                jobCredit: Math.abs(jobCredit)
              }
            });
            return responseHandler("updateStatusErrors", "Credit request approved successfully", 201);
          });
        default:
          return responseHandler(
            "updateStatusErrors",
            "Invalid approve status",
            400
          );
      }
    } catch (error: any) {
      console.log("Error approving credit request:", error);
      if (error.code) {
        switch (error.code) {
          case "P2025":
            return responseHandler("updateStatusErrors", "Request rejected due to: Record not found. Unable to update.", 404);
          case "P2002":
            return responseHandler("updateStatusErrors", "Request rejected due to: Duplicate entry found.", 409);
          case "P2003":
            return responseHandler("updateStatusErrors", "Request rejected due to: Foreign key constraint failed.", 400);
          case "P2005":
            return responseHandler("updateStatusErrors", "Request rejected due to: Invalid field value.", 400);
          default:
            return responseHandler("updateStatusErrors", `Database error: ${error.message}`, 500);
        }
      }
      return responseHandler("updateStatusErrors", `Request rejected due to: ${error.message}`, 500);
    }
  }
};
