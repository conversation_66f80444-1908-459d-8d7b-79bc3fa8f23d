import { Prisma } from "@prisma/client";

// Create Plan Types
export interface CreatePlanInput {
  planName: string;
  planTitle: string;
  planDescription: string;
  slapPrice: number;
  discount: number;
  jobCredits: number;
  databaseCredits: number;
  jobValidity: number;
  planValidity: number;
  gst: number;
  isActive: boolean;
  refundWindow: number;
  planTypeId: string;
}

export interface CreatePlanArgs<T> {
  input: T;
}

export interface CreatePlanPayload {
  createPlanErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}

// Update Plan Types
export interface UpdatePlanInput {
  planId: number;
  planName?: string;
  planTitle?: string;
  planDescription?: string;
  slapPrice?: number;
  discount?: number;
  jobCredits?: number;
  databaseCredits?: number;
  jobValidity?: number;
  planValidity?: number;
  gst?: number;
  isActive?: boolean;
  refundWindow?: number;
  planTypeId: string;
}

export interface UpdatePlanArgs<T> {
  input: T;
}

export interface UpdatePlanPayload {
  updatePlanErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}

// Get All Plans
export interface plan {
  id: number;
  planName: string;
  planTitle: string;
  planDescription?: string;
  slapPrice: number;
  discount: Prisma.Decimal;
  jobCredits: number;
  databaseCredits: number;
  jobValidity: number;
  planValidity: number;
  gst: Prisma.Decimal;
  isActive: boolean;
  refundWindow?: number;
  planTypeId: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PlansPayload {
  plansErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
  plans: plan[]
}

// Recruiter Credit Request List
export interface CreditRequestApprove {
  id: number;
  name: string;
  isActive: boolean;
}

export interface CreditRequestType {
  id: number;
  name: string;
  isActive: boolean;
}

export interface PaymentStatus {
  id: number;
  name: string;
  isActive: boolean;
}

export interface RecruiterCreditTransaction {
  id: number;
  creditTransactionTypeId: number;
  creditTransactionType: CreditTransactionType;
  creditSpendTypeId: number;
  creditSpendType: CreditSpendType;
  jobCredit: number;
  databaseCredit: number;
  recruiterId: number;
  recruiter?: Recruiter;
  creditTransactionId: string;
  planId: number;
  plan: plan;
  planPurchaseTransactionId: number;
  planPurchaseTransaction?: RecruiterPlanPurchase;
  jobId?: number | null;
  //job?: Job | null;
  createdAt: Date;
  updatedAt: Date;
}


export interface CreditTransactionType {
  id: number;
  name: string;
}

export interface CreditSpendType {
  id: number;
  name: string;
}

export interface RecruiterPlanPurchase {
  id: number;
  recruiterId: number;
  recruiter?: Recruiter
  internalTransactionId: string;
  planId: number;
  plan: plan
  totalAmountPaid: number;
  actualAmountPaid: number;
  discount: Prisma.Decimal;
  //gst: number;
  paymentTransactionId: string |null;
  paymentStatus: PaymentStatus
  paymentStatusId: number;
  transactionFailedReason: string | null;
  recruitercredittransaction?: RecruiterCreditTransaction[] | null;
  location: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Recruiter {
  id: number;
  mobileNumber: string;
  name: string | null;
  email: string | null;
  company: {
    id: number;
    name: string;
    companyTypeId: number;
    isCompanyVerified: boolean;
    createdAt: string;
    updatedAt: string;
  }
}

export interface CRMUser {
  id: number;
  email: string;
  mobileNumber: string | null;
}

export interface CreditRequestsPayload {
  getCreditRequestsErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
  creditRequests: CreditRequest[]
}

export interface ApproveJobCreditRequestInput {
  requestId:number;
  jobCredit:number;
  approveStatus:number;
  rejectReason:string;
}

export interface ApproveJobCreditRequestArgs<T> {
  input: T;
}

export interface ApproveJobCreditRequestPayload {
  updateStatusErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
}

export interface RecruiterLatestPlanInfo {
  id: number;
  plan: plan;
  currentPlanTransaction: RecruiterPlanPurchase;
  currentJobCredits: number;
  currentDBCredtis: number;
  previousCarryJobCredits: number;
  previousCarryDBCredits: number;
  currentJobPostValidity: Date;
  currentPlanValidity: Date;
  isPlanActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreditRequest {
  id: number;
  recruiterId: number;
  crmUserId: number | null;
  crmUser: CRMUser | null;
  creditRequestApproveStatusId: number;
  creditRequestApproveStatus: CreditRequestApprove;
  creditRequestTypeId: number;
  creditRequestType: CreditRequestType;
  currentPlanTransactionId: number;
  currentPlanTransaction: RecruiterPlanPurchase;
  creditRejectReason: string | null;
  jobCredit: number;
  databaseCredit: number;
  lastCreditRequestDate: Date  | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreditHistory {
  id: number;
  name: string;
  mobileNumber: string;
  email: string | null;
  company: {
    id: number;
    name: string;
    industryId: number | null;
    numberOfEmployeesId: number | null;
    location: string;
    buildingName: string;
    companyTypeId: number | null;
    companyListId: number | null;
    isCompanyVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  recruiterlatestplaninfo: {
    currentJobCredits: number;
  } | null;
  creditHistory: {
    id: number;
    creditRequestApproveStatusId: number;
    creditRequestApproveStatus: CreditRequestApprove;
    creditRejectReason: string | null;
    jobCredit: number;
    databaseCredit: number;
    createdAt: Date;
    updatedAt: Date;
  }[];
}


export interface CreditHistoryPayload {
  creditHistoryErrors: {
    message: string;
  }[];
  message: string | null;
  status: number;
  recruiterCreditHistory: CreditHistory | null;
}
