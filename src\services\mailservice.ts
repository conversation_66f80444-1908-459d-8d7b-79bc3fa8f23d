import nodemailer from "nodemailer";

export interface MailInterface {
  from?: string;
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  dsn?: {
    id: string;
    return: string;
    notify: string[],
    recipient: string
  }
}

export default class MailService {
  private static instance: MailService;
  private transporter!: nodemailer.Transporter;

  // private constructor() {}
  //INTSTANCE CREATE FOR MAIL
  static getInstance() {
    if (!MailService.instance) {
      MailService.instance = new MailService();
    }
    return MailService.instance;
  }
  //CREATE CONNECTION FOR LIVE
  async createConneection() {
    this.transporter = nodemailer.createTransport({
      service: process.env.SMTP_SERVICE,
      host: process.env.SMTP_HOST,
      secure: false,
      port: process.env.SMTP_PORT as number | undefined,
      // secure: Boolean(process.env.SMTP_TLS),
      tls: {
        rejectUnauthorized: false
      },
      // auth: {
      //   user: "<EMAIL>",
      //   pass: "X70Pdxnw",
      // },
    });
  }
  //SEND MAIL
  async sendMail(
    requestId: string | number | string[],
    options: MailInterface
  ) {
    return await this.transporter
      .sendMail({
        from: options.from,
        to: options.to,
        cc: options.cc,
        bcc: options.bcc,
        subject: options.subject,
        text: options.text,
        html: options.html,
      })
      .then((info: any) => {
        return info;
      }).catch((err) => {
        console.log("=====issue in send mail===", err)
      });
  }
  //VERIFY CONNECTION
  async verifyConnection() {
    return this.transporter.verify((function(error, success) {
      if(error) {
        console.log("Mail Server Not Connected", error)
      } else {
        console.log("Mail Server is ready to take our messages", success);
      }
    }));
  }
  //CREATE TRANSPOTER
  getTransporter() {
    return this.transporter;
  }
}
