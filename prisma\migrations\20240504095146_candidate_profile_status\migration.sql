-- AlterTable
ALTER TABLE `recruiteruserdetails` ADD COLUMN `candidateProfileStatusId` INTEGER NULL;

-- CreateTable
CREATE TABLE `candidateprofilestatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_candidateProfileStatusId_fkey` FOREIGN KEY (`candidateProfileStatusId`) REFERENCES `candidateprofilestatus`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
