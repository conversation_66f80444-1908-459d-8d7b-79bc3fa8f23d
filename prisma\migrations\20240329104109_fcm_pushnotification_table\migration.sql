/*
  Warnings:

  - You are about to drop the column `pauseUntil` on the `user` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `user` DROP COLUMN `pauseUntil`;

-- CreateTable
CREATE TABLE `fcmdevicetoken` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `device` VARCHAR(100) NOT NULL DEFAULT '',
    `token` VARCHAR(255) NOT NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `userId` INTEGER NOT NULL,

    UNIQUE INDEX `fcmdevicetoken_device_token_userId_key`(`device`, `token`, `userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `fcmdevicetoken` ADD CONSTRAINT `fcmdevicetoken_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
