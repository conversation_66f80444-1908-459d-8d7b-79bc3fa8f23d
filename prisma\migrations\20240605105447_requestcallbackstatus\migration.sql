-- CreateTable
CREATE TABLE `recruiterrequest` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `crmUserId` INTEGER NULL,
    `requestCallBackStatusId` INTEGER NULL DEFAULT 1,
    `recruiterRequestFeedbackId` INTEGER NULL,
    `requestCallBackOtherReason` VARCHAR(255) NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `requestcallbackstatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BO<PERSON>EAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterrequestfeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(200) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(200) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_requestCallBackStatusId_fkey` FOREIGN KEY (`requestCallBackStatusId`) REFERENCES `requestcallbackstatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_recruiterRequestFeedbackId_fkey` FOREIGN KEY (`recruiterRequestFeedbackId`) REFERENCES `recruiterrequestfeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_crmUserId_fkey` FOREIGN KEY (`crmUserId`) REFERENCES `crmuser`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
