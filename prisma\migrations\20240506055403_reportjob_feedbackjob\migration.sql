/*
  Warnings:

  - The primary key for the `userreportjob` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[userId,jobId,jobFeedbackId]` on the table `userjobmapping` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,jobId]` on the table `userreportjob` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,jobId,reportJobId]` on the table `userreportjob` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `UserReportJob` RENAME TO `userreportjob`;

-- DropForeignKey
ALTER TABLE `userreportjob` DROP FOREIGN KEY `UserReportJob_jobId_fkey`;

-- DropForeignKey
ALTER TABLE `userreportjob` DROP FOREIGN KEY `UserReportJob_reportJobId_fkey`;

-- DropForeignKey
ALTER TABLE `userreportjob` DROP FOREIGN KEY `UserReportJob_userId_fkey`;

-- DropIndex userid (remove unique index)
ALTER TABLE `userreportjob` DROP INDEX `UserReportJob_userId_key`;

-- DropIndex jobid (remove unique index)
ALTER TABLE `userreportjob` DROP INDEX `UserReportJob_jobId_key`;


-- AlterTable
ALTER TABLE `userjobmapping` ADD COLUMN `isJobFeedBack` BOOLEAN NULL DEFAULT false,
    ADD COLUMN `jobFeedbackDate` DATETIME(3) NULL,
    ADD COLUMN `jobFeedbackId` INTEGER NULL,
    ADD COLUMN `otherFeedbackReason` VARCHAR(255) NULL;

-- AlterTable
ALTER TABLE `userreportjob` DROP PRIMARY KEY,
    ADD COLUMN `isJobReported` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `otherReportReason` VARCHAR(255) NULL,
    ADD PRIMARY KEY (`id`, `jobId`);

-- CreateTable
CREATE TABLE `jobfeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(70) NOT NULL DEFAULT '',
    `name_en` VARCHAR(70) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(70) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `userjobmapping_userId_jobId_jobFeedbackId_key` ON `userjobmapping`(`userId`, `jobId`, `jobFeedbackId`);

-- CreateIndex
CREATE UNIQUE INDEX `userreportjob_userId_jobId_key` ON `userreportjob`(`userId`, `jobId`);

-- CreateIndex
CREATE UNIQUE INDEX `userreportjob_userId_jobId_reportJobId_key` ON `userreportjob`(`userId`, `jobId`, `reportJobId`);

-- AddForeignKey
ALTER TABLE `userreportjob` ADD CONSTRAINT `userreportjob_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userreportjob` ADD CONSTRAINT `userreportjob_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userreportjob` ADD CONSTRAINT `userreportjob_reportJobId_fkey` FOREIGN KEY (`reportJobId`) REFERENCES `ReportJob`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobmapping` ADD CONSTRAINT `userjobmapping_jobFeedbackId_fkey` FOREIGN KEY (`jobFeedbackId`) REFERENCES `jobfeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

