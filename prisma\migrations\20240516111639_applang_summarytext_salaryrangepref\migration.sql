-- AlterTable
ALTER TABLE `AppLanguage` ADD COLUMN `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    ADD COLUMN `name_ta` VARCHAR(50) NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE `recruiterjobpostdetail` ADD COLUMN `area` VARCHAR(100) NULL DEFAULT '',
    ADD COLUMN `city` VARCHAR(100) NULL DEFAULT '',
    ADD COLUMN `state` VARCHAR(55) NULL DEFAULT '',
    ADD COLUMN `zipcode` VARCHAR(20) NULL DEFAULT '';

-- AlterTable
ALTER TABLE `UserJobPreferences` ADD COLUMN `salaryRangeId` INTEGER NULL;

-- AlterTable
ALTER TABLE `UserProfessional` MODIFY `professionalSummary` VARCHAR(800) NULL DEFAULT '';

-- Add<PERSON><PERSON>ign<PERSON>ey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_salaryRangeId_fkey` FOREIGN KEY (`salaryRangeId`) REFERENCES `monthlysalaryrange`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
