/*
  Warnings:

  - You are about to drop the column `addressDetails` on the `company` table. All the data in the column will be lost.
  - You are about to drop the column `hiringFor` on the `recruiter` table. All the data in the column will be lost.
  - You are about to drop the column `recruiterStatus` on the `recruiter` table. All the data in the column will be lost.
  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.
  - Made the column `name` on table `recruiter` required. This step will fail if there are existing NULL values in that column.
  - Made the column `name` on table `recruiterrole` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `company` DROP COLUMN `addressDetails`,
    ADD COLUMN `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    ADD COLUMN `companyListId` INTEGER NULL,
    ADD COLUMN `companyTypeId` INTEGER NULL,
    ADD COLUMN `isCompanyVerified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isDeleted` BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE `recruiter` DROP COLUMN `hiringFor`,
    DROP COLUMN `recruiterStatus`,
    ADD COLUMN `designationId` INTEGER NULL DEFAULT 1,
    ADD COLUMN `emailHash` VARCHAR(65) NOT NULL DEFAULT '',
    ADD COLUMN `hiringForId` INTEGER NULL DEFAULT 1,
    ADD COLUMN `isCompanyVerified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isEmailVerified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isPersonalVerified` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `isRecruiterHoldEmail` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `mobileNumberHash` VARCHAR(65) NOT NULL DEFAULT '',
    ADD COLUMN `recruiterStatusId` INTEGER NULL,
    ADD COLUMN `whatsAppUpdate` BOOLEAN NOT NULL DEFAULT false,
    MODIFY `name` VARCHAR(255) NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE `recruiterrole` MODIFY `name` VARCHAR(20) NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- CreateTable
CREATE TABLE `RecruiterOTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterEmailOTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Designation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `HiringFor` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompanyList` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `consultantcompanymapping` (
    `consultantId` INTEGER NOT NULL,
    `clientCompanyId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`consultantId`, `clientCompanyId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `clientcompany` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `industryId` INTEGER NULL,
    `numberOfEmployeesId` INTEGER NULL,
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    `isCompanyVerified` BOOLEAN NOT NULL DEFAULT false,
    `isDeleted` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_recruiterStatusId_fkey` FOREIGN KEY (`recruiterStatusId`) REFERENCES `RecruiterStatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_hiringForId_fkey` FOREIGN KEY (`hiringForId`) REFERENCES `HiringFor`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_designationId_fkey` FOREIGN KEY (`designationId`) REFERENCES `Designation`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_companyTypeId_fkey` FOREIGN KEY (`companyTypeId`) REFERENCES `HiringFor`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_companyListId_fkey` FOREIGN KEY (`companyListId`) REFERENCES `CompanyList`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `consultantcompanymapping` ADD CONSTRAINT `consultantcompanymapping_consultantId_fkey` FOREIGN KEY (`consultantId`) REFERENCES `company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `consultantcompanymapping` ADD CONSTRAINT `consultantcompanymapping_clientCompanyId_fkey` FOREIGN KEY (`clientCompanyId`) REFERENCES `clientcompany`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_industryId_fkey` FOREIGN KEY (`industryId`) REFERENCES `Industry`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_numberOfEmployeesId_fkey` FOREIGN KEY (`numberOfEmployeesId`) REFERENCES `NumberOfEmployees`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
