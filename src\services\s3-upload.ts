import { PutBucketCorsCommand, PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { v4 as uuidv4 } from "uuid";

const client = new S3Client({
  credentials: {
    accessKeyId: process.env.S3_ACCESSKEY as string,
    secretAccessKey: process.env.S3_SECRETACCESS as string,
  },
  region: process.env.S3_REGION,
});

// const putcors = new PutBucketCorsCommand({
//   Bucket: process.env.S3_BUCKETNAME,
//   CORSConfiguration: {
//     CORSRules: [
//       {
//         // Allow all headers to be sent to this bucket.
//         AllowedHeaders: ["*"],
//         // Allow only GET and PUT methods to be sent to this bucket.
//         AllowedMethods: ["GET", "PUT"],
//         // Allow only requests from the specified origin.
//         AllowedOrigins: ["http://localhost:8100", 
//         "https://devjobzapp.astromatch.io", 
//         "http://*************:4082",
//         "https://devjjimg.astromatch.io",
//         "https://devjj.astromatch.io"
//       ],
//         // Allow the entity tag (ETag) header to be returned in the response. The ETag header
//         // The entity tag represents a specific version of the object. The ETag reflects
//         // changes only to the contents of an object, not its metadata.
//         ExposeHeaders: ["ETag"],
//         // How long the requesting browser should cache the preflight response. After
//         // this time, the preflight request will have to be made again.
//         MaxAgeSeconds: 3600,
//       },
//     ],
//   },
// });

export const s3Upload = async (
  path: string,
  user_id: number,
  file_format: string
) => {
  // const response = await client.send(putcors);

  const key = `${path}/${user_id}/${uuidv4()}.${file_format}`;
  const command = new PutObjectCommand({
    Bucket: process.env.S3_BUCKETNAME,
    Key: key,
  });
  return await getSignedUrl(client, command, {
    expiresIn: parseInt(process.env.S3_EXPIRY as string),
  });
};
