-- AlterTable
ALTER TABLE `SkillList` RENAME TO `skilllist`;

-- AlterTable
ALTER TABLE `recruiterjobpostdetail` ADD COLUMN `jobTitleId` INTEGER NULL;

-- AlterTable
ALTER TABLE `skilllist` ADD COLUMN `jobRoleId` INTEGER NULL;

-- CreateTable
CREATE TABLE `jobtitle` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `jobRoleId` INTEGER NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `jobtitle` ADD CONSTRAINT `jobtitle_jobRoleId_fkey` FOREIGN KEY (`jobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `skilllist` ADD CONSTRAINT `skilllist_jobRoleId_fkey` FOREIGN KEY (`jobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobTitleId_fkey` FOREIGN KEY (`jobTitleId`) REFERENCES `jobtitle`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
