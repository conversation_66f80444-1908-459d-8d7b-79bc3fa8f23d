/*
  Warnings:

  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.

*/
-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- CreateTable
CREATE TABLE `recruiterdocument` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `aadhaarNumber` VARCHAR(255) NULL,
    `panNumber` VARCHAR(255) NULL,
    `gstin` VARCHAR(255) NULL,
    `fssai` VARCHAR(255) NULL,
    `udyogAadhaar` VARCHAR(255) NULL,
    `aadhaarImgUrl` VARCHAR(255) NULL,
    `panImgUrl` VARCHAR(255) NULL,
    `gstinImgUrl` VARCHAR(255) NULL,
    `fssaiImgUrl` VARCHAR(255) NULL,
    `udyogAadhaarImgUrl` VARCHAR(255) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterId` INTEGER NOT NULL,
    `personalstatusId` INTEGER NULL DEFAULT 1,
    `companystatusId` INTEGER NULL DEFAULT 1,
    `personalRejectReason` VARCHAR(255) NULL,
    `companyRejectReason` VARCHAR(255) NULL,

    UNIQUE INDEX `recruiterdocument_recruiterId_key`(`recruiterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Verificationstatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_personalstatusId_fkey` FOREIGN KEY (`personalstatusId`) REFERENCES `Verificationstatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_companystatusId_fkey` FOREIGN KEY (`companystatusId`) REFERENCES `Verificationstatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
