/*
  Warnings:

  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.

*/
-- AlterTable
ALTER TABLE `user` ADD COLUMN `coachMarkStatus` B<PERSON><PERSON>EAN NOT NULL DEFAULT false,
    MODIFY `pauseUntil` DATETIME NULL;

-- CreateTable
CREATE TABLE `usernotification` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(30) NOT NULL DEFAULT '',
    `description` VARCHAR(150) NOT NULL DEFAULT '',
    `isRead` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `userId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON>ign<PERSON>ey
ALTER TABLE `usernotification` ADD CONSTRAINT `usernotification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
