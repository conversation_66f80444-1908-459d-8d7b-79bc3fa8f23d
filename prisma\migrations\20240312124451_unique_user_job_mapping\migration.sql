/*
  Warnings:

  - You are about to alter the column `pauseUntil` on the `user` table. The data in that column could be lost. The data in that column will be cast from `DateTime(0)` to `DateTime`.
  - A unique constraint covering the columns `[userId,jobId]` on the table `userjobmapping` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,jobId]` on the table `userjobviewmapping` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE `user` MODIFY `pauseUntil` DATETIME NULL;

-- CreateIndex
CREATE UNIQUE INDEX `userjobmapping_userId_jobId_key` ON `userjobmapping`(`userId`, `jobId`);

-- CreateIndex
CREATE UNIQUE INDEX `userjobviewmapping_userId_jobId_key` ON `userjobviewmapping`(`userId`, `jobId`);
