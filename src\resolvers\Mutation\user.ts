import { Context } from "../../context";
import bcrypt from "bcryptjs";
import { protect, requireAdmin, ROLES, isAdmin } from "../../middleware/auth";
import { responseHandler } from "../../utils/responseHandler";
import {
  CreateUserPayload,
  CreateUserArgs,
  CreateUserInput,
  LoginUserArgs,
  LoginUserInput,
  LoginUserPayload,
  uploadResumeArgs,
  uploadResumeInput,
  uploadResumePayload,
  UpdatePasswordArgs,
  UpdatePasswordInput,
  UpdatePasswordPayload,
  UpdateRecruiterStatusArgs,
	UpdateRecruiterStatusInput,
	UpdateRecruiterStatusPayload,
  AssignRoleArgs,
  AssignRoleInput,
  AssignRolePayload,
  GetCurrentUserPayload,
} from "../../types/user.types";
import { createHmac } from "node:crypto";
import { getSignedJwtToken } from "../../extensions/jwtTokenExtension";
import { s3Upload } from "../../services/s3-upload";

export const userResolvers = {
  createUser: async (
    parent: unknown,
    {
      input: { name, email, password, confirmPassword, roleId },
    }: CreateUserArgs<CreateUserInput>,
    { prisma, req }: Context
  ): Promise<CreateUserPayload> => {
    // const currentUser = await protect(req);
    // if (currentUser && currentUser.roleId !== 1) {
    //   return responseHandler(
    //     "CreateUserErrors",
    //     "Not Authorized to access",
    //     401
    //   );
    // }
    try {
      if (!name || !email || !password || !confirmPassword) {
        return responseHandler(
          "CreateUserErrors",
          "You must provide all required fields",
          400
        );
      }

      // Check if user is authenticated and has admin role following your pattern
      const currentUser = await protect(req);

      // Only admins can create users and assign roles
      if (!isAdmin(currentUser)) {
        return responseHandler(
          "CreateUserErrors",
          "Only administrators can create new users",
          403
        );
      }

      if (password !== confirmPassword) {
        return responseHandler(
          "CreateUserErrors",
          "Password and confirm password do not match",
          400
        );
      }

      // Set default role to USER if not specified - Only Admin (1) and User (2) allowed
      const assignedRoleId = roleId || ROLES.USER;

      // Only allow Admin (1) or User (2) roles
      if (assignedRoleId !== ROLES.ADMIN && assignedRoleId !== ROLES.USER) {
        return responseHandler(
          "CreateUserErrors",
          "Invalid role specified. Only Admin or User roles are allowed.",
          400
        );
      }

      const emailHash = createHmac(
        "sha256",
        process.env.EMAILHASHSECRET as string
      )
        .update(email)
        .digest("hex");

      const newUser = await prisma.crmuser.create({
        data: {
          name,
          email,
          password: bcrypt.hashSync(password, 8),
          roleId: assignedRoleId,
          emailHash,
        },
        include: {
          role: true,
        },
      });

      if (newUser && newUser.id) {
        return responseHandler(
          "CreateUserErrors",
          `User created successfully with ${newUser.role.name} role`,
          201
        );
      } else {
        return responseHandler(
          "CreateUserErrors",
          "Issue while creating the user",
          400
        );
      }
    } catch (error: any) {
      console.log("====error create new crm user", error);
      if (error.code === 'P2002' && error.meta?.target === 'crmuser_email_key') {
        return responseHandler("CreateUserErrors", "Email already exist", 400);
      } else {
        return responseHandler("CreateUserErrors", "unknown issue", 400);
      }
    }
  },

  loginUser: async (
    parent: unknown,
    { input: { email, password } }: LoginUserArgs<LoginUserInput>,
    { prisma, req, res }: Context
  ): Promise<LoginUserPayload> => {
    try {
      if (!email && !password) {
        return responseHandler(
          "LoginUserErrors",
          "You must provide a required field",
          400
        );
      } else {
        const getUser = await prisma.crmuser.findUnique({
          where: {
            email
          },
          include: {
            role: true
          }
        });
        if (getUser && getUser.id && getUser.email === email) {
          const checkpwd = bcrypt.compareSync(password, getUser.password);
          if (!checkpwd) {
            return responseHandler(
              "LoginUserErrors",
              "Password not match with entered email",
              400
            );
          } else {
            const generateToken = await getSignedJwtToken.crmuser.getJwtToken(
              getUser.id
            );

            const options = {
              expires: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000),
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
            };

            res.cookie("jjcrm_token", generateToken, options);
            return responseHandler("LoginUserErrors", "Successfully logged in", 200, {
              token: generateToken as string,
              user: {
                id: getUser.id,
                name: getUser.name,
                email: getUser.email,
                roleId: getUser.roleId,
                roleName: getUser.role.name,
              }
            });
          }
        } else {
          return responseHandler(
            "LoginUserErrors",
            "Email not match with password",
            400
          );
        }
      }
    } catch (error) {
      console.log("====error create new crm user", error);
      return responseHandler("LoginUserErrors", "unknown issue", 400);
    }
  },

  updatePassword: async (
    parent: unknown,
    { input: { oldPassword, newPassword } }: UpdatePasswordArgs<UpdatePasswordInput>,
    { prisma, req, res }: Context
  ): Promise<UpdatePasswordPayload> => {
    const currentUser = await protect(req);
    if (currentUser) {
      if (!oldPassword || !newPassword) {
        return responseHandler(
          "updatePasswordErrors",
          "You must provide a required field",
          400
        );
      } else if (oldPassword === newPassword) {
        return responseHandler(
          "updatePasswordErrors",
          "You must provide a different password",
          400
        );
      } else {
        try {
          const isOldPasswordValid = bcrypt.compareSync(oldPassword, currentUser.password);
          if(!isOldPasswordValid) {
            return responseHandler(
              "updatePasswordErrors",
              "Old Password doesnt match!",
              400
            );
          } else {
            await prisma.crmuser.update({
              where:{
                id: +currentUser.id
              },
              data:{
                password: bcrypt.hashSync(newPassword, 8),
              }
            });
            return responseHandler("updatePasswordErrors", "Password updated successfully", 200);

          }

        } catch (error) {
          return responseHandler("updatePasswordErrors", "unknown issue", 400);

        }
      }
    } else {
      return responseHandler("uploadResumeError", "No user found", 401);
    }
  },
  

  
  uploadResume: async (
    parent: unknown,
    { input: { userId, path, file_format } }: uploadResumeArgs<uploadResumeInput>,
    { req }: Context
  ): Promise<uploadResumePayload> => {
    const currentUser = await protect(req);

    if (currentUser) {
      if (!path && !file_format && !userId) {
        return responseHandler(
          "uploadResumeError",
          "Please provide required details",
          400
        );
      } else {
        try {
          const signedURL = await s3Upload(path, userId, file_format);

          return responseHandler(
            "uploadResumeError",
            "successfully uploaded",
            200,
            {
              signedURL: signedURL,
            }
          );
        } catch (error) {
          return responseHandler(
            "uploadResumeError",
            "Failed to upload resume",
            400
          );
        }
      }
    } else {
      return responseHandler("uploadResumeError", "No user found", 401);
    }
  },
  updateRecruiterStatus: async (
		parent: unknown,
		{
			input: { recruiterId, statusId },
		}: UpdateRecruiterStatusArgs<UpdateRecruiterStatusInput>,
		{ prisma, req }: Context,
	): Promise<UpdateRecruiterStatusPayload> => {
		const currentUser = await protect(req);

		if (currentUser) {
			if (!recruiterId && !statusId) {
				return responseHandler(
					'UpdateRecruiterStatusErrors',
					'Please provide required details',
					400,
				);
			} else {
				try {
					await prisma.recruiter.update({
						where: {
							id: +recruiterId,
						},
						data: {
							recruiterStatusId: +statusId,
						},
					});

					return {
						UpdateRecruiterStatusErrors: [],
						message: 'Recruiter Status Updated SuccessFully',
						status: 200,
					};
				} catch (error) {
					return responseHandler(
						'UpdateRecruiterStatusErrors',
						'Failed to Update Recruiter Status',
						400,
					);
				}
			}
		} else {
			return responseHandler(
				'UpdateRecruiterStatusErrors',
				'No user found',
				401,
			);
		}
	},

  // Admin-only function to assign roles to users following your pattern
  assignRole: async (
    parent: unknown,
    { input: { userId, roleId } }: AssignRoleArgs<AssignRoleInput>,
    { prisma, req }: Context
  ): Promise<AssignRolePayload> => {
    try {
      // Only admins can assign roles
      const currentUser = await requireAdmin(req);

      if (!userId || !roleId) {
        return responseHandler(
          "AssignRoleErrors",
          "User ID and Role ID are required",
          400
        );
      }

      // Validate role ID - Only Admin (1) or User (2) roles allowed
      if (roleId !== ROLES.ADMIN && roleId !== ROLES.USER) {
        return responseHandler(
          "AssignRoleErrors",
          "Invalid role specified. Only Admin or User roles are allowed.",
          400
        );
      }

      // Check if target user exists
      const targetUser = await prisma.crmuser.findUnique({
        where: { id: userId },
        include: { role: true }
      });

      if (!targetUser) {
        return responseHandler(
          "AssignRoleErrors",
          "User not found",
          404
        );
      }

      // Update user role
      const updatedUser = await prisma.crmuser.update({
        where: { id: userId },
        data: { roleId },
        include: { role: true }
      });

      return responseHandler(
        "AssignRoleErrors",
        `User role updated to ${updatedUser.role.name} successfully`,
        200
      );
    } catch (error: any) {
      console.log("====error assigning role", error);
      return responseHandler("AssignRoleErrors", "Failed to assign role", 400);
    }
  },

  // Get current user information following your pattern
  getCurrentUser: async (
    parent: unknown,
    args: unknown,
    { req }: Context
  ): Promise<GetCurrentUserPayload> => {
    try {
      const currentUser = await protect(req);

      if (!currentUser) {
        return responseHandler("getCurrentUserErrors", "User not found", 404);
      }

      return responseHandler("getCurrentUserErrors", "User retrieved successfully", 200, {
        user: {
          id: currentUser.id,
          name: currentUser.name,
          email: currentUser.email,
          roleId: currentUser.roleId,
          roleName: currentUser.role.name,
        }
      });
    } catch (error: any) {
      console.log("====error getting current user", error);
      return responseHandler("getCurrentUserErrors", "Failed to get user information", 401);
    }
  },
};
