import { Context } from "../../context";
import bcrypt from "bcryptjs";
import { protect } from "../../middleware/auth";
import { responseHandler } from "../../utils/responseHandler";
import {
  CreateUserPayload,
  CreateUserArgs,
  CreateUserInput,
  LoginUserArgs,
  LoginUserInput,
  LoginUserPayload,
  uploadResumeArgs,
  uploadResumeInput,
  uploadResumePayload,
  UpdatePasswordArgs,
  UpdatePasswordInput,
  UpdatePasswordPayload,
  UpdateRecruiterStatusArgs,
	UpdateRecruiterStatusInput,
	UpdateRecruiterStatusPayload
} from "../../types/user.types";
import { createHmac } from "node:crypto";
import { getSignedJwtToken } from "../../extensions/jwtTokenExtension";
import { s3Upload } from "../../services/s3-upload";

export const userResolvers = {
  createUser: async (
    parent: unknown,
    {
      input: { name, email, password, confirmPassword, roleId },
    }: CreateUserArgs<CreateUserInput>,
    { prisma, req }: Context
  ): Promise<CreateUserPayload> => {
    // const currentUser = await protect(req);
    // if (currentUser && currentUser.roleId !== 1) {
    //   return responseHandler(
    //     "CreateUserErrors",
    //     "Not Authorized to access",
    //     401
    //   );
    // }
    try {
      if (!name && !email && !password && !confirmPassword) {
        return responseHandler(
          "CreateUserErrors",
          "You must provide a required field",
          400
        );
      } else {
        if (password === confirmPassword) {
          const emailHash = createHmac(
            "sha256",
            process.env.EMAILHASHSECRET as string
          )
            .update(email)
            .digest("hex");

          const createUser = await prisma.crmuser.create({
            data: {
              name,
              email,
              password: bcrypt.hashSync(password, 8),
              roleId: +roleId
            },
          });
          if (createUser && createUser.id) {
            return responseHandler(
              "CreateUserErrors",
              "User Created Successfully",
              201
            );
          } else {
            return responseHandler(
              "CreateUserErrors",
              "Issue while creat the user",
              400
            );
          }
        } else {
          return responseHandler(
            "CreateUserErrors",
            "Password and confirm password not matched",
            400
          );
        }
      }
    } catch (error: any) {
      console.log("====error create new crm user", error);
      if (error.code === 'P2002' && error.meta?.target === 'crmuser_email_key') {
        return responseHandler("CreateUserErrors", "Email already exist", 400);
      } else {
        return responseHandler("CreateUserErrors", "unknown issue", 400);
      }
    }
  },

  loginUser: async (
    parent: unknown,
    { input: { email, password } }: LoginUserArgs<LoginUserInput>,
    { prisma, req, res }: Context
  ): Promise<LoginUserPayload> => {
    try {
      if (!email && !password) {
        return responseHandler(
          "LoginUserErrors",
          "You must provide a required field",
          400
        );
      } else {
        const getUser = await prisma.crmuser.findUnique({
          where: {
            email
          },
        });
        if (getUser && getUser.id && getUser.email === email) {
          const checkpwd = bcrypt.compareSync(password, getUser.password);
          if (!checkpwd) {
            return responseHandler(
              "LoginUserErrors",
              "Password not match with entered email",
              400
            );
          } else {
            const generateToken = await getSignedJwtToken.crmuser.getJwtToken(
              getUser.id
            );

            const options = {
              expires: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000),
              // signed: true,
              // secure: true,
              // httpOnly: true,
            };

            res.cookie("token", generateToken, options);
            return responseHandler("LoginUserErrors", "Successfully loggedin", 200, {
              token: generateToken as string,
            });
          }
        } else {
          return responseHandler(
            "LoginUserErrors",
            "Email not match with password",
            400
          );
        }
      }
    } catch (error) {
      console.log("====error create new crm user", error);
      return responseHandler("LoginUserErrors", "unknown issue", 400);
    }
  },

  updatePassword: async (
    parent: unknown,
    { input: { oldPassword, newPassword } }: UpdatePasswordArgs<UpdatePasswordInput>,
    { prisma, req, res }: Context
  ): Promise<UpdatePasswordPayload> => {
    const currentUser = await protect(req);
    if (currentUser) {
      if (!oldPassword || !newPassword) {
        return responseHandler(
          "updatePasswordErrors",
          "You must provide a required field",
          400
        );
      } else if (oldPassword === newPassword) {
        return responseHandler(
          "updatePasswordErrors",
          "You must provide a different password",
          400
        );
      } else {
        try {
          const isOldPasswordValid = bcrypt.compareSync(oldPassword, currentUser.password);
          if(!isOldPasswordValid) {
            return responseHandler(
              "updatePasswordErrors",
              "Old Password doesnt match!",
              400
            );
          } else {
            await prisma.crmuser.update({
              where:{
                id: +currentUser.id
              },
              data:{
                password: bcrypt.hashSync(newPassword, 8),
              }
            });
            return responseHandler("updatePasswordErrors", "Password updated successfully", 200);

          }

        } catch (error) {
          return responseHandler("updatePasswordErrors", "unknown issue", 400);

        }
      }
    } else {
      return responseHandler("uploadResumeError", "No user found", 401);
    }
  },
  

  
  uploadResume: async (
    parent: unknown,
    { input: { userId, path, file_format } }: uploadResumeArgs<uploadResumeInput>,
    { req }: Context
  ): Promise<uploadResumePayload> => {
    const currentUser = await protect(req);

    if (currentUser) {
      if (!path && !file_format && !userId) {
        return responseHandler(
          "uploadResumeError",
          "Please provide required details",
          400
        );
      } else {
        try {
          const signedURL = await s3Upload(path, userId, file_format);

          return responseHandler(
            "uploadResumeError",
            "successfully uploaded",
            200,
            {
              signedURL: signedURL,
            }
          );
        } catch (error) {
          return responseHandler(
            "uploadResumeError",
            "Failed to upload resume",
            400
          );
        }
      }
    } else {
      return responseHandler("uploadResumeError", "No user found", 401);
    }
  },
  updateRecruiterStatus: async (
		parent: unknown,
		{
			input: { recruiterId, statusId },
		}: UpdateRecruiterStatusArgs<UpdateRecruiterStatusInput>,
		{ prisma, req }: Context,
	): Promise<UpdateRecruiterStatusPayload> => {
		const currentUser = await protect(req);

		if (currentUser) {
			if (!recruiterId && !statusId) {
				return responseHandler(
					'UpdateRecruiterStatusErrors',
					'Please provide required details',
					400,
				);
			} else {
				try {
					await prisma.recruiter.update({
						where: {
							id: +recruiterId,
						},
						data: {
							recruiterStatusId: +statusId,
						},
					});

					return {
						UpdateRecruiterStatusErrors: [],
						message: 'Recruiter Status Updated SuccessFully',
						status: 200,
					};
				} catch (error) {
					return responseHandler(
						'UpdateRecruiterStatusErrors',
						'Failed to Update Recruiter Status',
						400,
					);
				}
			}
		} else {
			return responseHandler(
				'UpdateRecruiterStatusErrors',
				'No user found',
				401,
			);
		}
	}
};
