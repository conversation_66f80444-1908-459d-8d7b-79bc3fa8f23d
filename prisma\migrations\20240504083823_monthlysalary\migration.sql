-- AlterTable
ALTER TABLE `MonthlySalaryRange` RENAME TO `monthlysalaryrange`;

-- AlterTable
ALTER TABLE `monthlysalaryrange` ADD COLUMN `maxSalary` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `minSalary` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    ADD COLUMN `name_ta` VARCHAR(50) NOT NULL DEFAULT '';

-- AlterTable
ALTER TABLE `user` ADD COLUMN `deleteFeedbackOtherReason` VARCHAR(255) NULL DEFAULT '';

-- AlterTable
ALTER TABLE `userjobmapping` ADD COLUMN `jobAppliedDate` DATETIME(3) NULL DEFAULT CURRENT_TIMESTAMP(3);

-- AlterTable
ALTER TABLE `userjobviewmapping` ADD COLUMN `jobViewedDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);
