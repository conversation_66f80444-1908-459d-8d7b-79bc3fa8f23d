export const typeDefs = `#graphql

enum CacheControlScope {
  PUBLIC
  PRIVATE
}

directive @cacheControl (
  maxAge: Int
  scope: CacheControlScope
  inheritMaxAge: Boolean
) on FIELD_DEFINITION | OBJECT | INTERFACE | UNION

## Query ##
type Query {
  getCRMUserRole: [CRMUserRole] # Get CRM user role (Admin only)
  getAllCRMUsers: [CRMUserInfo] # Get all CRM users (Admin only)
  getCurrentUser: GetCurrentUserPayload # Get current user information

  # getCurrentCMRUser: CurrentCRMUserPayload
  # searchRecruiter(mobileNumber): CRMUser
  recruiterVerificationList: [VerificationListPayload] # Get all recruiter list for verificaiton
  getPendingCompanyList: [PendigCompanies] #to get pending company details
  syncSolrDB: [Job]

  ##Request Callback
  getRecruiterRequestCallback(filter: recruiterReqCallbackFilter ):[RecruiterRequest]
  getRequestCallbackStatus:[Requestcallbackstatus]

  ##recruiter details
  workType: [WorkType]
  shiftType: [ShiftType]
  employmentType: [EmploymentType]
  workingDays: [WorkingDays]
  joinPreference: [JoinPreference]
  jobBenefits: [JobBenefits]
  depositReason: [DepositReason]
  interviewType: [InterviewType]
  requiredAssets: [AssetRequired]
  englishKnowledge: [EnglishKnowledge]
  distance: [Distance]
  workExperience: [WorkExperience]
  educationQualification: [Qualification]
  gender: [Gender]
  companyList: [CompanyList]
  getJobTitle: [JobTitle]
  jobRole: [JobRole]
  skills: [Skills]

  ## VerifyStatus =>  dashboard, Company Listing, recruiterListing, jobListing, callback Listing
  getCRMDetails: RecruiterFullDetails
  getCompanyApprovalDetails(filter: approvalStatusFilter): ApprovalStatusDetails
  getIdentityApprovalDetails(filter: approvalStatusFilter): IdentityApproval
  getPendingJobs(filter: jobStatus): JobsResponse #get pending jobs for verificaiton
  getCallBackDetails(filter: callBackFilter): [callBackDetails]
  getCompanyList: [CompanyListDetails]
  getUnverifiedRecruiter(filter: recruiterApprovalStatusFilter): RecruiterVerifyDetails

  getSeekerDetail(filter: seekerStatusFilter): SeekerDetails
  getSelectedUser(userId: ID): UserDetails
  recruiterWhatsappNotification(filter: recruiterFilter): RecruiterSuccessStatus

  ## get single record for verifying
  getRecruiterVerificationDetail(recruiterId: ID!): VerificationDetailsPayload #get single recruiter verification detail
  getCompanyVerificationDetail(companyId: ID!): CompanyVerificationDetailsPayload #get single company verification detail
  GetSelectedJob(job_id: ID!): JobBasic #get selected Job from list
  getLoginInfo(filter: loginInfoFilter): LoginInfoDetails

  ## Plan and Payment
  plans: PlansPayload # Get All Plans
  getCreditRequests: GetCreditRequestsPayload
  recruiterCreditHistory(recruiterId: ID!): CreditHistoryPayload  
}

type LoginInfoDetails {
  loginDetails: [LoginInfo]
   totalCount : Int
}


type UserLoginDetails {
  id: ID
  name: String
  mobileNumber: String
}

type RecruiterLoginDetails{
  id: ID
  name:String
  mobileNumber: String
  company: CompanyLoginDetails
  RecruiterRole: RecruiterLoginRole
  HiringFor: HiringForDetails
}

type HiringForDetails {
   id: ID, 
   name: String
}

enum UserloginType{
  RECRUITER
  CANDIDATE
}

input loginInfoFilter{
  type:UserloginType!
  date: String
  pageNumber: Int!
  searchText: String
  take: Int
}

type LoginInfo {
  id: ID
  loginDate:String
  loginDevice: String
  devicePlatform: String
  deviceId:String
  loginType: LoginType
  logoutDate: String
  logoutType: LogoutType
  recruiter: RecruiterLoginDetails
  user:UserLoginDetails
}


type CompanyLoginDetails {
  id: ID
  name: String
  location: String
  buildingName: String
}

type RecruiterLoginRole{
  id: ID
  name: String
}

type LoginType{
  id: ID
  name: String
}

type LogoutType{
  id: ID
  name: String
}


type IdentityApproval {
 identityDetails: [approvalStatusDetails]
 totalCount: Int
}

type RecruiterVerifyDetails {
  recruiterDetails: [RecruiterCompanyDetails]
   totalCount: Int

}

type RecruiterSuccessStatus{
  message: String
}

type ApprovalStatusDetails {
 companyDetails: [approvalStatusDetails]
 totalCount: Int
 }

type SeekerDetails {
seekerDetails: [seekerStatusDetails]
totalCount: Int
}

type JobRole {
  id: ID
  name: String
  name_en: String
  name_ta: String
  jobRoleCategoryId: ID
}

type JobTitle {
  id: ID
  name: String
  name_en: String
  name_ta: String
  isActive: Boolean
  jobRoleId: ID
  jobRole: JobRole
}


type JobsResponse {
jobs: [Job],
count: Int 
}

type Language {
  id: ID
  name: String
  code: ID
},

type UserEducation {
  id: ID!
  userId: ID!
  qualification: Qualification
  course: Course
  specialization: Specialization
  degreeMode: DegreeMode
  higherSecondaryPercentage: Float
  graduatePercentage: Float
  collegeName: String
  graduationYear: Int
}

# DegreeMode Type 
type DegreeMode {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type Course {
  id: ID!
  name_en: String!
  name_ta: String!
  name: String!
  qualificationId: ID
  qualification: Qualification
}

type Specialization {
  id: ID!
  name: String!
  name_en: String!
  name_ta: String!
  courseId: ID!
  course: Course
}

type Qualification {
  id: ID!
  name: String!
  name_en: String!
  name_ta: String!
}

type JobRoleCategory {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type Distance {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type CommuteMode {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type Commute {
  commuteMode: CommuteMode
}

type employment {
  employmentType: EmploymentType
}

type EmploymentType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type workTypeMap{
  workType: WorkType
}

type WorkType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type shiftTypeMap {
  shiftType: ShiftType
}

type ShiftType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type workingDaysMap {
  workingDays: WorkingDays
}

type WorkingDays {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type UserJobPreferences{
  id: ID
  userId: ID
  prefJobRole: JobRole
  prefRoleCategory: JobRoleCategory
  city: String
  area: String
  distance: Distance
  userCommuteMode: [Commute]
  userEmploymentType: [employment]
  userWorkType: [workTypeMap]
  userShiftType: [shiftTypeMap]
  userWorkingDays: [workingDaysMap]
}

type WorkingDomain {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type EnglishKnowledge {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type languageMap{
  profileLanguage: ProfileLanguage
}

type ProfileLanguage {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type UserProfessional {
  id: ID
  userId: ID
  currentJobRole: JobRole
  currentRoleCategory: JobRoleCategory
  workingDomain: [WorkingDomain]
  workExperience: String
  experienceYear: Int
  experienceMonth: Int
  monthlySalary: Int
  previousJobTitle: String
  companyName: String
  englishKnowledge: EnglishKnowledge
  resume: String
  professionalSummary: String
  languagesKnown: [languageMap]
  skills: [MappingSkills]
}

type MappingSkills {
  userProfessionalId: ID,
    skillId: ID,
    skill: Skills
}

type Skills {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type UserDetails {
  mobileNumber: String
  id: ID
  gender: String
  name: String
  email: String
  dateOfBirth: String
  twoWheelerLicense: Boolean
  profilePicture: String,
  userStatusId: ID,
  latitude: String,
  longitude: String,
  language: Language
  userEducation: UserEducation
  userJobPreferences: UserJobPreferences
  userProfessional: UserProfessional
}

input jobStatus {
  status: JobStatusEnum!
  pageNumber: Int!
  take: Int
}


type CompanyList {
  id: ID
  name: String
  name_en: String
  name_ta: String
  isActive: Boolean
}

type EnglishKnowledge {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

enum JobStatusEnum{
  completedRequest
  pendingRequest
  expiredRequest
  allRequest
}

type CompanyVerificationDetailsPayload {
 id: ID
  name: String 
  NumberOfEmployees: NumberOfEmployees
  industry: Indstury
  companyListId: ID
  CompanyList: CompanyListDetails
  isCompanyVerified: Boolean
  Recruiter: RecruiterDetails
}

type RecruiterDetails {
  id:ID
  name: String
  mobileNumber: String
  email: String
  Recruiterdocument: VerifyDocumentDetails
}

type CompanyListDetails {
   id: ID
   name: String 
   isActive: Boolean 
}

type callBackDetails {
  id: ID
  crmUserId: ID
  createdAt: String
  updatedAt: String
  crmuser: CRMUser
  requestCallBackStatusId: ID
  requestCallBackStatus: RecruiterCallbackStatus
  recruiterId: ID
  recruiter: RecruiterCompanyDetails
}

type RecruiterCallbackStatus {
  id: ID,
  name: String
}

input approvalStatusFilter {
  type: companySearchType !
  pageNumber: Int!
  searchText: String
  take: Int
}

input recruiterReqCallbackFilter {
  requestStatusId:ID!
  pageNumber: Int
  searchText: String
  take: Int
}

input recruiterApprovalStatusFilter {
  type: recruiterSearchType !
  pageNumber: Int!
  recruiterWhatsAppUpdate: Boolean
  recruiterEmailUpdate: Boolean
  searchText: String
  take: Int
  isFetchAllData: Boolean
}

input recruiterFilter {
  recruiterDetails:[RecruiterDetailInput]!
  templateType: String!
  modeOfCommunication: ModeOfCommunication!
}

enum ModeOfCommunication {
  email
  whatsapp
}

input RecruiterDetailInput {
  name: String
  email: String
  phone: String
}


enum seekerSearchType {
  allUsers
  deletedUsers
  activeUsers
}

input seekerStatusFilter{
  type: seekerSearchType !
  pageNumber: Int!
  searchText: String
  take: Int
}

input callBackFilter {
  type: callBackType !
  pageNumber: Int!
  searchText: String
}

enum callBackType {
  openRequest
  closedRequest
}

enum companySearchType {
  completedRequest
  pendingRequest
  rejectedRequest
  allRequest
}

enum recruiterSearchType {
  recruiterCreated
  recruiterUpdated
  companyCreated
  allRequest
}

type CompanyDocGiven {
  id: ID 
  name: String
}

type PersonalDocGiven {
  id: ID 
  name: String
}

type CompanystatusDetails {
 id: ID 
  name: String
}

type RecruiterRoleDetails {
 id: ID 
  name: String
}

type  RecruiterStatusDetails{
 id: ID 
  name: String
}      
  
type HiringForDetails{
  id: ID 
  name: String
}

type DesignationDetails {
  id: ID 
  name: String
}

type UserEduDetails {
  collegeName: String 
  collegesId: ID
  colleges: Colleges
}

type Colleges {
  id: ID
  name: String
}

type UserProfDetails {
  resume: String
}

type seekerStatusDetails {
  id: ID,
  mobileNumber:String,
  name: String,
  email: String,
  city:City
  state:State
  createdAt: String,
  updatedAt: String,
  userEducation: UserEduDetails
  userProfessional: UserProfDetails
}

type City {
  id: ID
  name: String
}

type State {
  id: ID
  name: String
}

type approvalStatusDetails {
  id: ID
  createdAt: String
  companyDocGivenId: ID
  companyDocGiven: CompanyDocGiven
  companystatusId: ID
  personalDocGivenId: ID,
  personalDocGiven: PersonalDocGiven
  Companystatus: CompanystatusDetails,
  recruiter: RecruiterCompanyDetails
}

type RecruiterCompanyDetails  {
  id: ID,
  mobileNumber: String,
  name: String,
  email: String,
  isEmailVerified: Boolean,
  isPersonalVerified: Boolean,
  isCompanyVerified: Boolean,
  whatsAppUpdate: Boolean,
  isRecruiterHoldEmail: Boolean,
  isRecruiterSubmitEmail: Boolean,
  isAppRatingSubmit: Boolean,
  isAppFeedbackSubmit: Boolean,
  mobileNumberHash: String,
  emailHash: String,
  appRating: Int,
  createdAt: String,
  updatedAt: String,
  recruiterRoleId: Int,
  recruiterStatusId: Int,
  hiringForId: Int,
  designationId: Int,
  RecruiterRole: RecruiterRoleDetails,
  RecruiterStatus: RecruiterStatusDetails,
  HiringFor: HiringForDetails,
  Designation: DesignationDetails,
  company: CompanyDetails
}

type CompanyDetails {
  id: ID
  name: String
  industryId: ID,
  numberOfEmployeesId: ID,
  location: String,
  buildingName: String,
  companyTypeId: ID,
  isCompanyVerified: Boolean,
  isDeleted: Boolean,
  createdAt: String,
  updatedAt: String,
  recruiterId: ID,
  companyListId: ID,
  industry: Indstury,
  NumberOfEmployees: NumberOfEmployees
  # getCRMDetails: RecruiterFullDetails
}

type RecruiterFullDetails {
  recruiterDetails:RecruiterCounts,
  companyManagement: CompanyCounts,
  recruiterManagement: VerificationCounts,
  jobDetails: JobCounts,
  callBack: CallbackCount,
  feedback: Int,
  jobSeeker: JobSeeker
  unverifiedDetails: UnverifiedRecruiterDetails
  loginDetails: LoginDetails
}
type UnverifiedRecruiterDetails {totalCount: Int, createdCount:Int, updatedDetailsCount: Int, updatedCompanyCount: Int}
type RecruiterCounts {totalCount: Int}
type CompanyCounts {totalCount: Int,verified: Int,pending:Int , rejected: Int}
type VerificationCounts {totalCount: Int,verified: Int,pending: Int,rejected: Int}
type JobCounts {totalCount: Int, active:Int , expired: Int , pending: Int}
type CallbackCount {totalCount: Int,open: Int,closed: Int}
type JobSeeker {totalCount: Int,deleted: Int}

type CRMUser {
  id: ID
  email: String
  mobileNumber: String
}

type LoginDetails {
  recruiter: Int, 
  candidate: Int
}

type PendigCompanies {
  id: ID
  name: String
  createdAt: String
  isCompanyVerified: Boolean
  CompanyList: PendingCompanyDetails
  Recruiter: RecruiterDetails
  location:String
}

type PendingCompanyDetails {
  id: ID
  name: String
  createdAt: String
  isCompanyVerified: Boolean
}

type RecruiterDetails {
  id: ID
  name: String
  isPersonalVerified: Boolean
  isCompanyVerified: Boolean
}

type Indstury {
  id: ID
  name: String
}

type NumberOfEmployees{
  id: ID
  name: String
}

type Company {
  id: Int
  name: String
  industry: Indstury
  # NumberOfEmployees: NumberOfEmployees
  location: String
  addressDetails: String
  # jobs: [Job]
}

type Job {
  id: ID
  companyName: String
  recruiter: Recruiter
  recruiterId: ID
  companyId: ID
  companyTypeId: ID
  companyListId: ID
  consultantId: ID
  jobStatusId: ID
  JobDetail: JobDetailPref
  candidatePreference: JobCandidatePref
  interview: JobInterviewPref
}

type Recruiter {
  id: ID            
  mobileNumber: String
  name: String 
  email: String
  # hiringFor: HiringFor
  hiringForId: ID
  recruiterStatusId: ID
  # RecruiterStatus: RecruiterStatus
  # RecruiterRole: RecruiterRole
  isEmailVerified: Boolean
  isPersonalVerified: Boolean
  isCompanyVerified: Boolean
  # company: Company
}


# Gender
type Gender {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type Qualification {
  id: ID!
  name: String
  name_en: String!
  name_ta: String!
}

# Work Experience (Fresher / exp)
type WorkExperience {
  id: ID
  name: String
  name_en: String
  name_ta: String
}
type Distance {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type JobRoleCategory {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type Skills {
  id: ID
  name: String
  name_en: String
  name_ta: String
  jobRoleId: ID
  jobRole: JobRole
}

## Interview
type Interview {
  id: ID
  hrName: String
  hrContactNumber: String
  InterviewType: InterviewType
  # allowCandidateCall: Boolean
  # whatsAppUpdate: Boolean
  faceInterview: FTFInterview
  WalkinInterview: WInterview
}

type WInterview {
  walkinStartDate: String
  duration: Int
  startTime: String
  endTime: String    
  location: String
  buildingName: String
  latitude: String
  longitude: String
}


type FTFInterview {
  location: String
  buildingName: String
   latitude: String
  longitude: String
}

# Asset Required Type
type AssetRequired {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

## Candiate 
type Candidate {
  id: ID
  Gender: Gender
  Education: Qualification
  WorkExperience: WorkExperience
  minExperience: Int
  maxExperience: Int
  applicationRadius: Distance
  EnglishKnowledge: EnglishKnowledge
  RecruiterSkillsMapping: [Skills]
  RequiredAssetsMapping: [AssetRequired]
  isAssetsRequired: Boolean
}

type JobBasic {
  consultantCompany: Consultant
  CompanyType: CompanyType
  jobStatus: JobStatus
  JobDetail: JobBase
  candidatePreference: Candidate
  interviewPreference: Interview
  #userJobMapping: [UserJobMap]
  #userJobViewMapping: [UserJobViewMap]
  #userReportJob: [UserReportJob]
}


type Consultant {
  id: ID,
  name: String
}

type CompanyType {
  id: ID,
  name: String
}

type JobStatus {
  id :ID
  name: String
}


# Job Category
type JobCategory {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type JoinPreference {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type WorkType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

# Job Benefits
type JobBenefits {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type WorkingDays {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type EmploymentType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}


type ShiftType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

# Deposit Reason
type DepositReason {
  id: ID
  name: String
  name_en: String
  name_ta: String
}


# Interview Type
type InterviewType {
  id: ID
  name: String
  name_en: String
  name_ta: String
}

type JobBase {
  id: ID!
  companyName: String
  jobTitle: String
  JobTitle: JobTitle
  jobTitleId: ID
  jobId: ID
  jobCategory: JobCategory
  numberOpenings: Int
  jobUrgency: JoinPreference
  workType: WorkType
  jobBenefits: [JobBenefits]
  WorkingDays: WorkingDays
  depositReason: DepositReason
  shiftType: ShiftType
  employmentType: EmploymentType
  jobLocation: String
  city: String
  area: String
  state: String
  zipcode: String
  buildingName: String
  latitude: String
  longitude: String
  jobDescription: String
  minMonthSalary: Int
  maxMonthSalary: Int
  isIncentives: Boolean
  avgMonthlyIncentive: Int
  feesDeposit: Boolean
  depositAmount: Int
  additionalInformation: String
  createdAt: String
  updatedAt: String
  companyId: ID
  candidateProfile: Candidate
  interview: Interview
  #userJobMapping: [UserJobMap]
  #userJobViewMapping: [UserJobViewMap]
}


type JobDetailPref {
  id: ID
  companyName: String
  jobTitle: String
  jobTitleId: ID
  # jobCategory: JobCategory
  numberOpenings: Int
  # jobUrgency: JoinPreference
  # workType: WorkType
  # jobBenefits: [JobBenefits]
  # WorkingDays: WorkingDays
  # depositReason: DepositReason
  # shiftType: ShiftType
  # employmentType: EmploymentType
  jobLocation: String
  buildingName: String
  latitude: String
  longitude: String
  jobDescription: String
  minMonthSalary: Int
  maxMonthSalary: Int
  isIncentives: Boolean
  isJobApplied: Boolean
  isJobViewed: Boolean
  avgMonthlyIncentive: Int
  feesDeposit: Boolean
  depositAmount: Int
  additionalInformation: String
  createdAt: String
  updatedAt: String
  companyId: ID
  # userJobMapping: [UserJobMap]
  # userJobViewMapping: [UserJobViewMap]
  jobAppliedDate: String
  jobViewedDate: String
  city: String
  area: String
}

type JobCandidatePref {
  id: ID
  # Gender: Gender
  # Education: Qualification
  # WorkExperience: WorkExperience
  minExperience: Int
  maxExperience: Int
  # applicationRadius: Distance
  # EnglishKnowledge: Qualification
  # RecruiterSkillsMapping: [Skills]
  # RequiredAssetsMapping: [AssetRequired]
  isAssetsRequired: Boolean
}

type JobInterviewPref {
  id: ID
  hrName: String
  hrContactNumber: String
  # InterviewType: InterviewType
  allowCandidateCall: Boolean
  whatsAppUpdate: Boolean
  # faceInterview: FTFInterview
  # WalkinInterview: WInterview
}

type WInterview {
  walkinStartDate: String
  duration: Int
  startTime: String
  endTime: String    
  location: String
  fullAddress: String
}

type FTFInterview {
  location: String
  fullAddress: String
}

type VerificationDetailsPayload {
  id: ID
  name: String
  mobileNumber: String
  email: String
  company: Company
  Recruiterdocument: VerifyDocumentDetails
}

## Get Recruiter Verification List ##
type VerificationListPayload {
  id: ID
  name: String
  mobileNumber: String
  email: String
  company: Company
  Recruiterdocument: VerifyDocumentDetails
}

type VerifyDocumentDetails {
  aadhaarNumber: String
  panNumber: String
  gstin: String
  fssai: String
  udyogAadhaar: String
  personalstatusId: ID
  companystatusId: ID
  personalDocGivenId: ID
  companyDocGivenId: ID
  companyRejectReason: String
  personalRejectReason: String
  updatedAt: String
  Companystatus: CompanyStatus
  Personalstatus:PersonalStatus
}

type CompanyStatus {
  id: ID
  name: String
}

type PersonalStatus {
  id: ID
  name: String
}

type CurrentCRMUserPayload {
  message: String
  status: Int
  data: CRMUser
}

type CRMUserRole {
  id: ID
  name: String
  isActive: Boolean
}

type Recruiters{
  id: ID            
  mobileNumber: String
  name: String 
  email: String
  company: CompanyDetails
}

type RequestCallBackStatus{
  name:String
}

type RecruiterRequest {
  id: ID
  requestCallBackStatusId:ID
  requestCallBackStatus:RequestCallBackStatus
  crmUserId: ID
  recruiterId:ID
  crmuser:CRMUser
  recruiter :Recruiters
  createdAt: String
  updatedAt: String
}

type CreditRequestApprove {
  id: ID
  name: String
  isActive: Boolean
}

## Mutation ##
type Mutation {
  ## User ##
  createUser(input: CreateUserInput): CreateUserPayload # Create new User (Admin only)
  loginUser(input: LoginUserInput): LoginUserPayload # Login the user
  assignRole(input: AssignRoleInput): AssignRolePayload # Assign role to user (Admin only)
  getCurrentUser: GetCurrentUserPayload # Get current user information

  ## Update Recruiter Verification Status ##
  updatePersonalVerification(input: UpdatePersonalVerificationInput): UpdatePersonalVerificationPayload # Update Verification Status
  updateCompanyVerification(input: UpdateCompanyVerificationInput): UpdateCompanyVerificationPayload # Update company verification
  updateJobVerification(input: UpdateJobVerificationInput): UpdateJobVerificationPayload # Update job verification
  updateCompanyStatus(input: updateCompanyStatusInput): updateCompanyStatusPayload # Update company status in company list
  updateJob(input: updateJobInput): updateJobPayload # Update draft job details
  companyNameUpdate(input:companyNameInput):companyNamePayload #company name update
  jobTitleUpdate(input:jobTitleInput):jobTitlePayload #job title update
  uploadResume(input:uploadResumeInput):uploadResumePayload #Upload resume
  reActiveJob(input:reActiveJobInput):reActiveJobPayload #Re-Active Job
  updatePassword(input:updatePasswordInput):updatePasswordPayload #update user current password

  ##callback request
  requestCallbackStatusUpdate(input:CallbackStatusUpdateInput):CallbackStatusUpdatePayload
  blockRecruiter(input:blockRecruiterInput):blockRecruiterPayload


  ## Plan and Payment ##
  createPlan(input: CreatePlanInput): CreatePlanPayload #Create the new plan
  updatePlan(input: UpdatePlanInput): UpdatePlanPayload # Update the particular Plan
  approveJobCreditRequest(input: ApproveJobCreditRequestInput): ApproveJobCreditRequestPayload


  # Update Recruiter Status
  updateRecruiterStatus(input:UpdateRecruiterStatusInput):UpdateRecruiterStatusPayload
}

# Update Recruiter Types and Payload

input UpdateRecruiterStatusInput {
  recruiterId:ID!
  statusId:ID!
}

type UpdateRecruiterStatusError {
  message: String!
}


type UpdateRecruiterStatusPayload {
  UpdateRecruiterStatusErrors: [UpdateRecruiterStatusError!]!
  message: String
  status: Int
}




## Job Verification ##
input UpdateJobVerificationInput {
  recruiterId: ID!
  jobId: ID!
  verifyStatus: Boolean!
  jobRejectReason: String
  # jobTitleId: ID
  # jobTitleStatus : Boolean
}

type UpdateJobVerificationError {
  message: String!
}

type UpdateJobVerificationPayload {
  UpdateJobVerificationErrors: [UpdateJobVerificationError!]!
  message: String
  status: Int
}

## Create User Input Types ##
type User {
  id: ID
  name: String
  email: String
  roleId: ID
}

input CreateUserInput {
  name: String
  email: String
  password: String
  confirmPassword: String
  roleId: ID
}

type CreateUserError {
  message: String!
}

type CreateUserPayload {
  CreateUserErrors: [CreateUserError!]!
  message: String
  status: Int
}

## Login user input and types ##
input LoginUserInput {
  email: String
  password: String
}

type LoginUserError {
  message: String!
}

type LoginUserPayload {
  LoginUserErrors: [LoginUserError!]!
  message: String
  status: Int
  token: String
  user: UserInfo
}

## Update Recruiter company
input UpdateCompanyVerificationInput {
  recruiterId: ID
  comapnyListId: ID
  companyId: ID
  verifyStatus: Boolean
  companyRejectReason: String
  companyNameStatus: Boolean
}

type UpdateCompanyVerificationError {
  message: String!
}

type UpdateCompanyVerificationPayload {
  UpdateCompanyVerificationErrors: [UpdateCompanyVerificationError!]!
  message: String
  status: Int
}

# Update recruiter personal
input UpdatePersonalVerificationInput {
  recruiterId: ID
  verifyStatus: Boolean
  personalRejectReason: String
}

type UpdatePersonalVerificationError {
  message: String!
}

type UpdatePersonalVerificationPayload {
  UpdatePersonalVerificationErrors: [UpdatePersonalVerificationError!]!
  message: String
  status: Int
  # data: VerifyDocumentDetails
}

# Update company verification in company list
input updateCompanyStatusInput {
  recruiterId: ID!
  companyId: ID!
}

type updateCompanyStatusError {
  message: String!
}

type updateCompanyStatusPayload {
  updateCompanyStatusError: [updateCompanyStatusError!]!
  message: String
  status: Int
}

## request callback ##
input CallbackStatusUpdateInput {
  recruiterRequestId: ID!
  requestCallBackStatusId: ID!
}

type CallbackStatusUpdateErrors {
  message: String!
}

type CallbackStatusUpdatePayload {
  CallbackStatusUpdateErrors: [CallbackStatusUpdateErrors!]!
  message: String
  status: Int
}

type Requestcallbackstatus {
  id: ID
  name: String
}


# Update job details
input UpdateJobDetailInput {
  jobId: ID!
  jobTitle: String
  jobTitleId: ID
  jobCategoryId: ID
  jobLocation: String
  latitude: String
  longitude: String
  fullAddress: String
  numberOpenings: ID
  workTypeId: ID
  employmentTypeId: ID
  jobUrgencyId: ID
  jobDescription: String
  minMonthSalary: Int
  maxMonthSalary: Int
  isIncentives: Boolean
  avgMonthlyIncentive: Int
  jobBenefits: [ID]
  shiftTypeId: ID
  workingDaysId: ID
  feesDeposit: Boolean
  depositAmount: Int
  depositReasonId: ID
  additionalInformation: String
  buildingName: String
  state:String
  area:String
  zipcode:String
  city:String
}


# Recruiter candidate preference
input CandidatePreferenceInput {
  job_id: ID!
  gender: ID!
  education: ID!
  workExperience: ID!
  minExperience: Int
  maxExperience: Int
  applicationRadius: ID!
  englishKnowledge: ID!
  skills: [ID!]!
  isAssetsRequired: Boolean
  requiredAssets: [ID]
}


# Recruiter candidate preference
input InterviewDetailsInput {
  job_id: ID!
  hrName: String
  hrContactNumber: String
  interviewType: ID!
  faceInterview: FaceToFaceInterview
  walkIn: WalkInInterview
}

input FaceToFaceInterview {
  location: String
  fullAddress: String
  buildingName: String
  lat: String
  lng: String
}

input WalkInInterview {
  walkinStartDate: String!
  duration: Int
  walkinStartTime: String
  walkinEndTime: String    
  location: String
  fullAddress: String
  lat: String
  lng: String
  buildingName: String
}


# Update draft job details
input updateJobInput {
  job_id: ID!
  jobPref: UpdateJobDetailInput
  candidatePref: CandidatePreferenceInput
  interviewPref: InterviewDetailsInput
  # isJobEdited: Boolean
  # titleDetails: TitleDetails

}

input TitleDetails {
  jobTitle: String 
  jobId: ID 
  jobRoleId: ID
  jobTitleId: ID
  updatedTitleId: ID 
  jobTitleStatus: Boolean
}

type updateJobError {
  message: String!
}

type updateJobPayload {
  updateJobError: [updateJobError!]!
  message: String
  status: Int
}


#companyNameUpdate(input:companyNameInput):companyNamePayload
input companyNameInput {
  companyListId: ID
  companyName: String 
  companyId: ID
  updatedCompanyListID: ID
  removedCompanyListId: ID
}

type companyNameError {
  message: String!
}

type companyNamePayload {
  companyNameError: [companyNameError!]!
  message: String
  status: Int
} 

#Upload resume
input uploadResumeInput {
  path: String
  file_format: String
  userId: ID!
}

type uploadResumeError {
  message: String!
}

type uploadResumePayload {
  uploadResumeError: [uploadResumeError!]!
  message: String
  status: Int
} 


#Re-Active Job
input reActiveJobInput {
  jobId: ID!
}

type reActiveJobError {
  message: String!
}

type reActiveJobPayload {
  ReActiveJobErrors: [reActiveJobError!]!
  message: String
  status: Int
} 


#job title update
input jobTitleInput {
    jobTitleId: ID 
  jobRoleId: ID 
  jobTitle: String
  jobId: ID 
  updatedJobTitleId: ID 
  removedJobTitleId: ID
}

type jobTitleError {
  message: String!
}

type jobTitlePayload {
  jobTitleErrors: [jobTitleError!]!
  message: String
  status: Int
} 

# block recruiter
input blockRecruiterInput {
  recruiterId: ID!
  recruiterStatus:String!
}

type blockRecruiterError {
  message: String!
}

type blockRecruiterPayload {
  blockRecruiterErrors: [blockRecruiterError!]!
  message: String
  status: Int
}

#update password
input updatePasswordInput {
  oldPassword: String!
  newPassword: String!
}

type updatePasswordError {
  message: String!
}

type updatePasswordPayload {
  updatePasswordErrors: [updatePasswordError!]!
  message: String
  status: Int
}

## Plan and Payment

## Create new plan
input CreatePlanInput {
  planName: String!
  planTitle: String!
  planDescription: String!
  slapPrice: Int
  discount: Float
  jobCredits: Int
  databaseCredits: Int
  jobValidity: Int
  planValidity: Int
  gst:Float
  isActive: Boolean
  refundWindow: Int
  planTypeId: ID!
}

type CreatePlanError {
  message: String!
}

type CreatePlanPayload {
  createPlanErrors: [CreatePlanError!]!
  message: String,
  status: Int
}

## Update plan
input UpdatePlanInput {
  planId: ID!
  planName: String
  planTitle: String
  planDescription: String
  slapPrice: Int
  discount: Float
  jobCredits: Int
  databaseCredits: Int
  jobValidity: Int
  planValidity: Int
  gst:Float
  isActive: Boolean
  refundWindow: Int
  planTypeId: ID!
}

type UpdatePlanError {
  message: String!
}

type UpdatePlanPayload {
  updatePlanErrors: [UpdatePlanError!]!
  message: String,
  status: Int
}

## credit request approval status
input ApproveJobCreditRequestInput {
  requestId: ID!
  jobCredit:Int!
  approveStatus:Int!
  rejectReason:String
}

type ApproveJobCreditRequestError {
  message: String!
}

type ApproveJobCreditRequestPayload {
  updateStatusErrors: [ApproveJobCreditRequestError!]!
  message: String,
  status: Int
}

## Get All Plans
type Plan {
  id: ID
  planName: String
  planTitle: String
  planDescription: String
  slapPrice: Int
  discount: Float
  jobCredits: Int
  databaseCredits: Int
  jobValidity: Int
  planValidity: Int
  gst:Float
  isActive: Boolean
  refundWindow: Int
  planTypeId: ID
  createdAt: String
  updatedAt: String
}

type PlansError {
  message: String!
}

type PlansPayload {
  plansErrors: [PlansError!]!
  message: String,
  status: Int
  plans: [Plan]
}

type CreditRequestApprove {
  id: ID
  name: String
  isActive: Boolean
}

type CreditRequestType {
  id: ID
  name: String
  isActive: Boolean
}

type PaymentStatus {
  id: ID
  name: String
  isActive: Boolean
}
type CreditTransactionType{
  id: ID
  name:String
}

type CreditSpendType{
  id: ID
  name:String
}

type RecruiterCreditTransaction {
  id: ID!
  creditTransactionTypeId: Int!
  creditTransactionType: CreditTransactionType!
  creditSpendTypeId: Int!
  creditSpendType: CreditSpendType!
  jobCredit: Int!
  databaseCredit: Int!
  recruiterId: ID!
  recruiter: Recruiter
  creditTransactionId: String!
  planId: ID!
  plan: Plan!
  planPurchaseTransactionId: ID!
  planPurchaseTransaction: RecruiterPlanPurchase
  jobId: ID
  # job: Job
  createdAt: String!
  updatedAt: String!
}

type RecruiterPlanPurchase {
  id: ID
  recruiterId: ID
  recruiter: Recruiters
  internalTransactionId: String
  planId: ID
  plan: Plan
  totalAmountPaid: Int
  actualAmountPaid: Int
  discount: Float
  gst: Float
  paymentTransactionId: ID
  paymentStatus: PaymentStatus
  paymentStatusId: ID
  transactionFailedReason: String
  recruitercredittransaction: [RecruiterCreditTransaction!]
  location: String
  createdAt: String
  updatedAt: String
}

## Get All Recruiter Requested Credit Request
type CreditRequest {
  id: ID
  recruiterId: ID
  recruiter: Recruiters
  crmUserId: ID
  crmUser: CRMUser
  creditRequestApproveStatusId: ID
  creditRequestApproveStatus: CreditRequestApprove
  creditRequestTypeId: ID
  creditRequestType: CreditRequestType
  currentPlanTransactionId: ID
  currentPlanTransaction: RecruiterPlanPurchase
  creditRejectReason: String
  jobCredit: Int
  databaseCredit: Int
  lastCreditRequestDate: String
  createdAt: String
  updatedAt: String
}

type PlansError {
  message: String!
}

type GetCreditRequestsPayload {
  getCreditRequestsErrors: [PlansError!]!
  message: String,
  status: Int
  creditRequests: [CreditRequest]
}

type RecruiterLatestPlanInfo {
  id: ID!
  plan: Plan!
  currentPlanTransaction: RecruiterPlanPurchase!
  currentJobCredits: Int!
  currentDBCredtis: Int!
  previousCarryJobCredits: Int!
  previousCarryDBCredits: Int!
  currentJobPostValidity: String!
  currentPlanValidity: String!
  isPlanActive: Boolean!
  createdAt: String!
  updatedAt: String!
}

type RecruiterCreditHistory {
  id: ID!
  mobileNumber: String!
  name: String
  email: String
  company: CompanyDetails
  recruiterlatestplaninfo: RecruiterLatestPlanInfo
  creditHistory: [CreditRequest!]!
}

type CreditHistoryError {
  message: String!
}

type CreditHistoryPayload {
  creditHistoryErrors: [CreditHistoryError!]!
  message: String,
  status: Int
  recruiterCreditHistory: RecruiterCreditHistory
}

## Role-Based Authentication Types following your pattern ##

type UserInfo {
  id: Int!
  name: String!
  email: String!
  roleId: Int!
  roleName: String!
}

type CRMUserInfo {
  id: Int!
  name: String!
  email: String!
  roleId: Int!
  roleName: String!
}

# Role Assignment Input and Payload following your pattern
input AssignRoleInput {
  userId: Int!
  roleId: Int!
}

type AssignRoleError {
  message: String!
}

type AssignRolePayload {
  AssignRoleErrors: [AssignRoleError!]!
  message: String
  status: Int
}

# Get Current User Payload following your pattern
type GetCurrentUserError {
  message: String!
}

type GetCurrentUserPayload {
  getCurrentUserErrors: [GetCurrentUserError!]!
  message: String
  status: Int
  user: UserInfo
}
`;
