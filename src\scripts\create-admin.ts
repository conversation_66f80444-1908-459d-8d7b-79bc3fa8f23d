import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { createHmac } from "node:crypto";

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    const adminEmail = "<EMAIL>";
    const adminPassword = "admin123";
    const adminName = "System Administrator";

    // Check if admin user already exists
    const existingAdmin = await prisma.crmuser.findUnique({
      where: { email: adminEmail }
    });

    if (existingAdmin) {
      console.log("✅ Admin user already exists!");
      console.log("📧 Email:", adminEmail);
      console.log("🆔 Current Role ID:", existingAdmin.roleId);
      console.log("\n📝 NOTE: Only Admin (ID: 1) and User (ID: 2) roles are supported.");
      return;
    }

    // Create email hash following your pattern
    const emailHash = createHmac(
      "sha256",
      process.env.EMAILHASHSECRET as string
    )
      .update(adminEmail)
      .digest("hex");

    // Create admin user following your pattern
    const adminUser = await prisma.crmuser.create({
      data: {
        name: adminName,
        email: adminEmail,
        password: bcrypt.hashSync(adminPassword, 8),
        roleId: 1, // Admin role
        emailHash,
      },
      include: {
        role: true,
      },
    });

    console.log("✅ Admin user created successfully!");
    console.log("📧 Email:", adminEmail);
    console.log("🔑 Password:", adminPassword);
    console.log("👤 Role:", adminUser.role.name, "(ID: 1)");
    console.log("🆔 User ID:", adminUser.id);
    console.log("\n⚠️  IMPORTANT: Please change the default password after first login!");
    console.log("\n🔐 Login with these credentials and create additional users as needed.");
    console.log("\n📝 NOTE: Only Admin (ID: 1) and User (ID: 2) roles are supported.");

  } catch (error) {
    console.error("❌ Error creating admin user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createAdminUser();
}

export { createAdminUser };
