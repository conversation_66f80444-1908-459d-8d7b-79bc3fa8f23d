import { prisma } from "../db";
import { GraphQLError } from "graphql";
import axios from "axios";
import { getTemplateDetails } from "../templates/whatsapp/whatsAppTemplates";
import { RecDetails } from "../types/commonInterFun.type";

if (!process.env.SINCH_GRANT_TYPE || !process.env.SINCH_CLIENT_ID || !process.env.SINCH_USERNAME || !process.env.SINCH_PASSWORD) {
  throw new Error('Missing required environment variables');
}
const CREDENTIALS = {
  grant_type: process.env.SINCH_GRANT_TYPE as string,
  client_id: process.env.SINCH_CLIENT_ID as string,
  username: process.env.SINCH_USERNAME as string,
  password: process.env.SINCH_PASSWORD as string,
};


let accessToken: string | null = null;
let refreshToken: string | null = null;
let tokenExpiryTime: number | null = null;
let refreshTokenExpiryTime: number | null = null; 

const getAccessToken = async (): Promise<string> => {
  // Check if the access token is valid
  if (accessToken && tokenExpiryTime && tokenExpiryTime > Date.now()) {
    return accessToken; 
  }

  // If the access token expired, use the refresh token to get a new one
  if (refreshToken && refreshTokenExpiryTime && refreshTokenExpiryTime > Date.now()) {
    try {
      return await refreshAccessToken()
    } catch (error:any) {
      if (error.message === 'Refresh token expired') {
        throw new Error('Refresh token expired. Reauthentication required');
      }
      throw error; 
    }
  }

  // If neither the access nor refresh token is valid, request new token
  return await fetchNewAccessToken();
};

// New access token using the refresh token
const refreshAccessToken = async (): Promise<string> => {
  if (!refreshToken) {
    throw new Error('No refresh token available');
  }

  try {
    const tokenResponse = await axios.post(
      `${process.env.WHATSAPP_NOTIFICATION_API_BASEURL}/realms/ipmessaging/protocol/openid-connect/token`,
      new URLSearchParams({
        ...CREDENTIALS,  
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );
    accessToken = tokenResponse.data.access_token;
    tokenExpiryTime = Date.now() + (tokenResponse.data.expires_in * 1000);
    refreshTokenExpiryTime = Date.now() + (tokenResponse.data.refresh_expires_in * 1000);

    if (!accessToken) {
      throw new Error('Failed to refresh access token');
    }

    return accessToken;
  } catch (error:any) {
    console.log("Error refreshing access token:", error);
    throw new Error("Failed to refresh access token");
  }
};

// Fetch a new access token initially
const fetchNewAccessToken = async (): Promise<string> => {
  try {
    if (!CREDENTIALS.grant_type || !CREDENTIALS.client_id || !CREDENTIALS.username || !CREDENTIALS.password) {
      throw new Error('Missing required credentials');
    }
    const tokenResponse = await axios.post(
      `${process.env.WHATSAPP_NOTIFICATION_API_BASEURL}/realms/ipmessaging/protocol/openid-connect/token`,
      new URLSearchParams(CREDENTIALS),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    accessToken = tokenResponse.data.access_token;
    refreshToken = tokenResponse.data.refresh_token;
    tokenExpiryTime = Date.now() + (tokenResponse.data.expires_in * 1000);
    refreshTokenExpiryTime = Date.now() + (tokenResponse.data.refresh_expires_in * 1000);
    if (!accessToken) {
      throw new Error('Failed to retrieve access token');
    }

    return accessToken;
  } catch (error) {
    console.log("Error retrieving access token:", error);
    throw new Error("Failed to retrieve access token");
  }
};

export const sendWhatsAppNotification = async (to: string, recDetails:RecDetails, templateName: string): Promise<any> => {
  try {
    const token = await getAccessToken();
    const templateDetails = await getTemplateDetails(recDetails,templateName)
    const whatsappResponse = await axios.post(
      `${process.env.WHATSAPP_NOTIFICATION_API_SEND}/pull-platform-receiver/v2/wa/messages`,
      {
        recipient_type: 'individual',
        to,
        type: 'template',
        // template: {
        //   name: 'registration_incomplete',
        //   language: {
        //     policy: 'deterministic',
        //     code: 'en',
        //   },
        //   components: [
        //     {
        //       type: 'body',
        //       parameters: [
        //         {
        //           type: 'text',
        //           text: 'Hi',
        //         },
        //       ],
        //     },
        //   ],
        // },
        template: templateDetails,
        metadata: {
          messageId: 'defaultId',
          transactionId: 'defaultTransactionId',
          callbackDlrUrl: 'http://sinchindia.com',
        },
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      }
    );

    console.log("WhatsApp Notification Response:", whatsappResponse.data);
    return whatsappResponse.data;
  } catch (error:any) {
    console.log("Error sending WhatsApp notification:", error);
    throw new Error("Failed to send WhatsApp notification");
  }
};
