import crypto from "crypto";

function sha1(input: string | Buffer): Buffer {
  return crypto.createHash("sha1").update(input).digest();
}

function password_derive_bytes(
  password: string,
  salt: string,
  iterations: number,
  len: number
): Buffer {
  let key = Buffer.from(password + salt);
  for (let i = 0; i < iterations; i++) {
    key = sha1(key);
  }
  if (key.length < len) {
    const hx = password_derive_bytes(password, salt, iterations - 1, 20);
    for (let counter = 1; key.length < len; ++counter) {
      key = Buffer.concat([
        key,
        sha1(Buffer.concat([Buffer.from(counter.toString()), hx])),
      ]);
    }
  }
  return Buffer.alloc(len, key);
}

async function encode(string: string): Promise<string> {
  if (!process.env.CRYPT_IV) {
    throw new Error("crypto Secret is not set in env");
  }
  if (!process.env.CRYPT_SECRET) {
    throw new Error("crypto Secret is not set in env");
  }

  const iv = Buffer.from(process.env.CRYPT_IV);
  const ivstring = iv.toString("hex");
  const key = password_derive_bytes(process.env.CRYPT_SECRET, "", 100, 32);
  const cipher = crypto.createCipheriv("aes-256-cbc", key, ivstring);
  const part1 = cipher.update(string, "utf8");
  const part2 = cipher.final();
  const encrypted = Buffer.concat([part1, part2]).toString("base64");
  if (!encrypted) {
    console.log("issue in encrypton");
  }
  return encrypted;
}

async function decode(string: string): Promise<string> {
  console.log("=====decode strirng", string)
  if (!process.env.CRYPT_IV) {
    console.log("====iniside process not secret iv", process.env.CRYPT_IV)
    throw new Error("crypto Secret is not set in env");
  }
  if (!process.env.CRYPT_SECRET) {
    console.log("====iniside process not secret", process.env.CRYPT_SECRET)
    throw new Error("crypto Secret is not set in env");
  }

  const iv = Buffer.from(process.env.CRYPT_IV);
  console.log("====iv====", iv)
  const ivstring = iv.toString("hex");
  console.log("=====ivstring===", ivstring)
  const key = password_derive_bytes(process.env.CRYPT_SECRET, "", 100, 32);
  console.log("===key===", key)
  const decipher = crypto.createDecipheriv("aes-256-cbc", key, ivstring);
  console.log("===decipher===", decipher)
  let decrypted = decipher.update(string, "base64", "utf8");
  console.log("===decrypted===", decrypted)
  decrypted += decipher.final();
  console.log("====decrypted====", decrypted)
  return decrypted;
}

export { encode, decode };
