export const crmPermissions = [
  // User Management Permissions
  {
    id: 1,
    name: "user.create",
    description: "Create new CRM users",
    resource: "user",
    action: "create",
    isActive: true,
  },
  {
    id: 2,
    name: "user.read",
    description: "View CRM user details",
    resource: "user",
    action: "read",
    isActive: true,
  },
  {
    id: 3,
    name: "user.update",
    description: "Update CRM user information",
    resource: "user",
    action: "update",
    isActive: true,
  },
  {
    id: 4,
    name: "user.delete",
    description: "Delete CRM users",
    resource: "user",
    action: "delete",
    isActive: true,
  },
  {
    id: 5,
    name: "user.assign_role",
    description: "Assign roles to CRM users",
    resource: "user",
    action: "assign_role",
    isActive: true,
  },

  // Recruiter Management Permissions
  {
    id: 6,
    name: "recruiter.read",
    description: "View recruiter profiles and details",
    resource: "recruiter",
    action: "read",
    isActive: true,
  },
  {
    id: 7,
    name: "recruiter.update",
    description: "Update recruiter information",
    resource: "recruiter",
    action: "update",
    isActive: true,
  },
  {
    id: 8,
    name: "recruiter.approve",
    description: "Approve recruiter verification",
    resource: "recruiter",
    action: "approve",
    isActive: true,
  },
  {
    id: 9,
    name: "recruiter.reject",
    description: "Reject recruiter verification",
    resource: "recruiter",
    action: "reject",
    isActive: true,
  },
  {
    id: 10,
    name: "recruiter.block",
    description: "Block/unblock recruiters",
    resource: "recruiter",
    action: "block",
    isActive: true,
  },

  // Company Management Permissions
  {
    id: 11,
    name: "company.read",
    description: "View company details",
    resource: "company",
    action: "read",
    isActive: true,
  },
  {
    id: 12,
    name: "company.update",
    description: "Update company information",
    resource: "company",
    action: "update",
    isActive: true,
  },
  {
    id: 13,
    name: "company.approve",
    description: "Approve company verification",
    resource: "company",
    action: "approve",
    isActive: true,
  },
  {
    id: 14,
    name: "company.reject",
    description: "Reject company verification",
    resource: "company",
    action: "reject",
    isActive: true,
  },

  // Job Management Permissions
  {
    id: 15,
    name: "job.read",
    description: "View job postings",
    resource: "job",
    action: "read",
    isActive: true,
  },
  {
    id: 16,
    name: "job.update",
    description: "Update job postings",
    resource: "job",
    action: "update",
    isActive: true,
  },
  {
    id: 17,
    name: "job.approve",
    description: "Approve job postings",
    resource: "job",
    action: "approve",
    isActive: true,
  },
  {
    id: 18,
    name: "job.reject",
    description: "Reject job postings",
    resource: "job",
    action: "reject",
    isActive: true,
  },
  {
    id: 19,
    name: "job.delete",
    description: "Delete job postings",
    resource: "job",
    action: "delete",
    isActive: true,
  },

  // Role Management Permissions
  {
    id: 20,
    name: "role.read",
    description: "View roles and permissions",
    resource: "role",
    action: "read",
    isActive: true,
  },
  {
    id: 21,
    name: "role.create",
    description: "Create new roles",
    resource: "role",
    action: "create",
    isActive: true,
  },
  {
    id: 22,
    name: "role.update",
    description: "Update role permissions",
    resource: "role",
    action: "update",
    isActive: true,
  },
  {
    id: 23,
    name: "role.delete",
    description: "Delete roles",
    resource: "role",
    action: "delete",
    isActive: true,
  },

  // System Management Permissions
  {
    id: 24,
    name: "system.admin",
    description: "Full system administration access",
    resource: "system",
    action: "admin",
    isActive: true,
  },
  {
    id: 25,
    name: "reports.read",
    description: "View system reports and analytics",
    resource: "reports",
    action: "read",
    isActive: true,
  },
  {
    id: 26,
    name: "notifications.send",
    description: "Send notifications to users",
    resource: "notifications",
    action: "send",
    isActive: true,
  },

  // Request Callback Permissions
  {
    id: 27,
    name: "callback.read",
    description: "View callback requests",
    resource: "callback",
    action: "read",
    isActive: true,
  },
  {
    id: 28,
    name: "callback.update",
    description: "Update callback request status",
    resource: "callback",
    action: "update",
    isActive: true,
  },
];
