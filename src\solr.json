{"add-field": [{"name": "jobId", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "companyName", "type": "string", "stored": "true"}, {"name": "recruiterId", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "companyId", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "companyTypeId", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "companyListId", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "consultantId", "indexed": "true", "type": "pint", "stored": "true"}, {"name": "jobStatusId", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "JobDetail.jobTitle", "type": "string", "stored": "true"}, {"name": "JobDetail.companyName", "type": "string", "stored": "true"}, {"name": "JobDetail.jobCategoryId", "type": "pint", "stored": "true"}, {"name": "JobDetail.jobLocation", "type": "string", "stored": "true"}, {"name": "JobDetail.buildingName", "type": "string", "stored": "true"}, {"name": "JobDetail.location", "type": "location", "stored": "true"}, {"name": "JobDetail.latitude", "type": "string", "stored": "true"}, {"name": "JobDetail.longitude", "type": "string", "stored": "true"}, {"name": "JobDetail.numberOpenings", "type": "pint", "stored": "true"}, {"name": "JobDetail.workTypeId", "type": "pint", "stored": "true"}, {"name": "JobDetail.jobUrgencyId", "type": "pint", "stored": "true"}, {"name": "JobDetail.jobDescription", "type": "string", "stored": "true"}, {"name": "JobDetail.minMonthSalary", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "JobDetail.maxMonthSalary", "indexed": "true", "type": "pint", "stored": "true", "required": "true"}, {"name": "JobDetail.isIncentives", "type": "boolean", "stored": "true"}, {"name": "JobDetail.workingDaysId", "type": "pint", "stored": "true"}, {"name": "JobDetail.feesDeposit", "type": "boolean", "stored": "true"}, {"name": "JobDetail.avgMonthlyIncentive", "type": "pint", "stored": "true"}, {"name": "JobDetail.depositAmount", "type": "pint", "stored": "true"}, {"name": "JobDetail.depositReasonId", "type": "pint", "stored": "true"}, {"name": "JobDetail.additionalInformation", "type": "string", "stored": "true"}, {"name": "JobDetail.createdAt", "type": "pdate", "stored": "true"}, {"name": "JobDetail.updatedAt", "type": "pdate", "stored": "true"}, {"name": "JobDetail.companyId", "type": "pint", "stored": "true"}, {"name": "JobDetail.shiftTypeId", "type": "pint", "stored": "true"}, {"name": "JobDetail.employmentTypeId", "type": "pint", "stored": "true"}, {"name": "candidatePreference.genderId", "type": "pint", "stored": "true"}, {"name": "candidatePreference.educationId", "type": "pint", "stored": "true"}, {"name": "candidatePreference.workExperienceId", "type": "pint", "stored": "true"}, {"name": "candidatePreference.minExperience", "type": "pint", "stored": "true"}, {"name": "candidatePreference.maxExperience", "type": "pint", "stored": "true"}, {"name": "candidatePreference.applicationRadiusId", "type": "pint", "stored": "true"}, {"name": "candidatePreference.englishKnowledgeId", "type": "pint", "stored": "true"}, {"name": "candidatePreference.isAssetsRequired", "type": "boolean", "stored": "true"}, {"name": "candidatePreference.createdAt", "type": "pdate", "stored": "true"}, {"name": "candidatePreference.updatedAt", "type": "pdate", "stored": "true"}, {"name": "interviewPreference.hrName", "type": "string", "stored": "true", "required": "true"}, {"name": "interviewPreference.hrContactNumber", "type": "string", "stored": "true", "required": "true"}, {"name": "interviewPreference.interviewTypeId", "type": "pint", "stored": "true", "required": "true"}, {"name": "interviewPreference.createdAt", "type": "pdate", "stored": "true"}, {"name": "interviewPreference.updatedAt", "type": "pdate", "stored": "true"}]}