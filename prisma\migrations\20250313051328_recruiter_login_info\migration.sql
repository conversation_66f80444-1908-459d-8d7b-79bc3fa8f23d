-- CreateTable
CREATE TABLE `recruiterlogininfo` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `loginDevice` VARCHAR(100) NULL DEFAULT '',
    `devicePlatform` VARCHAR(100) NULL DEFAULT '',
    `deviceId` VARCHAR(100) NULL DEFAULT '',
    `loginDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `logintypeId` INTEGER NOT NULL,
    `logoutDate` DATETIME(3) NULL,
    `logouttypeId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE `recruiterlogininfo` ADD CONSTRAINT `recruiterlogininfo_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterlogininfo` ADD CONSTRAINT `recruiterlogininfo_logintypeId_fkey` FOREIGN KEY (`logintypeId`) REFERENCES `logintype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterlogininfo` ADD CONSTRAINT `recruiterlogininfo_logouttypeId_fkey` FOREIGN KEY (`logouttypeId`) REFERENCES `logouttype`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
