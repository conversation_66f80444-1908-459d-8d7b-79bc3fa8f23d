-- CreateTable
CREATE TABLE `user` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `mobileNumber` VARCHAR(70) NOT NULL,
    `profilePicture` VARCHAR(150) NULL DEFAULT '',
    `name` VARCHAR(256) NULL DEFAULT '',
    `dateOfBirth` DATE NULL,
    `whatsAppUpdate` BOOLEAN NOT NULL DEFAULT false,
    `gender` CHAR(10) NULL DEFAULT '',
    `email` VARCHAR(255) NULL,
    `twoWheelerLicense` BOOLEAN NOT NULL DEFAULT false,
    `latitude` VARCHAR(191) NULL DEFAULT '',
    `longitude` VARCHAR(191) NULL DEFAULT '',
    `location` POINT SRID 4326 NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `languageId` INTEGER NULL,
    `userStatusId` INTEGER NULL,
    `jobzAppStatusId` INTEGER NULL,
    `deleteFeedbackId` INTEGER NULL,
    `UserRating` INTEGER NULL DEFAULT 0,
    `pauseUntil` DATETIME NULL,
    `mobileNumberHash` VARCHAR(65) NOT NULL DEFAULT '',

    UNIQUE INDEX `user_mobileNumber_key`(`mobileNumber`),
    UNIQUE INDEX `user_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(10) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobzAppStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(10) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DeleteFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AppLanguage` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NULL DEFAULT '',
    `code` CHAR(6) NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `OTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobzOTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserEducation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `qualificationId` INTEGER NOT NULL,
    `courseId` INTEGER NOT NULL,
    `specializationId` INTEGER NOT NULL,
    `higherSecondaryPercentage` DECIMAL(5, 2) NULL DEFAULT 0,
    `graduatePercentage` DECIMAL(5, 2) NULL DEFAULT 0,
    `collegeName` VARCHAR(255) NULL DEFAULT '',
    `graduationYear` INTEGER NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `degreeModeId` INTEGER NULL,
    `collegesId` INTEGER NULL,

    UNIQUE INDEX `UserEducation_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QualificationType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Course` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `name_en` VARCHAR(100) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(100) NOT NULL DEFAULT '',
    `qualificationId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Specialization` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(150) NOT NULL DEFAULT '',
    `name_en` VARCHAR(150) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(150) NOT NULL DEFAULT '',
    `courseId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DegreeMode` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Colleges` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserProfessional` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `workExperience` VARCHAR(191) NULL,
    `experienceYear` INTEGER NULL DEFAULT 0,
    `experienceMonth` INTEGER NULL DEFAULT 0,
    `monthlySalary` INTEGER NULL DEFAULT 0,
    `joinPreferenceId` INTEGER NULL,
    `currentRoleCategoryId` INTEGER NULL,
    `currentJobRoleId` INTEGER NULL,
    `previousJobTitle` VARCHAR(50) NULL DEFAULT '',
    `companyName` VARCHAR(50) NULL DEFAULT '',
    `englishKnowledgeId` INTEGER NULL,
    `resume` VARCHAR(255) NULL DEFAULT '',
    `professionalSummary` VARCHAR(255) NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserProfessional_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EnglishKnowledgeLevel` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `MonthlySalaryRange` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LanguageMapping` (
    `userLanguageId` INTEGER NOT NULL,
    `profileLanguageId` INTEGER NOT NULL,

    PRIMARY KEY (`userLanguageId`, `profileLanguageId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProfileLanguages` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `name_en` VARCHAR(20) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SkillsMapping` (
    `userProfessionalId` INTEGER NOT NULL,
    `skillId` INTEGER NOT NULL,

    PRIMARY KEY (`userProfessionalId`, `skillId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SkillList` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `name_en` VARCHAR(100) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDomainList` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDomainMapping` (
    `userProfessionalId` INTEGER NOT NULL,
    `WorkingDomainId` INTEGER NOT NULL,

    PRIMARY KEY (`userProfessionalId`, `WorkingDomainId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserJobPreferences` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `prefRoleCategoryId` INTEGER NULL,
    `prefJobRoleId` INTEGER NULL,
    `city` VARCHAR(70) NULL DEFAULT '',
    `area` VARCHAR(70) NULL DEFAULT '',
    `distanceId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserJobPreferences_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Distance` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobRoleCategory` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobRole` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `jobRoleCategoryId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CommuteModeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `commuteModeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `commuteModeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CommuteMode` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EmploymentTypeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `employmentTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `employmentTypeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EmploymentType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkTypeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `workTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `workTypeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShiftTypeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `shiftTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `shiftTypeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShiftType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDaysMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `workingDaysId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `workingDaysId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDays` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JoinPreference` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL DEFAULT '',
    `name_en` VARCHAR(30) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(30) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SuggestionFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserReviewFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `reviewId` INTEGER NULL,
    `suggestionId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserFeedback_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ReportJob` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(70) NOT NULL DEFAULT '',
    `name_en` VARCHAR(70) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(70) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserReportJob` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `reportJobId` INTEGER NULL,

    UNIQUE INDEX `UserReportJob_userId_key`(`userId`),
    UNIQUE INDEX `UserReportJob_jobId_key`(`jobId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserJobMapping` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `shortlisted` BOOLEAN NOT NULL DEFAULT false,
    `profileViewed` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`id`, `userId`, `jobId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserJobViewMapping` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `shortlisted` BOOLEAN NOT NULL DEFAULT false,
    `profileViewed` BOOLEAN NOT NULL DEFAULT false,

    PRIMARY KEY (`id`, `userId`, `jobId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Recruiter` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `mobileNumber` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NULL,
    `email` VARCHAR(255) NULL,
    `hiringFor` ENUM('Recruiter', 'Consultant') NULL,
    `recruiterRoleId` INTEGER NULL DEFAULT 1,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterStatus` BOOLEAN NOT NULL DEFAULT false,

    UNIQUE INDEX `Recruiter_mobileNumber_key`(`mobileNumber`),
    UNIQUE INDEX `Recruiter_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterRole` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Company` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `industryId` INTEGER NULL,
    `numberOfEmployeesId` INTEGER NULL,
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `addressDetails` VARCHAR(255) NOT NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterId` INTEGER NOT NULL,

    UNIQUE INDEX `Company_recruiterId_key`(`recruiterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Industry` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `NumberOfEmployees` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterJobPostDetail` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `companyName` VARCHAR(100) NOT NULL DEFAULT '',
    `jobTitle` VARCHAR(255) NOT NULL DEFAULT '',
    `jobCategoryId` INTEGER NULL,
    `jobLocation` VARCHAR(255) NOT NULL DEFAULT '',
    `fullAddress` VARCHAR(255) NOT NULL DEFAULT '',
    `latitude` VARCHAR(191) NULL DEFAULT '',
    `longitude` VARCHAR(191) NULL DEFAULT '',
    `numberOpenings` INTEGER NOT NULL DEFAULT 1,
    `workTypeId` INTEGER NULL,
    `jobUrgencyId` INTEGER NULL,
    `jobDescription` TEXT NOT NULL,
    `minMonthSalary` INTEGER NOT NULL DEFAULT 0,
    `maxMonthSalary` INTEGER NULL DEFAULT 0,
    `isIncentives` BOOLEAN NOT NULL DEFAULT false,
    `avgMonthlyIncentive` INTEGER NULL DEFAULT 0,
    `workingDaysId` INTEGER NULL,
    `feesDeposit` BOOLEAN NOT NULL DEFAULT false,
    `depositAmount` INTEGER NULL,
    `depositReasonId` INTEGER NULL,
    `additionalInformation` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `companyId` INTEGER NOT NULL,
    `shiftTypeId` INTEGER NULL,
    `employmentTypeId` INTEGER NULL,
    `isJobApplied` BOOLEAN NOT NULL DEFAULT false,
    `isJobViewed` BOOLEAN NOT NULL DEFAULT false,
    `jobAppliedDate` DATETIME(3) NULL,
    `jobViewedDate` DATETIME(3) NULL,

    FULLTEXT INDEX `RecruiterJobPostDetail_jobTitle_companyName_jobDescription_j_idx`(`jobTitle`, `companyName`, `jobDescription`, `jobLocation`, `fullAddress`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobCategory` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(55) NOT NULL DEFAULT '',
    `name_en` VARCHAR(55) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(55) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobBenefits` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DepositReason` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobBenefitsMapping` (
    `recruiterJobPostDetailId` INTEGER NOT NULL,
    `jobBenefitsId` INTEGER NOT NULL,

    PRIMARY KEY (`jobBenefitsId`, `recruiterJobPostDetailId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterCandidatePreference` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `genderId` INTEGER NULL,
    `educationId` INTEGER NULL,
    `workExperienceId` INTEGER NULL,
    `minExperience` INTEGER NULL DEFAULT 0,
    `maxExperience` INTEGER NULL DEFAULT 0,
    `applicationRadiusId` INTEGER NULL,
    `englishKnowledgeId` INTEGER NULL,
    `isAssetsRequired` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `jobDetailId` INTEGER NULL,

    UNIQUE INDEX `RecruiterCandidatePreference_jobDetailId_key`(`jobDetailId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Gender` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `name_en` VARCHAR(20) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkExperience` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(25) NOT NULL DEFAULT '',
    `name_en` VARCHAR(25) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(25) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterSkillsMapping` (
    `recruiterCandidatePreferenceId` INTEGER NOT NULL,
    `skillId` INTEGER NOT NULL,

    PRIMARY KEY (`recruiterCandidatePreferenceId`, `skillId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RequiredAssets` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(55) NOT NULL DEFAULT '',
    `name_en` VARCHAR(55) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(55) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RequiredAssetsMapping` (
    `requiredAssetsId` INTEGER NOT NULL,
    `recruiterCandidatePreferenceId` INTEGER NOT NULL,

    PRIMARY KEY (`recruiterCandidatePreferenceId`, `requiredAssetsId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Interview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `hrName` VARCHAR(100) NOT NULL DEFAULT '',
    `hrContactNumber` VARCHAR(15) NOT NULL DEFAULT '',
    `allowCandidateCall` BOOLEAN NOT NULL DEFAULT false,
    `whatsAppUpdate` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `jobDetailId` INTEGER NULL,
    `interviewTypeId` INTEGER NOT NULL,

    UNIQUE INDEX `Interview_jobDetailId_key`(`jobDetailId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `InterviewType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(55) NOT NULL DEFAULT '',
    `name_en` VARCHAR(55) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(55) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `FFInterview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `fullAddress` VARCHAR(255) NOT NULL DEFAULT '',
    `interviewId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `FFInterview_interviewId_key`(`interviewId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WalkinInterview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `walkinStartDate` DATE NOT NULL,
    `duration` INTEGER NOT NULL DEFAULT 0,
    `startTime` VARCHAR(50) NOT NULL DEFAULT '',
    `endTime` VARCHAR(50) NOT NULL DEFAULT '',
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `fullAddress` VARCHAR(255) NOT NULL DEFAULT '',
    `interviewId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `WalkinInterview_interviewId_key`(`interviewId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_languageId_fkey` FOREIGN KEY (`languageId`) REFERENCES `AppLanguage`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_userStatusId_fkey` FOREIGN KEY (`userStatusId`) REFERENCES `UserStatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_jobzAppStatusId_fkey` FOREIGN KEY (`jobzAppStatusId`) REFERENCES `JobzAppStatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_deleteFeedbackId_fkey` FOREIGN KEY (`deleteFeedbackId`) REFERENCES `DeleteFeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_qualificationId_fkey` FOREIGN KEY (`qualificationId`) REFERENCES `QualificationType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_courseId_fkey` FOREIGN KEY (`courseId`) REFERENCES `Course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_specializationId_fkey` FOREIGN KEY (`specializationId`) REFERENCES `Specialization`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_degreeModeId_fkey` FOREIGN KEY (`degreeModeId`) REFERENCES `DegreeMode`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_collegesId_fkey` FOREIGN KEY (`collegesId`) REFERENCES `Colleges`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Course` ADD CONSTRAINT `Course_qualificationId_fkey` FOREIGN KEY (`qualificationId`) REFERENCES `QualificationType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Specialization` ADD CONSTRAINT `Specialization_courseId_fkey` FOREIGN KEY (`courseId`) REFERENCES `Course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_joinPreferenceId_fkey` FOREIGN KEY (`joinPreferenceId`) REFERENCES `JoinPreference`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_currentRoleCategoryId_fkey` FOREIGN KEY (`currentRoleCategoryId`) REFERENCES `JobRoleCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_currentJobRoleId_fkey` FOREIGN KEY (`currentJobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_englishKnowledgeId_fkey` FOREIGN KEY (`englishKnowledgeId`) REFERENCES `EnglishKnowledgeLevel`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LanguageMapping` ADD CONSTRAINT `LanguageMapping_userLanguageId_fkey` FOREIGN KEY (`userLanguageId`) REFERENCES `UserProfessional`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LanguageMapping` ADD CONSTRAINT `LanguageMapping_profileLanguageId_fkey` FOREIGN KEY (`profileLanguageId`) REFERENCES `ProfileLanguages`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SkillsMapping` ADD CONSTRAINT `SkillsMapping_userProfessionalId_fkey` FOREIGN KEY (`userProfessionalId`) REFERENCES `UserProfessional`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SkillsMapping` ADD CONSTRAINT `SkillsMapping_skillId_fkey` FOREIGN KEY (`skillId`) REFERENCES `SkillList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDomainMapping` ADD CONSTRAINT `WorkingDomainMapping_userProfessionalId_fkey` FOREIGN KEY (`userProfessionalId`) REFERENCES `UserProfessional`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDomainMapping` ADD CONSTRAINT `WorkingDomainMapping_WorkingDomainId_fkey` FOREIGN KEY (`WorkingDomainId`) REFERENCES `WorkingDomainList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_prefRoleCategoryId_fkey` FOREIGN KEY (`prefRoleCategoryId`) REFERENCES `JobRoleCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_prefJobRoleId_fkey` FOREIGN KEY (`prefJobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_distanceId_fkey` FOREIGN KEY (`distanceId`) REFERENCES `Distance`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `JobRole` ADD CONSTRAINT `JobRole_jobRoleCategoryId_fkey` FOREIGN KEY (`jobRoleCategoryId`) REFERENCES `JobRoleCategory`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CommuteModeMapping` ADD CONSTRAINT `CommuteModeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CommuteModeMapping` ADD CONSTRAINT `CommuteModeMapping_commuteModeId_fkey` FOREIGN KEY (`commuteModeId`) REFERENCES `CommuteMode`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EmploymentTypeMapping` ADD CONSTRAINT `EmploymentTypeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EmploymentTypeMapping` ADD CONSTRAINT `EmploymentTypeMapping_employmentTypeId_fkey` FOREIGN KEY (`employmentTypeId`) REFERENCES `EmploymentType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkTypeMapping` ADD CONSTRAINT `WorkTypeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkTypeMapping` ADD CONSTRAINT `WorkTypeMapping_workTypeId_fkey` FOREIGN KEY (`workTypeId`) REFERENCES `WorkType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShiftTypeMapping` ADD CONSTRAINT `ShiftTypeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShiftTypeMapping` ADD CONSTRAINT `ShiftTypeMapping_shiftTypeId_fkey` FOREIGN KEY (`shiftTypeId`) REFERENCES `ShiftType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDaysMapping` ADD CONSTRAINT `WorkingDaysMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDaysMapping` ADD CONSTRAINT `WorkingDaysMapping_workingDaysId_fkey` FOREIGN KEY (`workingDaysId`) REFERENCES `WorkingDays`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFeedback` ADD CONSTRAINT `UserFeedback_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFeedback` ADD CONSTRAINT `UserFeedback_reviewId_fkey` FOREIGN KEY (`reviewId`) REFERENCES `UserReviewFeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFeedback` ADD CONSTRAINT `UserFeedback_suggestionId_fkey` FOREIGN KEY (`suggestionId`) REFERENCES `SuggestionFeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserReportJob` ADD CONSTRAINT `UserReportJob_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserReportJob` ADD CONSTRAINT `UserReportJob_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `RecruiterJobPostDetail`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserReportJob` ADD CONSTRAINT `UserReportJob_reportJobId_fkey` FOREIGN KEY (`reportJobId`) REFERENCES `ReportJob`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobMapping` ADD CONSTRAINT `UserJobMapping_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobMapping` ADD CONSTRAINT `UserJobMapping_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `RecruiterJobPostDetail`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobViewMapping` ADD CONSTRAINT `UserJobViewMapping_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobViewMapping` ADD CONSTRAINT `UserJobViewMapping_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `RecruiterJobPostDetail`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Recruiter` ADD CONSTRAINT `Recruiter_recruiterRoleId_fkey` FOREIGN KEY (`recruiterRoleId`) REFERENCES `RecruiterRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Company` ADD CONSTRAINT `Company_industryId_fkey` FOREIGN KEY (`industryId`) REFERENCES `Industry`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Company` ADD CONSTRAINT `Company_numberOfEmployeesId_fkey` FOREIGN KEY (`numberOfEmployeesId`) REFERENCES `NumberOfEmployees`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Company` ADD CONSTRAINT `Company_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `Recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_jobCategoryId_fkey` FOREIGN KEY (`jobCategoryId`) REFERENCES `JobCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_workTypeId_fkey` FOREIGN KEY (`workTypeId`) REFERENCES `WorkType`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_jobUrgencyId_fkey` FOREIGN KEY (`jobUrgencyId`) REFERENCES `JoinPreference`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_workingDaysId_fkey` FOREIGN KEY (`workingDaysId`) REFERENCES `WorkingDays`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_depositReasonId_fkey` FOREIGN KEY (`depositReasonId`) REFERENCES `DepositReason`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_companyId_fkey` FOREIGN KEY (`companyId`) REFERENCES `Company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_shiftTypeId_fkey` FOREIGN KEY (`shiftTypeId`) REFERENCES `ShiftType`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterJobPostDetail` ADD CONSTRAINT `RecruiterJobPostDetail_employmentTypeId_fkey` FOREIGN KEY (`employmentTypeId`) REFERENCES `EmploymentType`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `JobBenefitsMapping` ADD CONSTRAINT `JobBenefitsMapping_recruiterJobPostDetailId_fkey` FOREIGN KEY (`recruiterJobPostDetailId`) REFERENCES `RecruiterJobPostDetail`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `JobBenefitsMapping` ADD CONSTRAINT `JobBenefitsMapping_jobBenefitsId_fkey` FOREIGN KEY (`jobBenefitsId`) REFERENCES `JobBenefits`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterCandidatePreference` ADD CONSTRAINT `RecruiterCandidatePreference_genderId_fkey` FOREIGN KEY (`genderId`) REFERENCES `Gender`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterCandidatePreference` ADD CONSTRAINT `RecruiterCandidatePreference_educationId_fkey` FOREIGN KEY (`educationId`) REFERENCES `QualificationType`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterCandidatePreference` ADD CONSTRAINT `RecruiterCandidatePreference_workExperienceId_fkey` FOREIGN KEY (`workExperienceId`) REFERENCES `WorkExperience`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterCandidatePreference` ADD CONSTRAINT `RecruiterCandidatePreference_applicationRadiusId_fkey` FOREIGN KEY (`applicationRadiusId`) REFERENCES `Distance`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterCandidatePreference` ADD CONSTRAINT `RecruiterCandidatePreference_englishKnowledgeId_fkey` FOREIGN KEY (`englishKnowledgeId`) REFERENCES `EnglishKnowledgeLevel`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterCandidatePreference` ADD CONSTRAINT `RecruiterCandidatePreference_jobDetailId_fkey` FOREIGN KEY (`jobDetailId`) REFERENCES `RecruiterJobPostDetail`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterSkillsMapping` ADD CONSTRAINT `RecruiterSkillsMapping_recruiterCandidatePreferenceId_fkey` FOREIGN KEY (`recruiterCandidatePreferenceId`) REFERENCES `RecruiterCandidatePreference`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterSkillsMapping` ADD CONSTRAINT `RecruiterSkillsMapping_skillId_fkey` FOREIGN KEY (`skillId`) REFERENCES `SkillList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RequiredAssetsMapping` ADD CONSTRAINT `RequiredAssetsMapping_requiredAssetsId_fkey` FOREIGN KEY (`requiredAssetsId`) REFERENCES `RequiredAssets`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RequiredAssetsMapping` ADD CONSTRAINT `RequiredAssetsMapping_recruiterCandidatePreferenceId_fkey` FOREIGN KEY (`recruiterCandidatePreferenceId`) REFERENCES `RecruiterCandidatePreference`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Interview` ADD CONSTRAINT `Interview_jobDetailId_fkey` FOREIGN KEY (`jobDetailId`) REFERENCES `RecruiterJobPostDetail`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Interview` ADD CONSTRAINT `Interview_interviewTypeId_fkey` FOREIGN KEY (`interviewTypeId`) REFERENCES `InterviewType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `FFInterview` ADD CONSTRAINT `FFInterview_interviewId_fkey` FOREIGN KEY (`interviewId`) REFERENCES `Interview`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WalkinInterview` ADD CONSTRAINT `WalkinInterview_interviewId_fkey` FOREIGN KEY (`interviewId`) REFERENCES `Interview`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
