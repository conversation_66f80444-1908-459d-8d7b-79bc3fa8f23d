module.exports = {
  apps: [
    {
      name: "projectjj-backend",
      script: "build/index.js",
      // instances: "max",
      // exec_mode: "cluster",
      // Logging
      out_file: "./out.log",
      error_file: "./error.log",
      merge_logs: true,
      log_date_format: "DD-MM HH:mm:ss Z",
      log_type: "json",

      env_sprint: {
        NODE_ENV: "sprint",
        PORT: 5000,
      },
    },
  ],
  deploy: {
    sprint: {
      user: "jjnode",
      host: "**************",
      ref: "sprint",
      repo: "************************:jj/projectjj-backend.git",
      path: "/home/<USER>",
      ssh_options: "StrictHostKeyChecking=no",
      "post-deploy":
        "git pull && npm --prefix /home/<USER>",
    },
  },
};
