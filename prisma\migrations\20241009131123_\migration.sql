-- CreateTable
CREATE TABLE `user` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `mobileNumber` VARCHAR(70) NOT NULL,
    `profilePicture` VARCHAR(150) NULL DEFAULT '',
    `name` VARCHAR(256) NULL DEFAULT '',
    `dateOfBirth` DATE NULL,
    `whatsAppUpdate` BOOLEAN NOT NULL DEFAULT false,
    `gender` CHAR(10) NULL DEFAULT '',
    `email` VARCHAR(255) NULL,
    `twoWheelerLicense` BOOLEAN NOT NULL DEFAULT false,
    `coachMarkStatus` BOOLEAN NOT NULL DEFAULT false,
    `latitude` VARCHAR(191) NULL DEFAULT '',
    `longitude` VARCHAR(191) NULL DEFAULT '',
    `location` POINT SRID 4326 NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `languageId` INTEGER NULL,
    `userStatusId` INTEGER NULL,
    `jobzAppStatusId` INTEGER NULL,
    `deleteFeedbackId` INTEGER NULL,
    `deleteFeedbackOtherReason` VARCHAR(255) NULL DEFAULT '',
    `UserRating` INTEGER NULL DEFAULT 0,
    `mobileNumberHash` VARCHAR(65) NOT NULL DEFAULT '',

    UNIQUE INDEX `user_mobileNumber_key`(`mobileNumber`),
    UNIQUE INDEX `user_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(10) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobzAppStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(10) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `usernotification` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(30) NOT NULL DEFAULT '',
    `description` VARCHAR(150) NOT NULL DEFAULT '',
    `isRead` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `userId` INTEGER NOT NULL,
    `usernotificationtypeId` INTEGER NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `fcmdevicetoken` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `device` VARCHAR(100) NOT NULL DEFAULT '',
    `token` VARCHAR(255) NOT NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `userId` INTEGER NOT NULL,

    UNIQUE INDEX `fcmdevicetoken_device_token_userId_key`(`device`, `token`, `userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `usernotificationtype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DeleteFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AppLanguage` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `code` CHAR(6) NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `OTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobzOTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserEducation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `qualificationId` INTEGER NOT NULL,
    `courseId` INTEGER NOT NULL,
    `specializationId` INTEGER NOT NULL,
    `higherSecondaryPercentage` DECIMAL(5, 2) NULL DEFAULT 0,
    `graduatePercentage` DECIMAL(5, 2) NULL DEFAULT 0,
    `collegeName` VARCHAR(255) NULL DEFAULT '',
    `graduationYear` INTEGER NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `degreeModeId` INTEGER NULL,
    `collegesId` INTEGER NULL,

    UNIQUE INDEX `UserEducation_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `QualificationType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Course` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `name_en` VARCHAR(100) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(100) NOT NULL DEFAULT '',
    `qualificationId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Specialization` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(150) NOT NULL DEFAULT '',
    `name_en` VARCHAR(150) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(150) NOT NULL DEFAULT '',
    `courseId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DegreeMode` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Colleges` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserProfessional` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `workExperience` VARCHAR(191) NULL,
    `experienceYear` INTEGER NULL DEFAULT 0,
    `experienceMonth` INTEGER NULL DEFAULT 0,
    `monthlySalary` INTEGER NULL DEFAULT 0,
    `joinPreferenceId` INTEGER NULL,
    `currentRoleCategoryId` INTEGER NULL,
    `currentJobRoleId` INTEGER NULL,
    `previousJobTitle` VARCHAR(50) NULL DEFAULT '',
    `companyName` VARCHAR(50) NULL DEFAULT '',
    `englishKnowledgeId` INTEGER NULL,
    `resume` VARCHAR(255) NULL DEFAULT '',
    `professionalSummary` VARCHAR(800) NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserProfessional_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EnglishKnowledgeLevel` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `monthlysalaryrange` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `minSalary` INTEGER NOT NULL DEFAULT 0,
    `maxSalary` INTEGER NOT NULL DEFAULT 0,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `jobtitle` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `jobRoleId` INTEGER NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LanguageMapping` (
    `userLanguageId` INTEGER NOT NULL,
    `profileLanguageId` INTEGER NOT NULL,

    PRIMARY KEY (`userLanguageId`, `profileLanguageId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ProfileLanguages` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `name_en` VARCHAR(20) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SkillsMapping` (
    `userProfessionalId` INTEGER NOT NULL,
    `skillId` INTEGER NOT NULL,

    PRIMARY KEY (`userProfessionalId`, `skillId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `skilllist` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `name_en` VARCHAR(100) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(100) NOT NULL DEFAULT '',
    `jobRoleId` INTEGER NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDomainList` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDomainMapping` (
    `userProfessionalId` INTEGER NOT NULL,
    `WorkingDomainId` INTEGER NOT NULL,

    PRIMARY KEY (`userProfessionalId`, `WorkingDomainId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserJobPreferences` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `prefRoleCategoryId` INTEGER NULL,
    `prefJobRoleId` INTEGER NULL,
    `city` VARCHAR(70) NULL DEFAULT '',
    `area` VARCHAR(70) NULL DEFAULT '',
    `distanceId` INTEGER NULL,
    `salaryRangeId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserJobPreferences_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Distance` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobRoleCategory` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobRole` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `jobRoleCategoryId` INTEGER NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CommuteModeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `commuteModeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `commuteModeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CommuteMode` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EmploymentTypeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `employmentTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `employmentTypeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `EmploymentType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkTypeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `workTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `workTypeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShiftTypeMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `shiftTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `shiftTypeId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ShiftType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(40) NOT NULL DEFAULT '',
    `name_en` VARCHAR(40) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(40) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDaysMapping` (
    `userJobPreferenceId` INTEGER NOT NULL,
    `workingDaysId` INTEGER NOT NULL,

    PRIMARY KEY (`userJobPreferenceId`, `workingDaysId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkingDays` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JoinPreference` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL DEFAULT '',
    `name_en` VARCHAR(30) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(30) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SuggestionFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserReviewFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(50) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(50) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `UserFeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `reviewId` INTEGER NULL,
    `suggestionId` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `UserFeedback_userId_key`(`userId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ReportJob` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(70) NOT NULL DEFAULT '',
    `name_en` VARCHAR(70) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(70) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `userreportjob` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `reportJobId` INTEGER NULL,
    `otherReportReason` VARCHAR(255) NULL,
    `isJobReported` BOOLEAN NOT NULL DEFAULT false,

    UNIQUE INDEX `userreportjob_userId_jobId_key`(`userId`, `jobId`),
    UNIQUE INDEX `userreportjob_userId_jobId_reportJobId_key`(`userId`, `jobId`, `reportJobId`),
    PRIMARY KEY (`id`, `jobId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `userjobmapping` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `candidateApplied` BOOLEAN NOT NULL DEFAULT false,
    `jobAppliedDate` DATETIME(3) NULL DEFAULT CURRENT_TIMESTAMP(3),
    `jobFeedbackId` INTEGER NULL,
    `isJobFeedBack` BOOLEAN NULL DEFAULT false,
    `jobFeedbackDate` DATETIME(3) NULL,
    `otherFeedbackReason` VARCHAR(255) NULL,

    UNIQUE INDEX `userjobmapping_userId_jobId_key`(`userId`, `jobId`),
    UNIQUE INDEX `userjobmapping_userId_jobId_jobFeedbackId_key`(`userId`, `jobId`, `jobFeedbackId`),
    PRIMARY KEY (`id`, `userId`, `jobId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `jobfeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(70) NOT NULL DEFAULT '',
    `name_en` VARCHAR(70) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(70) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiteruserdetails` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `shortlisted` BOOLEAN NOT NULL DEFAULT false,
    `shortlistedDate` DATETIME(3) NULL,
    `profileViewed` BOOLEAN NOT NULL DEFAULT false,
    `profileViewedDate` DATETIME(3) NULL,
    `candidateProfileStatusId` INTEGER NULL,
    `recruiterReportCandidateId` INTEGER NULL,
    `otherReportReason` VARCHAR(255) NULL,

    UNIQUE INDEX `recruiteruserdetails_userId_recruiterId_jobId_key`(`userId`, `recruiterId`, `jobId`),
    PRIMARY KEY (`id`, `userId`, `jobId`, `recruiterId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterreportcandidate` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(70) NOT NULL DEFAULT '',
    `name_en` VARCHAR(70) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(70) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `candidateprofilestatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `userjobviewmapping` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `userId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `candidateViewed` BOOLEAN NOT NULL DEFAULT false,
    `jobViewedDate` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `userjobviewmapping_userId_jobId_key`(`userId`, `jobId`),
    PRIMARY KEY (`id`, `userId`, `jobId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiter` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `mobileNumber` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `email` VARCHAR(255) NULL,
    `isEmailVerified` BOOLEAN NOT NULL DEFAULT false,
    `isPersonalVerified` BOOLEAN NOT NULL DEFAULT false,
    `isCompanyVerified` BOOLEAN NOT NULL DEFAULT false,
    `whatsAppUpdate` BOOLEAN NOT NULL DEFAULT false,
    `isRecruiterHoldEmail` BOOLEAN NOT NULL DEFAULT false,
    `isRecruiterSubmitEmail` BOOLEAN NOT NULL DEFAULT false,
    `isAppRatingSubmit` BOOLEAN NOT NULL DEFAULT false,
    `isAppFeedbackSubmit` BOOLEAN NOT NULL DEFAULT false,
    `mobileNumberHash` VARCHAR(65) NOT NULL DEFAULT '',
    `emailHash` VARCHAR(65) NOT NULL DEFAULT '',
    `appRating` INTEGER NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterRoleId` INTEGER NULL DEFAULT 1,
    `recruiterStatusId` INTEGER NULL,
    `hiringForId` INTEGER NULL DEFAULT 1,
    `designationId` INTEGER NULL DEFAULT 1,

    UNIQUE INDEX `recruiter_mobileNumber_key`(`mobileNumber`),
    UNIQUE INDEX `recruiter_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterdocument` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `aadhaarNumber` VARCHAR(255) NULL,
    `panNumber` VARCHAR(255) NULL,
    `gstin` VARCHAR(255) NULL,
    `fssai` VARCHAR(255) NULL,
    `udyogAadhaar` VARCHAR(255) NULL,
    `aadhaarImgUrl` VARCHAR(255) NULL,
    `panImgUrl` VARCHAR(255) NULL,
    `gstinImgUrl` VARCHAR(255) NULL,
    `fssaiImgUrl` VARCHAR(255) NULL,
    `udyogAadhaarImgUrl` VARCHAR(255) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterId` INTEGER NOT NULL,
    `personalstatusId` INTEGER NULL DEFAULT 1,
    `companystatusId` INTEGER NULL DEFAULT 1,
    `personalDocGivenId` INTEGER NULL,
    `companyDocGivenId` INTEGER NULL,
    `personalRejectReason` VARCHAR(255) NULL,
    `companyRejectReason` VARCHAR(255) NULL,

    UNIQUE INDEX `recruiterdocument_recruiterId_key`(`recruiterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterOTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterEmailOTP` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `otp` VARCHAR(6) NOT NULL DEFAULT '',
    `expirationTime` DATETIME(3) NOT NULL,
    `verified` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Verificationstatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `verificationdocs` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(35) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Designation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `HiringFor` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterrole` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `CompanyList` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `company` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `industryId` INTEGER NULL,
    `numberOfEmployeesId` INTEGER NULL,
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    `companyTypeId` INTEGER NULL,
    `isCompanyVerified` BOOLEAN NOT NULL DEFAULT false,
    `isDeleted` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `recruiterId` INTEGER NOT NULL,
    `companyListId` INTEGER NULL,

    UNIQUE INDEX `company_recruiterId_key`(`recruiterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `consultantcompanymapping` (
    `consultantId` INTEGER NOT NULL,
    `clientCompanyId` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`consultantId`, `clientCompanyId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `clientcompany` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `industryId` INTEGER NULL,
    `numberOfEmployeesId` INTEGER NULL,
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    `isCompanyVerified` BOOLEAN NOT NULL DEFAULT false,
    `isDeleted` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `companyListId` INTEGER NULL,
    `recruiterId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Industry` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `NumberOfEmployees` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `job` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `companyName` VARCHAR(255) NOT NULL,
    `recruiterId` INTEGER NOT NULL,
    `companyId` INTEGER NOT NULL,
    `companyTypeId` INTEGER NULL,
    `companyListId` INTEGER NOT NULL,
    `consultantId` INTEGER NULL,
    `jobStatusId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterjobpostdetail` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `companyName` VARCHAR(255) NOT NULL,
    `jobTitle` VARCHAR(255) NOT NULL DEFAULT '',
    `jobCategoryId` INTEGER NOT NULL,
    `jobLocation` VARCHAR(255) NOT NULL DEFAULT '',
    `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    `area` VARCHAR(100) NULL DEFAULT '',
    `city` VARCHAR(100) NULL DEFAULT '',
    `state` VARCHAR(55) NULL DEFAULT '',
    `zipcode` VARCHAR(20) NULL DEFAULT '',
    `latitude` VARCHAR(191) NOT NULL DEFAULT '',
    `longitude` VARCHAR(191) NOT NULL DEFAULT '',
    `numberOpenings` INTEGER NOT NULL DEFAULT 1,
    `workTypeId` INTEGER NOT NULL,
    `jobUrgencyId` INTEGER NOT NULL,
    `jobDescription` TEXT NOT NULL,
    `minMonthSalary` INTEGER NOT NULL DEFAULT 0,
    `maxMonthSalary` INTEGER NULL DEFAULT 0,
    `isIncentives` BOOLEAN NOT NULL DEFAULT false,
    `avgMonthlyIncentive` INTEGER NULL DEFAULT 0,
    `workingDaysId` INTEGER NOT NULL,
    `feesDeposit` BOOLEAN NOT NULL DEFAULT false,
    `depositAmount` INTEGER NULL,
    `depositReasonId` INTEGER NULL,
    `additionalInformation` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `companyId` INTEGER NOT NULL,
    `shiftTypeId` INTEGER NOT NULL,
    `employmentTypeId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,
    `jobTitleId` INTEGER NULL,

    UNIQUE INDEX `recruiterjobpostdetail_jobId_key`(`jobId`),
    FULLTEXT INDEX `recruiterjobpostdetail_jobTitle_companyName_jobDescription_j_idx`(`jobTitle`, `companyName`, `jobDescription`, `jobLocation`, `buildingName`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruitercandidatepreference` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `genderId` INTEGER NOT NULL,
    `educationId` INTEGER NOT NULL,
    `workExperienceId` INTEGER NOT NULL,
    `minExperience` INTEGER NOT NULL DEFAULT 0,
    `maxExperience` INTEGER NULL DEFAULT 0,
    `applicationRadiusId` INTEGER NOT NULL,
    `englishKnowledgeId` INTEGER NOT NULL,
    `isAssetsRequired` BOOLEAN NOT NULL DEFAULT false,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `jobId` INTEGER NOT NULL,

    UNIQUE INDEX `recruitercandidatepreference_jobId_key`(`jobId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `interview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `hrName` VARCHAR(100) NOT NULL DEFAULT '',
    `hrContactNumber` VARCHAR(255) NOT NULL DEFAULT '',
    `hrContactNumberHash` VARCHAR(65) NOT NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `interviewTypeId` INTEGER NOT NULL,
    `jobId` INTEGER NOT NULL,

    UNIQUE INDEX `interview_jobId_key`(`jobId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobStatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobCategory` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(55) NOT NULL DEFAULT '',
    `name_en` VARCHAR(55) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(55) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobBenefits` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DepositReason` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `JobBenefitsMapping` (
    `recruiterJobPostDetailId` INTEGER NOT NULL,
    `jobBenefitsId` INTEGER NOT NULL,

    PRIMARY KEY (`jobBenefitsId`, `recruiterJobPostDetailId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Gender` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `name_en` VARCHAR(20) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `WorkExperience` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(25) NOT NULL DEFAULT '',
    `name_en` VARCHAR(25) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(25) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecruiterSkillsMapping` (
    `recruiterCandidatePreferenceId` INTEGER NOT NULL,
    `skillId` INTEGER NOT NULL,

    PRIMARY KEY (`recruiterCandidatePreferenceId`, `skillId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RequiredAssets` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(55) NOT NULL DEFAULT '',
    `name_en` VARCHAR(55) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(55) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RequiredAssetsMapping` (
    `requiredAssetsId` INTEGER NOT NULL,
    `recruiterCandidatePreferenceId` INTEGER NOT NULL,

    PRIMARY KEY (`recruiterCandidatePreferenceId`, `requiredAssetsId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `interviewtype` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(55) NOT NULL DEFAULT '',
    `name_en` VARCHAR(55) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(55) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ffinterview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    `fullAddress` VARCHAR(255) NOT NULL DEFAULT '',
    `interviewId` INTEGER NOT NULL,
    `latitude` VARCHAR(20) NOT NULL DEFAULT '',
    `longitude` VARCHAR(20) NOT NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ffinterview_interviewId_key`(`interviewId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `walkininterview` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `walkinStartDate` DATE NOT NULL,
    `duration` INTEGER NOT NULL DEFAULT 0,
    `startTime` VARCHAR(50) NOT NULL DEFAULT '',
    `endTime` VARCHAR(50) NOT NULL DEFAULT '',
    `location` VARCHAR(255) NOT NULL DEFAULT '',
    `buildingName` VARCHAR(255) NOT NULL DEFAULT '',
    `interviewId` INTEGER NOT NULL,
    `latitude` VARCHAR(20) NOT NULL DEFAULT '',
    `longitude` VARCHAR(20) NOT NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `walkininterview_interviewId_key`(`interviewId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterappfeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL DEFAULT '',
    `name_en` VARCHAR(255) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(255) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterappfeedbackmapping` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `recruiterappFeedbackId` INTEGER NOT NULL,
    `recruiterappFeedbackOtherReason` VARCHAR(255) NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `recruiterappfeedbackmapping_recruiterId_key`(`recruiterId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `crmuser` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `mobileNumber` VARCHAR(255) NULL,
    `password` VARCHAR(255) NOT NULL,
    `roleId` INTEGER NOT NULL DEFAULT 2,
    `emailHash` VARCHAR(255) NULL,
    `mobileHash` VARCHAR(255) NULL,

    UNIQUE INDEX `crmuser_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `crmuserrole` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterrequest` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recruiterId` INTEGER NOT NULL,
    `crmUserId` INTEGER NULL,
    `requestCallBackStatusId` INTEGER NULL DEFAULT 1,
    `recruiterRequestFeedbackId` INTEGER NULL,
    `requestCallBackOtherReason` VARCHAR(255) NULL DEFAULT '',
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `requestcallbackstatus` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(20) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `recruiterrequestfeedback` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL DEFAULT '',
    `name_en` VARCHAR(200) NOT NULL DEFAULT '',
    `name_ta` VARCHAR(200) NOT NULL DEFAULT '',
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_languageId_fkey` FOREIGN KEY (`languageId`) REFERENCES `AppLanguage`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_userStatusId_fkey` FOREIGN KEY (`userStatusId`) REFERENCES `UserStatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_jobzAppStatusId_fkey` FOREIGN KEY (`jobzAppStatusId`) REFERENCES `JobzAppStatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_deleteFeedbackId_fkey` FOREIGN KEY (`deleteFeedbackId`) REFERENCES `DeleteFeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `usernotification` ADD CONSTRAINT `usernotification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `usernotification` ADD CONSTRAINT `usernotification_usernotificationtypeId_fkey` FOREIGN KEY (`usernotificationtypeId`) REFERENCES `usernotificationtype`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `fcmdevicetoken` ADD CONSTRAINT `fcmdevicetoken_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_qualificationId_fkey` FOREIGN KEY (`qualificationId`) REFERENCES `QualificationType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_courseId_fkey` FOREIGN KEY (`courseId`) REFERENCES `Course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_specializationId_fkey` FOREIGN KEY (`specializationId`) REFERENCES `Specialization`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_degreeModeId_fkey` FOREIGN KEY (`degreeModeId`) REFERENCES `DegreeMode`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserEducation` ADD CONSTRAINT `UserEducation_collegesId_fkey` FOREIGN KEY (`collegesId`) REFERENCES `Colleges`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Course` ADD CONSTRAINT `Course_qualificationId_fkey` FOREIGN KEY (`qualificationId`) REFERENCES `QualificationType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Specialization` ADD CONSTRAINT `Specialization_courseId_fkey` FOREIGN KEY (`courseId`) REFERENCES `Course`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_joinPreferenceId_fkey` FOREIGN KEY (`joinPreferenceId`) REFERENCES `JoinPreference`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_currentRoleCategoryId_fkey` FOREIGN KEY (`currentRoleCategoryId`) REFERENCES `JobRoleCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_currentJobRoleId_fkey` FOREIGN KEY (`currentJobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserProfessional` ADD CONSTRAINT `UserProfessional_englishKnowledgeId_fkey` FOREIGN KEY (`englishKnowledgeId`) REFERENCES `EnglishKnowledgeLevel`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `jobtitle` ADD CONSTRAINT `jobtitle_jobRoleId_fkey` FOREIGN KEY (`jobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LanguageMapping` ADD CONSTRAINT `LanguageMapping_userLanguageId_fkey` FOREIGN KEY (`userLanguageId`) REFERENCES `UserProfessional`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LanguageMapping` ADD CONSTRAINT `LanguageMapping_profileLanguageId_fkey` FOREIGN KEY (`profileLanguageId`) REFERENCES `ProfileLanguages`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SkillsMapping` ADD CONSTRAINT `SkillsMapping_userProfessionalId_fkey` FOREIGN KEY (`userProfessionalId`) REFERENCES `UserProfessional`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SkillsMapping` ADD CONSTRAINT `SkillsMapping_skillId_fkey` FOREIGN KEY (`skillId`) REFERENCES `skilllist`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `skilllist` ADD CONSTRAINT `skilllist_jobRoleId_fkey` FOREIGN KEY (`jobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDomainMapping` ADD CONSTRAINT `WorkingDomainMapping_userProfessionalId_fkey` FOREIGN KEY (`userProfessionalId`) REFERENCES `UserProfessional`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDomainMapping` ADD CONSTRAINT `WorkingDomainMapping_WorkingDomainId_fkey` FOREIGN KEY (`WorkingDomainId`) REFERENCES `WorkingDomainList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_prefRoleCategoryId_fkey` FOREIGN KEY (`prefRoleCategoryId`) REFERENCES `JobRoleCategory`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_prefJobRoleId_fkey` FOREIGN KEY (`prefJobRoleId`) REFERENCES `JobRole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_distanceId_fkey` FOREIGN KEY (`distanceId`) REFERENCES `Distance`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserJobPreferences` ADD CONSTRAINT `UserJobPreferences_salaryRangeId_fkey` FOREIGN KEY (`salaryRangeId`) REFERENCES `monthlysalaryrange`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `JobRole` ADD CONSTRAINT `JobRole_jobRoleCategoryId_fkey` FOREIGN KEY (`jobRoleCategoryId`) REFERENCES `JobRoleCategory`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CommuteModeMapping` ADD CONSTRAINT `CommuteModeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `CommuteModeMapping` ADD CONSTRAINT `CommuteModeMapping_commuteModeId_fkey` FOREIGN KEY (`commuteModeId`) REFERENCES `CommuteMode`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EmploymentTypeMapping` ADD CONSTRAINT `EmploymentTypeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `EmploymentTypeMapping` ADD CONSTRAINT `EmploymentTypeMapping_employmentTypeId_fkey` FOREIGN KEY (`employmentTypeId`) REFERENCES `EmploymentType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkTypeMapping` ADD CONSTRAINT `WorkTypeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkTypeMapping` ADD CONSTRAINT `WorkTypeMapping_workTypeId_fkey` FOREIGN KEY (`workTypeId`) REFERENCES `WorkType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShiftTypeMapping` ADD CONSTRAINT `ShiftTypeMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ShiftTypeMapping` ADD CONSTRAINT `ShiftTypeMapping_shiftTypeId_fkey` FOREIGN KEY (`shiftTypeId`) REFERENCES `ShiftType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDaysMapping` ADD CONSTRAINT `WorkingDaysMapping_userJobPreferenceId_fkey` FOREIGN KEY (`userJobPreferenceId`) REFERENCES `UserJobPreferences`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `WorkingDaysMapping` ADD CONSTRAINT `WorkingDaysMapping_workingDaysId_fkey` FOREIGN KEY (`workingDaysId`) REFERENCES `WorkingDays`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFeedback` ADD CONSTRAINT `UserFeedback_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFeedback` ADD CONSTRAINT `UserFeedback_reviewId_fkey` FOREIGN KEY (`reviewId`) REFERENCES `UserReviewFeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `UserFeedback` ADD CONSTRAINT `UserFeedback_suggestionId_fkey` FOREIGN KEY (`suggestionId`) REFERENCES `SuggestionFeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userreportjob` ADD CONSTRAINT `userreportjob_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userreportjob` ADD CONSTRAINT `userreportjob_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userreportjob` ADD CONSTRAINT `userreportjob_reportJobId_fkey` FOREIGN KEY (`reportJobId`) REFERENCES `ReportJob`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobmapping` ADD CONSTRAINT `userjobmapping_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobmapping` ADD CONSTRAINT `userjobmapping_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobmapping` ADD CONSTRAINT `userjobmapping_jobFeedbackId_fkey` FOREIGN KEY (`jobFeedbackId`) REFERENCES `jobfeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_candidateProfileStatusId_fkey` FOREIGN KEY (`candidateProfileStatusId`) REFERENCES `candidateprofilestatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiteruserdetails` ADD CONSTRAINT `recruiteruserdetails_recruiterReportCandidateId_fkey` FOREIGN KEY (`recruiterReportCandidateId`) REFERENCES `recruiterreportcandidate`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobviewmapping` ADD CONSTRAINT `userjobviewmapping_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `userjobviewmapping` ADD CONSTRAINT `userjobviewmapping_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_recruiterRoleId_fkey` FOREIGN KEY (`recruiterRoleId`) REFERENCES `recruiterrole`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_recruiterStatusId_fkey` FOREIGN KEY (`recruiterStatusId`) REFERENCES `RecruiterStatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_hiringForId_fkey` FOREIGN KEY (`hiringForId`) REFERENCES `HiringFor`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiter` ADD CONSTRAINT `recruiter_designationId_fkey` FOREIGN KEY (`designationId`) REFERENCES `Designation`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_personalstatusId_fkey` FOREIGN KEY (`personalstatusId`) REFERENCES `Verificationstatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_companystatusId_fkey` FOREIGN KEY (`companystatusId`) REFERENCES `Verificationstatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_personalDocGivenId_fkey` FOREIGN KEY (`personalDocGivenId`) REFERENCES `verificationdocs`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterdocument` ADD CONSTRAINT `recruiterdocument_companyDocGivenId_fkey` FOREIGN KEY (`companyDocGivenId`) REFERENCES `verificationdocs`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_industryId_fkey` FOREIGN KEY (`industryId`) REFERENCES `Industry`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_numberOfEmployeesId_fkey` FOREIGN KEY (`numberOfEmployeesId`) REFERENCES `NumberOfEmployees`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_companyTypeId_fkey` FOREIGN KEY (`companyTypeId`) REFERENCES `HiringFor`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `company` ADD CONSTRAINT `company_companyListId_fkey` FOREIGN KEY (`companyListId`) REFERENCES `CompanyList`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `consultantcompanymapping` ADD CONSTRAINT `consultantcompanymapping_consultantId_fkey` FOREIGN KEY (`consultantId`) REFERENCES `company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `consultantcompanymapping` ADD CONSTRAINT `consultantcompanymapping_clientCompanyId_fkey` FOREIGN KEY (`clientCompanyId`) REFERENCES `clientcompany`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_industryId_fkey` FOREIGN KEY (`industryId`) REFERENCES `Industry`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_numberOfEmployeesId_fkey` FOREIGN KEY (`numberOfEmployeesId`) REFERENCES `NumberOfEmployees`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_companyListId_fkey` FOREIGN KEY (`companyListId`) REFERENCES `CompanyList`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `clientcompany` ADD CONSTRAINT `clientcompany_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_companyId_fkey` FOREIGN KEY (`companyId`) REFERENCES `company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_companyTypeId_fkey` FOREIGN KEY (`companyTypeId`) REFERENCES `HiringFor`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_companyListId_fkey` FOREIGN KEY (`companyListId`) REFERENCES `CompanyList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_consultantId_fkey` FOREIGN KEY (`consultantId`) REFERENCES `clientcompany`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `job` ADD CONSTRAINT `job_jobStatusId_fkey` FOREIGN KEY (`jobStatusId`) REFERENCES `JobStatus`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobCategoryId_fkey` FOREIGN KEY (`jobCategoryId`) REFERENCES `JobRole`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_workTypeId_fkey` FOREIGN KEY (`workTypeId`) REFERENCES `WorkType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobUrgencyId_fkey` FOREIGN KEY (`jobUrgencyId`) REFERENCES `JoinPreference`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_workingDaysId_fkey` FOREIGN KEY (`workingDaysId`) REFERENCES `WorkingDays`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_depositReasonId_fkey` FOREIGN KEY (`depositReasonId`) REFERENCES `DepositReason`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_companyId_fkey` FOREIGN KEY (`companyId`) REFERENCES `company`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_shiftTypeId_fkey` FOREIGN KEY (`shiftTypeId`) REFERENCES `ShiftType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_employmentTypeId_fkey` FOREIGN KEY (`employmentTypeId`) REFERENCES `EmploymentType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterjobpostdetail` ADD CONSTRAINT `recruiterjobpostdetail_jobTitleId_fkey` FOREIGN KEY (`jobTitleId`) REFERENCES `jobtitle`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_genderId_fkey` FOREIGN KEY (`genderId`) REFERENCES `Gender`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_educationId_fkey` FOREIGN KEY (`educationId`) REFERENCES `QualificationType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_workExperienceId_fkey` FOREIGN KEY (`workExperienceId`) REFERENCES `WorkExperience`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_applicationRadiusId_fkey` FOREIGN KEY (`applicationRadiusId`) REFERENCES `Distance`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_englishKnowledgeId_fkey` FOREIGN KEY (`englishKnowledgeId`) REFERENCES `EnglishKnowledgeLevel`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruitercandidatepreference` ADD CONSTRAINT `recruitercandidatepreference_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `interview` ADD CONSTRAINT `interview_interviewTypeId_fkey` FOREIGN KEY (`interviewTypeId`) REFERENCES `interviewtype`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `interview` ADD CONSTRAINT `interview_jobId_fkey` FOREIGN KEY (`jobId`) REFERENCES `job`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `JobBenefitsMapping` ADD CONSTRAINT `JobBenefitsMapping_recruiterJobPostDetailId_fkey` FOREIGN KEY (`recruiterJobPostDetailId`) REFERENCES `recruiterjobpostdetail`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `JobBenefitsMapping` ADD CONSTRAINT `JobBenefitsMapping_jobBenefitsId_fkey` FOREIGN KEY (`jobBenefitsId`) REFERENCES `JobBenefits`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterSkillsMapping` ADD CONSTRAINT `RecruiterSkillsMapping_recruiterCandidatePreferenceId_fkey` FOREIGN KEY (`recruiterCandidatePreferenceId`) REFERENCES `recruitercandidatepreference`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecruiterSkillsMapping` ADD CONSTRAINT `RecruiterSkillsMapping_skillId_fkey` FOREIGN KEY (`skillId`) REFERENCES `skilllist`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RequiredAssetsMapping` ADD CONSTRAINT `RequiredAssetsMapping_requiredAssetsId_fkey` FOREIGN KEY (`requiredAssetsId`) REFERENCES `RequiredAssets`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RequiredAssetsMapping` ADD CONSTRAINT `RequiredAssetsMapping_recruiterCandidatePreferenceId_fkey` FOREIGN KEY (`recruiterCandidatePreferenceId`) REFERENCES `recruitercandidatepreference`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ffinterview` ADD CONSTRAINT `ffinterview_interviewId_fkey` FOREIGN KEY (`interviewId`) REFERENCES `interview`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `walkininterview` ADD CONSTRAINT `walkininterview_interviewId_fkey` FOREIGN KEY (`interviewId`) REFERENCES `interview`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterappfeedbackmapping` ADD CONSTRAINT `recruiterappfeedbackmapping_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterappfeedbackmapping` ADD CONSTRAINT `recruiterappfeedbackmapping_recruiterappFeedbackId_fkey` FOREIGN KEY (`recruiterappFeedbackId`) REFERENCES `recruiterappfeedback`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `crmuser` ADD CONSTRAINT `crmuser_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `crmuserrole`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_requestCallBackStatusId_fkey` FOREIGN KEY (`requestCallBackStatusId`) REFERENCES `requestcallbackstatus`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_recruiterRequestFeedbackId_fkey` FOREIGN KEY (`recruiterRequestFeedbackId`) REFERENCES `recruiterrequestfeedback`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_recruiterId_fkey` FOREIGN KEY (`recruiterId`) REFERENCES `recruiter`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `recruiterrequest` ADD CONSTRAINT `recruiterrequest_crmUserId_fkey` FOREIGN KEY (`crmUserId`) REFERENCES `crmuser`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
